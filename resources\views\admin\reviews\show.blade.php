@extends('layouts.admin')

@section('title', 'Review Details')

@section('styles')
<style>
    .review-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .review-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        padding: 20px;
    }

    .review-body {
        padding: 30px;
    }

    .review-footer {
        background-color: #f8f9fa;
        padding: 20px;
        border-top: 1px solid #eee;
    }

    .star-rating {
        color: #ffc107;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .star-rating .far {
        color: #e0e0e0;
    }

    .booking-info-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .booking-info-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .booking-info-body {
        padding: 20px;
    }

    .booking-detail {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .booking-detail:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .booking-detail-label {
        font-weight: 600;
        color: #495057;
    }

    .vehicle-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .vehicle-image-placeholder {
        width: 100%;
        height: 150px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .vehicle-image-placeholder i {
        font-size: 3rem;
        color: #adb5bd;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-star me-2"></i> Review Details</h2>
        <div>
            <a href="{{ route('admin.reviews.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Reviews
            </a>
        </div>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <!-- Review Details -->
        <div class="col-md-8">
            <div class="review-card">
                <div class="review-header">
                    <h4 class="mb-2">Review for Booking #{{ $booking->booking_number }}</h4>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0">By {{ $booking->user->name }} on {{ $booking->reviewed_at->format('F d, Y h:i A') }}</p>
                        </div>
                        <div class="star-rating">
                            @for ($i = 1; $i <= 5; $i++)
                                <i class="{{ $i <= $booking->rating ? 'fas' : 'far' }} fa-star"></i>
                            @endfor
                        </div>
                    </div>
                </div>
                <div class="review-body">
                    <h5 class="mb-3">Review Content</h5>
                    <div class="review-content p-4 bg-light rounded">
                        <p class="mb-0">{{ $booking->review }}</p>
                    </div>
                </div>
                <div class="review-footer d-flex justify-content-between align-items-center">
                    <div>
                        <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-info">
                            <i class="fas fa-calendar-check me-2"></i> View Booking Details
                        </a>
                    </div>
                    <div>
                        <a href="{{ route('admin.reviews.edit', $booking->id) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-2"></i> Edit Review
                        </a>
                        <form action="{{ route('admin.reviews.destroy', $booking->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this review?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i> Delete Review
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Information -->
        <div class="col-md-4">
            <div class="booking-info-card">
                <div class="booking-info-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Information</h5>
                </div>
                <div class="booking-info-body">
                    <!-- Vehicle Information -->
                    <div class="text-center mb-4">
                        @if($booking->vehicle->image)
                            <img src="{{ asset('storage/' . $booking->vehicle->image) }}" class="vehicle-image" alt="{{ $booking->vehicle->name }}">
                        @else
                            <div class="vehicle-image-placeholder">
                                <i class="fas fa-car"></i>
                            </div>
                        @endif
                        <h5>{{ $booking->vehicle->name }}</h5>
                        <p class="text-muted">{{ $booking->vehicle->type }}</p>
                    </div>

                    <!-- Booking Details -->
                    <div class="booking-detail">
                        <div class="booking-detail-label">Booking Type</div>
                        <div>{{ ucfirst(str_replace('_', ' ', $booking->booking_type)) }}</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Pickup Address</div>
                        <div>{{ $booking->pickup_address }}</div>
                    </div>

                    @if($booking->via_stops && count($booking->via_stops) > 0)
                        <div class="booking-detail">
                            <div class="booking-detail-label">Via Stops</div>
                            <div>
                                @foreach($booking->via_stops as $index => $viaStop)
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-primary me-2">{{ $index + 1 }}</span>
                                        <small>{{ $viaStop['address'] ?? 'Address not specified' }}</small>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="booking-detail">
                        <div class="booking-detail-label">Dropoff Address</div>
                        <div>{{ $booking->dropoff_address }}</div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Pickup Date & Time</div>
                        <div>{{ $booking->pickup_date->format('M d, Y h:i A') }}</div>
                    </div>

                    @if($booking->return_date)
                    <div class="booking-detail">
                        <div class="booking-detail-label">Return Date & Time</div>
                        <div>{{ $booking->return_date->format('M d, Y h:i A') }}</div>
                    </div>
                    @endif

                    <div class="booking-detail">
                        <div class="booking-detail-label">Status</div>
                        <div><span class="badge bg-{{ $booking->status === 'completed' ? 'success' : 'primary' }}">{{ ucfirst($booking->status) }}</span></div>
                    </div>

                    <div class="booking-detail">
                        <div class="booking-detail-label">Amount</div>
                        <div>{{ $currencySymbol ?? '£' }}{{ number_format($booking->amount, 2) }}</div>
                    </div>

                    @if($booking->driver)
                    <div class="booking-detail">
                        <div class="booking-detail-label">Driver</div>
                        <div>{{ $booking->driver->name }}</div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
