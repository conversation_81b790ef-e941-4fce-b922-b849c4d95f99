<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('type'); // booking, payment, notification, marketing, system
            $table->string('category')->nullable(); // client, driver, admin
            $table->string('subject');
            $table->longText('content');
            $table->json('variables')->nullable(); // Available variables for this template
            $table->json('preview_data')->nullable(); // Sample data for preview
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system')->default(false); // System templates cannot be deleted
            $table->timestamps();

            $table->index(['type', 'category']);
            $table->index(['is_active', 'is_system']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
