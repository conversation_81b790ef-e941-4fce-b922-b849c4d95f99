<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\EmailService;
use Carbon\Carbon;

class SendDriverDocumentExpiryReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drivers:document-expiry-reminders {--days=30 : Number of days before expiry to send reminders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send document expiry reminders to drivers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $this->info("Checking for driver documents expiring within {$days} days...");

        $drivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->get();

        $remindersSent = 0;

        foreach ($drivers as $driver) {
            $expiringDocuments = $this->getExpiringDocuments($driver, $days);

            if (!empty($expiringDocuments)) {
                try {
                    EmailService::sendDriverDocumentExpiry($driver, $expiringDocuments);
                    $remindersSent++;

                    $this->line("✅ Reminder sent to {$driver->name} ({$driver->email}) - " . count($expiringDocuments) . " documents expiring");
                } catch (\Exception $e) {
                    $this->error("❌ Failed to send reminder to {$driver->name}: " . $e->getMessage());
                }
            }
        }

        $this->info("📧 Document expiry reminders sent to {$remindersSent} drivers");
        return Command::SUCCESS;
    }

    /**
     * Get expiring documents for a driver
     */
    private function getExpiringDocuments($driver, $days)
    {
        $expiringDocuments = [];
        $cutoffDate = Carbon::now()->addDays($days);

        // Check license expiry
        if ($driver->license_expiry && Carbon::parse($driver->license_expiry)->lte($cutoffDate)) {
            $expiringDocuments[] = [
                'name' => 'Driving License',
                'expiry_date' => Carbon::parse($driver->license_expiry)->format('F j, Y'),
                'days_until_expiry' => Carbon::now()->diffInDays(Carbon::parse($driver->license_expiry), false),
            ];
        }

        // Check insurance expiry
        if ($driver->insurance_expiry && Carbon::parse($driver->insurance_expiry)->lte($cutoffDate)) {
            $expiringDocuments[] = [
                'name' => 'Vehicle Insurance',
                'expiry_date' => Carbon::parse($driver->insurance_expiry)->format('F j, Y'),
                'days_until_expiry' => Carbon::now()->diffInDays(Carbon::parse($driver->insurance_expiry), false),
            ];
        }

        // Check MOT expiry
        if ($driver->mot_expiry && Carbon::parse($driver->mot_expiry)->lte($cutoffDate)) {
            $expiringDocuments[] = [
                'name' => 'MOT Certificate',
                'expiry_date' => Carbon::parse($driver->mot_expiry)->format('F j, Y'),
                'days_until_expiry' => Carbon::now()->diffInDays(Carbon::parse($driver->mot_expiry), false),
            ];
        }

        return $expiringDocuments;
    }
}
