<?php

namespace App\Helpers;

class PrivacyHelper
{
    /**
     * Format a full name to show only first name and last initial for privacy
     *
     * @param string $fullName
     * @return string
     */
    public static function formatPrivateName(string $fullName): string
    {
        $nameParts = explode(' ', trim($fullName));
        
        if (count($nameParts) === 1) {
            // Only first name provided
            return $nameParts[0];
        }
        
        $firstName = $nameParts[0];
        $lastInitial = substr($nameParts[1] ?? '', 0, 1);
        
        return $firstName . ($lastInitial ? ' ' . $lastInitial . '.' : '');
    }

    /**
     * Mask email address for privacy (show first 3 chars and domain)
     *
     * @param string $email
     * @return string
     */
    public static function maskEmail(string $email): string
    {
        $parts = explode('@', $email);
        
        if (count($parts) !== 2) {
            return $email; // Invalid email format
        }
        
        $username = $parts[0];
        $domain = $parts[1];
        
        if (strlen($username) <= 3) {
            return str_repeat('*', strlen($username)) . '@' . $domain;
        }
        
        return substr($username, 0, 3) . str_repeat('*', strlen($username) - 3) . '@' . $domain;
    }

    /**
     * Mask phone number for privacy (show last 4 digits)
     *
     * @param string $phone
     * @return string
     */
    public static function maskPhone(string $phone): string
    {
        $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($cleanPhone) <= 4) {
            return str_repeat('*', strlen($cleanPhone));
        }
        
        return str_repeat('*', strlen($cleanPhone) - 4) . substr($cleanPhone, -4);
    }

    /**
     * Check if user should see full contact details based on role and context
     *
     * @param string $viewerRole
     * @param string $targetRole
     * @param string $context
     * @return bool
     */
    public static function canViewFullContactDetails(string $viewerRole, string $targetRole, string $context = 'general'): bool
    {
        // Admin can see all details
        if ($viewerRole === 'admin') {
            return true;
        }
        
        // Driver can see client phone for pickup coordination
        if ($viewerRole === 'driver' && $targetRole === 'client' && in_array($context, ['booking', 'pickup'])) {
            return true;
        }
        
        // Client can see driver phone for pickup coordination
        if ($viewerRole === 'client' && $targetRole === 'driver' && in_array($context, ['booking', 'pickup'])) {
            return true;
        }
        
        // Default to privacy protection
        return false;
    }

    /**
     * Get appropriate contact information based on privacy rules
     *
     * @param object $user
     * @param string $viewerRole
     * @param string $context
     * @return array
     */
    public static function getContactInfo($user, string $viewerRole, string $context = 'general'): array
    {
        $canViewFull = self::canViewFullContactDetails($viewerRole, $user->role ?? 'client', $context);
        
        return [
            'name' => $canViewFull ? $user->name : self::formatPrivateName($user->name),
            'email' => $canViewFull ? $user->email : self::maskEmail($user->email),
            'phone' => $canViewFull ? $user->phone : self::maskPhone($user->phone ?? ''),
            'show_full_contact' => $canViewFull
        ];
    }
}
