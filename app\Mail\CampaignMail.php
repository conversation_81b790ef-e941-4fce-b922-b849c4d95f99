<?php

namespace App\Mail;

use App\Models\EmailCampaign;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CampaignMail extends Mailable
{
    use Queueable, SerializesModels;

    protected EmailCampaign $campaign;
    protected array $emailData;

    /**
     * Create a new message instance.
     */
    public function __construct(EmailCampaign $campaign, array $emailData)
    {
        $this->campaign = $campaign;
        $this->emailData = $emailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        // Replace variables in subject
        $subject = $this->campaign->subject;
        foreach ($this->emailData as $key => $value) {
            $subject = str_replace(['{' . $key . '}', '{{' . $key . '}}'], $value, $subject);
        }

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.campaign',
            with: [
                'campaign' => $this->campaign,
                'emailData' => $this->emailData,
                'content' => $this->renderContent(),
                'companyName' => $this->emailData['company_name'] ?? 'YNR Cars',
                'unsubscribeUrl' => $this->emailData['unsubscribe_url'] ?? '#',
                'trackingPixelUrl' => $this->emailData['tracking_pixel_url'] ?? '',
            ],
        );
    }

    /**
     * Render the email content with variables replaced
     */
    private function renderContent(): string
    {
        $content = $this->campaign->content;

        // Replace variables with actual data
        foreach ($this->emailData as $key => $value) {
            $content = str_replace(['{' . $key . '}', '{{' . $key . '}}'], $value, $content);
        }

        return $content;
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
