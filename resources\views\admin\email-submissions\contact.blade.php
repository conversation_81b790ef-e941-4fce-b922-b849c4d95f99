@extends('layouts.admin')

@section('title', 'Contact Form Submissions')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">📝 Contact Form Submissions</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.email-submissions.incoming') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-inbox"></i> Incoming Emails
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.email-submissions.contact') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">All Statuses</option>
                                        <option value="new" {{ request('status') === 'new' ? 'selected' : '' }}>New</option>
                                        <option value="emailed" {{ request('status') === 'emailed' ? 'selected' : '' }}>Emailed</option>
                                        <option value="responded" {{ request('status') === 'responded' ? 'selected' : '' }}>Responded</option>
                                        <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                                        <option value="spam_detected" {{ request('status') === 'spam_detected' ? 'selected' : '' }}>Spam</option>
                                    </select>
                                </div>

                                <div class="col-md-3">
                                    <label for="is_spam" class="form-label">Spam Filter</label>
                                    <select name="is_spam" id="is_spam" class="form-select">
                                        <option value="">All Submissions</option>
                                        <option value="0" {{ request('is_spam') === '0' ? 'selected' : '' }}>Not Spam</option>
                                        <option value="1" {{ request('is_spam') === '1' ? 'selected' : '' }}>Spam</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="search" class="form-label">Search</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control" placeholder="Name, email, subject, company..." value="{{ request('search') }}">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        @if(request()->hasAny(['status', 'is_spam', 'search']))
                                            <a href="{{ route('admin.email-submissions.contact') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $submissions->total() }}</h4>
                                            <p class="mb-0">Total Submissions</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-envelope fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $submissions->where('status', 'new')->count() }}</h4>
                                            <p class="mb-0">New</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-star fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $submissions->where('status', 'responded')->count() }}</h4>
                                            <p class="mb-0">Responded</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $submissions->where('is_spam', true)->count() }}</h4>
                                            <p class="mb-0">Spam Detected</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-shield-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Submissions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($submissions as $submission)
                                    <tr class="{{ $submission->is_spam ? 'table-warning' : '' }}">
                                        <td>
                                            <strong>{{ $submission->name }}</strong>
                                            @if($submission->company)
                                                <br><small class="text-muted">{{ $submission->company }}</small>
                                            @endif
                                            @if($submission->phone)
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> {{ $submission->phone }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="mailto:{{ $submission->email }}" class="text-decoration-none">
                                                {{ $submission->email }}
                                            </a>
                                            @if($submission->preferred_contact !== 'email')
                                                <br><small class="text-muted">Prefers: {{ ucfirst($submission->preferred_contact) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;" title="{{ $submission->subject }}">
                                                {{ $submission->subject }}
                                            </div>
                                            <small class="text-muted d-block">
                                                {{ Str::limit($submission->message, 80) }}
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge {{ $submission->status_badge_class }}">
                                                {{ ucfirst(str_replace('_', ' ', $submission->status)) }}
                                            </span>
                                            @if($submission->is_spam)
                                                <br><span class="badge bg-warning mt-1">SPAM</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $submission->priority === 'high' ? 'danger' : ($submission->priority === 'medium' ? 'warning' : 'secondary') }}">
                                                {{ ucfirst($submission->priority) }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                {{ $submission->submitted_at->format('d M Y H:i') }}
                                                <br>
                                                <span class="text-muted">{{ $submission->submitted_at->diffForHumans() }}</span>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('admin.email-submissions.show-contact', $submission) }}" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="mailto:{{ $submission->email }}?subject=Re: {{ urlencode($submission->subject) }}" class="btn btn-outline-success" title="Reply">
                                                    <i class="fas fa-reply"></i>
                                                </a>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $submission->id }}, 'responded')">Mark as Responded</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ $submission->id }}, 'closed')">Mark as Closed</a></li>
                                                        @if(!$submission->is_spam)
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-warning" href="#" onclick="markAsSpam({{ $submission->id }})">Mark as Spam</a></li>
                                                        @endif
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteSubmission({{ $submission->id }})">Delete</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p>No contact form submissions found</p>
                                                <small>Contact form submissions will appear here when clients submit the contact form</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($submissions->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $submissions->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function updateStatus(submissionId, status) {
    if (confirm(`Are you sure you want to mark this submission as ${status}?`)) {
        fetch(`/admin/email-submissions/contact/${submissionId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating status: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating status');
        });
    }
}

function markAsSpam(submissionId) {
    if (confirm('Are you sure you want to mark this submission as spam?')) {
        fetch(`/admin/email-submissions/contact/${submissionId}/spam`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error marking as spam: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error marking as spam');
        });
    }
}

function deleteSubmission(submissionId) {
    if (confirm('Are you sure you want to delete this submission? This action cannot be undone.')) {
        fetch(`/admin/email-submissions/contact/${submissionId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting submission: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting submission');
        });
    }
}
</script>
@endsection
