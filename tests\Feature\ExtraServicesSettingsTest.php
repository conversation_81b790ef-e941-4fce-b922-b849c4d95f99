<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExtraServicesSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
    }

    /** @test */
    public function admin_can_access_settings_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertSee('Extra Services Configuration');
    }

    /** @test */
    public function admin_can_update_extra_services_settings()
    {
        $settingsData = [
            // Extra Services Settings
            'meet_and_greet_fee' => '12.50',
            'child_seat_fee' => '8.00',
            'wheelchair_fee' => '15.00',
            'extra_luggage_fee' => '6.50',
            'meet_and_greet_enabled' => 'true',
            'child_seat_enabled' => 'true',
            'wheelchair_enabled' => 'true',
            'extra_luggage_enabled' => 'true',
            
            // Required company settings to pass validation
            'company_name' => 'Test Company',
            'company_email' => '<EMAIL>',
            'company_phone' => '+1234567890',
            'company_address' => '123 Test Street',
            'country_code' => 'US',
            'currency_code' => 'USD',
            'currency_symbol' => '$',
            'timezone' => 'UTC',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.settings.update'), $settingsData);

        $response->assertStatus(302); // Redirect after successful update

        // Debug: Check what's in the session
        if (!$response->getSession()->has('success')) {
            $errors = $response->getSession()->get('errors');
            if ($errors) {
                dump('Validation errors:', $errors->toArray());
            }
            dump('Session data:', $response->getSession()->all());
        }

        $response->assertSessionHas('success');

        // Verify settings were saved
        $this->assertDatabaseHas('settings', [
            'key' => 'meet_and_greet_fee',
            'value' => '12.50'
        ]);

        $this->assertDatabaseHas('settings', [
            'key' => 'child_seat_fee',
            'value' => '8.00'
        ]);

        $this->assertDatabaseHas('settings', [
            'key' => 'wheelchair_fee',
            'value' => '15.00'
        ]);

        $this->assertDatabaseHas('settings', [
            'key' => 'extra_luggage_fee',
            'value' => '6.50'
        ]);

        $this->assertDatabaseHas('settings', [
            'key' => 'meet_and_greet_enabled',
            'value' => 'true'
        ]);
    }

    /** @test */
    public function extra_services_settings_validation_works()
    {
        $invalidData = [
            'meet_and_greet_fee' => 'invalid', // Should be numeric
            'child_seat_fee' => '-5.00', // Should be positive (min:0)
            'wheelchair_fee' => '1000.00', // Should be max 999.99
            'extra_luggage_fee' => 'not_a_number', // Should be numeric
            
            // Required company settings
            'company_name' => 'Test Company',
            'company_email' => '<EMAIL>',
            'company_phone' => '+1234567890',
            'company_address' => '123 Test Street',
            'country_code' => 'US',
            'currency_code' => 'USD',
            'currency_symbol' => '$',
            'timezone' => 'UTC',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.settings.update'), $invalidData);

        $response->assertStatus(302); // Redirect back with errors
        $response->assertSessionHasErrors([
            'meet_and_greet_fee',
            'child_seat_fee', 
            'wheelchair_fee',
            'extra_luggage_fee'
        ]);
    }

    /** @test */
    public function extra_services_settings_are_displayed_correctly()
    {
        // Create some test settings
        Setting::create(['key' => 'meet_and_greet_fee', 'value' => '15.00', 'group' => 'extra_services']);
        Setting::create(['key' => 'child_seat_fee', 'value' => '10.00', 'group' => 'extra_services']);
        Setting::create(['key' => 'meet_and_greet_enabled', 'value' => 'true', 'group' => 'extra_services']);
        Setting::create(['key' => 'child_seat_enabled', 'value' => 'false', 'group' => 'extra_services']);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.settings.index'));

        $response->assertStatus(200);
        $response->assertSee('value="15.00"', false); // meet_and_greet_fee
        $response->assertSee('value="10.00"', false); // child_seat_fee
        $response->assertSee('checked', false); // meet_and_greet_enabled should be checked
    }
}
