<?php

namespace App\Services;

use App\Mail\BookingConfirmationMail;
use App\Mail\BookingReminderMail;
use App\Mail\PaymentReceiptMail;
use App\Mail\ContactFormMail;
use App\Mail\ContactConfirmationMail;

use App\Mail\DriverAssignmentMail;
use App\Mail\DriverWelcomeMail;
use App\Mail\DriverDocumentExpiryMail;
use App\Mail\DriverEarningsSummaryMail;
use App\Mail\AdminDailyReportMail;
use App\Mail\AdminSystemAlertMail;
use App\Mail\AdminRevenueReportMail;
use App\Mail\AdminDriverAlertMail;
use App\Mail\AdminBookingAlertMail;
use App\Mail\AdminNewBookingMail;
use App\Mail\ClientWelcomeMail;
use App\Mail\ClientBookingStatusMail;
use App\Mail\ClientFeedbackRequestMail;
use App\Mail\ClientLoyaltyRewardMail;
use App\Mail\ClientNewsletterMail;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailService
{
    /**
     * Send booking confirmation email
     *
     * @param Booking $booking
     * @return bool
     */
    public static function sendBookingConfirmation(Booking $booking): bool
    {
        try {
            Mail::to($booking->user->email)
                ->send(new BookingConfirmationMail($booking));

            // Log the sent email
            self::logSentEmail(
                'booking_confirmation',
                $booking->user->email,
                "Booking Confirmation - #{$booking->booking_number}",
                'Booking',
                $booking->id
            );

            Log::info('Booking confirmation email sent', [
                'booking_id' => $booking->id,
                'booking_number' => $booking->booking_number,
                'user_email' => $booking->user->email
            ]);

            return true;
        } catch (\Exception $e) {
            // Log the failed email
            self::logFailedEmail(
                'booking_confirmation',
                $booking->user->email,
                "Booking Confirmation - #{$booking->booking_number}",
                $e->getMessage(),
                'Booking',
                $booking->id
            );

            Log::error('Failed to send booking confirmation email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send booking reminder email
     *
     * @param Booking $booking
     * @param string $type (client|driver)
     * @return bool
     */
    public static function sendBookingReminder(Booking $booking, string $type = 'client'): bool
    {
        try {
            $recipient = $type === 'driver' ? $booking->driver : $booking->user;

            if (!$recipient) {
                Log::warning('No recipient found for booking reminder', [
                    'booking_id' => $booking->id,
                    'type' => $type
                ]);
                return false;
            }

            Mail::to($recipient->email)
                ->send(new BookingReminderMail($booking, $type));

            Log::info('Booking reminder email sent', [
                'booking_id' => $booking->id,
                'type' => $type,
                'recipient_email' => $recipient->email
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking reminder email', [
                'booking_id' => $booking->id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send payment receipt email
     *
     * @param Payment $payment
     * @return bool
     */
    public static function sendPaymentReceipt(Payment $payment): bool
    {
        try {
            Mail::to($payment->user->email)
                ->send(new PaymentReceiptMail($payment));

            Log::info('Payment receipt email sent', [
                'payment_id' => $payment->id,
                'booking_id' => $payment->booking_id,
                'user_email' => $payment->user->email,
                'amount' => $payment->amount
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send payment receipt email', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send contact form emails
     *
     * @param array $contactData
     * @return bool
     */
    public static function sendContactFormEmails(array $contactData): bool
    {
        try {
            $adminEmail = SettingsService::getCompanyEmail();

            // Send to admin
            Mail::to($adminEmail)
                ->send(new ContactFormMail($contactData));

            // Send confirmation to user
            Mail::to($contactData['email'])
                ->send(new ContactConfirmationMail($contactData));

            Log::info('Contact form emails sent', [
                'user_email' => $contactData['email'],
                'admin_email' => $adminEmail,
                'subject' => $contactData['subject']
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send contact form emails', [
                'error' => $e->getMessage(),
                'contact_data' => $contactData
            ]);

            return false;
        }
    }



    /**
     * Test email configuration
     *
     * @param string $testEmail
     * @return bool
     */
    public static function testEmailConfiguration(string $testEmail): bool
    {
        try {
            $testData = [
                'test_email' => $testEmail,
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'server' => request()->getHost(),
            ];

            Mail::to($testEmail)->send(new \App\Mail\TestMail($testData));

            // Log the sent email
            self::logSentEmail(
                'test_email',
                $testEmail,
                'Email Configuration Test',
                null,
                null
            );

            Log::info('Test email sent successfully', ['email' => $testEmail]);

            return true;
        } catch (\Exception $e) {
            // Log the failed email
            self::logFailedEmail(
                'test_email',
                $testEmail,
                'Email Configuration Test',
                $e->getMessage(),
                null,
                null
            );

            Log::error('Test email failed', [
                'email' => $testEmail,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get email queue status
     *
     * @return array
     */
    public static function getEmailQueueStatus(): array
    {
        try {
            // Get queue connection
            $connection = config('queue.default');
            $queueName = config('queue.connections.' . $connection . '.queue', 'default');

            // Basic queue information
            $status = [
                'connection' => $connection,
                'queue_name' => $queueName,
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'processed_jobs' => 0,
                'last_processed' => null,
                'status' => 'unknown',
                'worker_status' => 'unknown',
            ];

            // Try to get queue size (this depends on the queue driver)
            try {
                if ($connection === 'database') {
                    $status['pending_jobs'] = \DB::table('jobs')->where('queue', $queueName)->count();
                    $status['failed_jobs'] = \DB::table('failed_jobs')->count();

                    $lastJob = \DB::table('jobs')
                        ->where('queue', $queueName)
                        ->orderBy('created_at', 'desc')
                        ->first();

                    if ($lastJob) {
                        $status['last_processed'] = $lastJob->created_at;
                    }

                    // Check if there are recent jobs (worker activity)
                    $recentJobs = \DB::table('jobs')
                        ->where('queue', $queueName)
                        ->where('created_at', '>', now()->subMinutes(5))
                        ->count();

                    $status['worker_status'] = $recentJobs > 0 ? 'active' : 'idle';
                } elseif ($connection === 'redis') {
                    // For Redis queue, you'd use Redis commands
                    $status['status'] = 'redis_queue';
                } else {
                    $status['status'] = 'sync_queue';
                }

                $status['status'] = $status['pending_jobs'] > 0 ? 'processing' : 'idle';
            } catch (\Exception $e) {
                $status['status'] = 'error';
                $status['error'] = $e->getMessage();
            }

            return $status;
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
                'pending_jobs' => 0,
                'failed_jobs' => 0,
            ];
        }
    }

    /**
     * Send booking status update notification
     *
     * @param \App\Models\Booking $booking
     * @param string $statusType
     * @param array $additionalData
     * @return bool
     */
    public static function sendBookingStatusUpdate(Booking $booking, string $statusType, array $additionalData = []): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($booking->user, 'email_booking_updates')) {
                return false;
            }

            Mail::to($booking->user->email)
                ->send(new ClientBookingStatusMail($booking, $statusType, $additionalData));

            Log::info('Booking status update email sent', [
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'user_email' => $booking->user->email,
                'status_type' => $statusType,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send booking status update email: ' . $e->getMessage(), [
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'user_email' => $booking->user->email,
                'status_type' => $statusType,
            ]);

            return false;
        }
    }

    /**
     * Send driver assignment notification
     *
     * @param \App\Models\Booking $booking
     * @return bool
     */
    public static function sendDriverAssignment(Booking $booking): bool
    {
        try {
            if (!$booking->driver) {
                return false;
            }

            Mail::to($booking->driver->email)
                ->send(new DriverAssignmentMail($booking));

            Log::info('Driver assignment email sent', [
                'booking_id' => $booking->id,
                'driver_email' => $booking->driver->email,
                'booking_number' => $booking->booking_number,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send driver assignment email: ' . $e->getMessage(), [
                'booking_id' => $booking->id,
                'driver_email' => $booking->driver->email ?? 'N/A',
            ]);
            return false;
        }
    }

    /**
     * Send driver welcome email
     *
     * @param \App\Models\User $driver
     * @param string|null $password
     * @return bool
     */
    public static function sendDriverWelcome(User $driver, $password = null): bool
    {
        try {
            Mail::to($driver->email)
                ->send(new DriverWelcomeMail($driver, $password));

            Log::info('Driver welcome email sent', [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send driver welcome email: ' . $e->getMessage(), [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
            ]);
            return false;
        }
    }

    /**
     * Send driver document expiry notification
     *
     * @param \App\Models\User $driver
     * @param array $expiringDocuments
     * @return bool
     */
    public static function sendDriverDocumentExpiry(User $driver, array $expiringDocuments): bool
    {
        try {
            Mail::to($driver->email)
                ->send(new DriverDocumentExpiryMail($driver, $expiringDocuments));

            Log::info('Driver document expiry email sent', [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
                'documents_count' => count($expiringDocuments),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send driver document expiry email: ' . $e->getMessage(), [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
            ]);
            return false;
        }
    }

    /**
     * Send driver earnings summary
     *
     * @param \App\Models\User $driver
     * @param array $earningsData
     * @param string $period
     * @return bool
     */
    public static function sendDriverEarningsSummary(User $driver, array $earningsData, string $period = 'weekly'): bool
    {
        try {
            Mail::to($driver->email)
                ->send(new DriverEarningsSummaryMail($driver, $earningsData, $period));

            Log::info('Driver earnings summary email sent', [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
                'period' => $period,
                'earnings' => $earningsData['net_earnings'] ?? 0,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send driver earnings summary email: ' . $e->getMessage(), [
                'driver_id' => $driver->id,
                'driver_email' => $driver->email,
                'period' => $period,
            ]);
            return false;
        }
    }

    /**
     * Send daily business report to admin
     *
     * @param array $reportData
     * @param string|null $reportDate
     * @return bool
     */
    public static function sendAdminDailyReport(array $reportData, $reportDate = null): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for daily report');
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminDailyReportMail($reportData, $reportDate));
            }

            Log::info('Admin daily report sent', [
                'report_date' => $reportDate ?? now()->format('Y-m-d'),
                'admin_count' => count($adminEmails),
                'revenue' => $reportData['today_revenue'] ?? 0,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin daily report: ' . $e->getMessage(), [
                'report_date' => $reportDate ?? now()->format('Y-m-d'),
            ]);
            return false;
        }
    }

    /**
     * Send system alert to admin
     *
     * @param string $alertType
     * @param array $alertData
     * @param string $severity
     * @return bool
     */
    public static function sendAdminSystemAlert(string $alertType, array $alertData, string $severity = 'medium'): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for system alert', [
                    'alert_type' => $alertType,
                    'severity' => $severity,
                ]);
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminSystemAlertMail($alertType, $alertData, $severity));
            }

            Log::info('Admin system alert sent', [
                'alert_type' => $alertType,
                'severity' => $severity,
                'admin_count' => count($adminEmails),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin system alert: ' . $e->getMessage(), [
                'alert_type' => $alertType,
                'severity' => $severity,
            ]);
            return false;
        }
    }

    /**
     * Send revenue report to admin
     *
     * @param array $revenueData
     * @param string $period
     * @param string|null $periodLabel
     * @return bool
     */
    public static function sendAdminRevenueReport(array $revenueData, string $period = 'weekly', string $periodLabel = null): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for revenue report');
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminRevenueReportMail($revenueData, $period, $periodLabel));
            }

            Log::info('Admin revenue report sent', [
                'period' => $period,
                'admin_count' => count($adminEmails),
                'total_revenue' => $revenueData['total_revenue'] ?? 0,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin revenue report: ' . $e->getMessage(), [
                'period' => $period,
            ]);
            return false;
        }
    }

    /**
     * Send driver alert to admin
     *
     * @param string $alertType
     * @param array $driverData
     * @param array $alertData
     * @return bool
     */
    public static function sendAdminDriverAlert(string $alertType, array $driverData, array $alertData = []): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for driver alert', [
                    'alert_type' => $alertType,
                    'driver_id' => $driverData['id'] ?? 'N/A',
                ]);
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminDriverAlertMail($alertType, $driverData, $alertData));
            }

            Log::info('Admin driver alert sent', [
                'alert_type' => $alertType,
                'driver_id' => $driverData['id'] ?? 'N/A',
                'driver_name' => $driverData['name'] ?? 'N/A',
                'admin_count' => count($adminEmails),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin driver alert: ' . $e->getMessage(), [
                'alert_type' => $alertType,
                'driver_id' => $driverData['id'] ?? 'N/A',
            ]);
            return false;
        }
    }

    /**
     * Send new booking notification to admin
     *
     * @param \App\Models\Booking $booking
     * @return bool
     */
    public static function sendAdminNewBookingNotification(Booking $booking): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for new booking notification', [
                    'booking_id' => $booking->id,
                ]);
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminNewBookingMail($booking));
            }

            Log::info('Admin new booking notification sent', [
                'booking_id' => $booking->id,
                'booking_number' => $booking->booking_number,
                'admin_count' => count($adminEmails),
                'amount' => $booking->amount,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin new booking notification', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Send booking alert to admin
     *
     * @param \App\Models\Booking $booking
     * @param string $alertType
     * @param array $alertData
     * @return bool
     */
    public static function sendAdminBookingAlert(Booking $booking, string $alertType, array $alertData = []): bool
    {
        try {
            $adminEmails = self::getAdminEmails();

            if (empty($adminEmails)) {
                Log::warning('No admin emails configured for booking alert', [
                    'alert_type' => $alertType,
                    'booking_id' => $booking->id,
                ]);
                return false;
            }

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new AdminBookingAlertMail($booking, $alertType, $alertData));
            }

            Log::info('Admin booking alert sent', [
                'alert_type' => $alertType,
                'booking_id' => $booking->id,
                'booking_number' => $booking->booking_number,
                'admin_count' => count($adminEmails),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send admin booking alert: ' . $e->getMessage(), [
                'alert_type' => $alertType,
                'booking_id' => $booking->id,
            ]);
            return false;
        }
    }

    /**
     * Get admin email addresses
     *
     * @return array
     */
    private static function getAdminEmails(): array
    {
        // Get admin emails from settings or config
        $adminEmails = [];

        // Try to get from settings first
        $settingsEmails = SettingsService::get('admin_notification_emails', '');
        if (!empty($settingsEmails)) {
            $adminEmails = array_map('trim', explode(',', $settingsEmails));
        }

        // Fallback to config
        if (empty($adminEmails)) {
            $configEmails = config('mail.admin_emails', []);
            if (is_string($configEmails)) {
                $adminEmails = array_map('trim', explode(',', $configEmails));
            } elseif (is_array($configEmails)) {
                $adminEmails = $configEmails;
            }
        }

        // Final fallback to default admin email
        if (empty($adminEmails)) {
            $defaultEmail = config('mail.from.address');
            if ($defaultEmail) {
                $adminEmails = [$defaultEmail];
            }
        }

        // Filter out empty emails
        return array_filter($adminEmails, function($email) {
            return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
        });
    }

    /**
     * Send welcome email to client
     *
     * @param \App\Models\User $client
     * @param bool $isNewRegistration
     * @return bool
     */
    public static function sendClientWelcome(User $client, bool $isNewRegistration = true): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($client, 'email_booking_updates')) {
                return false;
            }

            Mail::to($client->email)
                ->send(new ClientWelcomeMail($client, $isNewRegistration));

            Log::info('Client welcome email sent', [
                'client_id' => $client->id,
                'client_email' => $client->email,
                'is_new_registration' => $isNewRegistration,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send client welcome email: ' . $e->getMessage(), [
                'client_id' => $client->id,
                'client_email' => $client->email,
            ]);
            return false;
        }
    }

    /**
     * Send booking status update to client
     *
     * @param \App\Models\Booking $booking
     * @param string $statusType
     * @param string|null $previousStatus
     * @return bool
     */
    public static function sendClientBookingStatus(Booking $booking, string $statusType, string $previousStatus = null): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($booking->user, 'email_booking_updates')) {
                return false;
            }

            Mail::to($booking->user->email)
                ->send(new ClientBookingStatusMail($booking, $statusType, $previousStatus));

            Log::info('Client booking status email sent', [
                'booking_id' => $booking->id,
                'client_email' => $booking->user->email,
                'status_type' => $statusType,
                'previous_status' => $previousStatus,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send client booking status email: ' . $e->getMessage(), [
                'booking_id' => $booking->id,
                'client_email' => $booking->user->email,
                'status_type' => $statusType,
            ]);
            return false;
        }
    }

    /**
     * Send feedback request to client
     *
     * @param \App\Models\Booking $booking
     * @return bool
     */
    public static function sendClientFeedbackRequest(Booking $booking): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($booking->user, 'email_booking_updates')) {
                return false;
            }

            Mail::to($booking->user->email)
                ->send(new ClientFeedbackRequestMail($booking));

            Log::info('Client feedback request email sent', [
                'booking_id' => $booking->id,
                'client_email' => $booking->user->email,
                'booking_number' => $booking->booking_number,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send client feedback request email: ' . $e->getMessage(), [
                'booking_id' => $booking->id,
                'client_email' => $booking->user->email,
            ]);
            return false;
        }
    }

    /**
     * Send loyalty reward to client
     *
     * @param \App\Models\User $client
     * @param string $rewardType
     * @param array $rewardData
     * @return bool
     */
    public static function sendClientLoyaltyReward(User $client, string $rewardType, array $rewardData): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($client, 'email_promotions')) {
                return false;
            }

            Mail::to($client->email)
                ->send(new ClientLoyaltyRewardMail($client, $rewardType, $rewardData));

            Log::info('Client loyalty reward email sent', [
                'client_id' => $client->id,
                'client_email' => $client->email,
                'reward_type' => $rewardType,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send client loyalty reward email: ' . $e->getMessage(), [
                'client_id' => $client->id,
                'client_email' => $client->email,
                'reward_type' => $rewardType,
            ]);
            return false;
        }
    }

    /**
     * Send newsletter to client
     *
     * @param \App\Models\User $client
     * @param array $newsletterData
     * @param string $newsletterType
     * @return bool
     */
    public static function sendClientNewsletter(User $client, array $newsletterData, string $newsletterType = 'general'): bool
    {
        try {
            // Check notification preferences
            if (!self::shouldSendEmail($client, 'email_promotions')) {
                return false;
            }

            Mail::to($client->email)
                ->send(new ClientNewsletterMail($newsletterData, $newsletterType));

            Log::info('Client newsletter email sent', [
                'client_id' => $client->id,
                'client_email' => $client->email,
                'newsletter_type' => $newsletterType,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send client newsletter email: ' . $e->getMessage(), [
                'client_id' => $client->id,
                'client_email' => $client->email,
                'newsletter_type' => $newsletterType,
            ]);
            return false;
        }
    }

    /**
     * Check if email should be sent based on user preferences
     *
     * @param \App\Models\User $user
     * @param string $emailType
     * @return bool
     */
    private static function shouldSendEmail(User $user, string $emailType): bool
    {
        // Get user's notification settings
        $notificationSettings = $user->notificationSettings;

        if (!$notificationSettings) {
            // If no settings exist, default to sending emails
            return true;
        }

        // Check the specific email type preference
        return $notificationSettings->{$emailType} ?? true;
    }

    /**
     * Log sent email to database
     *
     * @param string $type
     * @param string $toEmail
     * @param string $subject
     * @param string|null $relatedType
     * @param int|null $relatedId
     * @return void
     */
    private static function logSentEmail(string $type, string $toEmail, string $subject, ?string $relatedType = null, ?int $relatedId = null): void
    {
        try {
            \App\Models\EmailLog::create([
                'type' => $type,
                'direction' => 'sent',
                'from_email' => SettingsService::get('mail_from_address', '<EMAIL>'),
                'to_email' => $toEmail,
                'subject' => $subject,
                'status' => 'sent',
                'related_type' => $relatedType,
                'related_id' => $relatedId,
                'sent_at' => now(),
                'metadata' => json_encode([
                    'timestamp' => now()->toISOString(),
                    'server' => request()->server('SERVER_NAME'),
                ]),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log sent email', [
                'type' => $type,
                'to_email' => $toEmail,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Log failed email to database
     *
     * @param string $type
     * @param string $toEmail
     * @param string $subject
     * @param string $errorMessage
     * @param string|null $relatedType
     * @param int|null $relatedId
     * @return void
     */
    private static function logFailedEmail(string $type, string $toEmail, string $subject, string $errorMessage, ?string $relatedType = null, ?int $relatedId = null): void
    {
        try {
            \App\Models\EmailLog::create([
                'type' => $type,
                'direction' => 'sent',
                'from_email' => SettingsService::get('mail_from_address', '<EMAIL>'),
                'to_email' => $toEmail,
                'subject' => $subject,
                'status' => 'failed',
                'related_type' => $relatedType,
                'related_id' => $relatedId,
                'error_message' => $errorMessage,
                'metadata' => json_encode([
                    'timestamp' => now()->toISOString(),
                    'server' => request()->server('SERVER_NAME'),
                ]),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log failed email', [
                'type' => $type,
                'to_email' => $toEmail,
                'error' => $e->getMessage()
            ]);
        }
    }
}
