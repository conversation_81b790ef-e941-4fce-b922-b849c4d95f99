<?php

namespace App\Http\Controllers;

use App\Models\EmailCampaign;
use App\Models\EmailCampaignRecipient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EmailTrackingController extends Controller
{
    /**
     * Track email opens
     */
    public function trackOpen(Request $request, $campaignId, $recipientId, $token)
    {
        try {
            $campaign = EmailCampaign::findOrFail($campaignId);
            $recipient = EmailCampaignRecipient::where('campaign_id', $campaignId)
                ->where('id', $recipientId)
                ->firstOrFail();

            // Verify token
            $expectedToken = hash('sha256', $recipient->email . $campaign->id);
            if (!hash_equals($expectedToken, $token)) {
                abort(404);
            }

            // Mark as opened (only if not already opened)
            if (!$recipient->opened_at) {
                $recipient->markAsOpened();
                $campaign->increment('open_count');

                Log::info('Email opened', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'email' => $recipient->email,
                ]);
            }

            // Return a 1x1 transparent pixel
            return response(base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'))
                ->header('Content-Type', 'image/gif')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            Log::error('Email tracking error', [
                'campaign_id' => $campaignId,
                'recipient_id' => $recipientId,
                'error' => $e->getMessage(),
            ]);

            // Return a 1x1 transparent pixel even on error
            return response(base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'))
                ->header('Content-Type', 'image/gif');
        }
    }

    /**
     * Track email clicks
     */
    public function trackClick(Request $request, $campaignId, $recipientId, $token)
    {
        try {
            $campaign = EmailCampaign::findOrFail($campaignId);
            $recipient = EmailCampaignRecipient::where('campaign_id', $campaignId)
                ->where('id', $recipientId)
                ->firstOrFail();

            // Verify token
            $expectedToken = hash('sha256', $recipient->email . $campaign->id);
            if (!hash_equals($expectedToken, $token)) {
                abort(404);
            }

            // Get the target URL
            $targetUrl = $request->get('url');
            if (!$targetUrl) {
                abort(404);
            }

            // Mark as clicked (only if not already clicked)
            if (!$recipient->clicked_at) {
                $recipient->markAsClicked();
                $campaign->increment('click_count');

                Log::info('Email link clicked', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'email' => $recipient->email,
                    'target_url' => $targetUrl,
                ]);
            }

            // Redirect to the target URL
            return redirect($targetUrl);

        } catch (\Exception $e) {
            Log::error('Email click tracking error', [
                'campaign_id' => $campaignId,
                'recipient_id' => $recipientId,
                'error' => $e->getMessage(),
            ]);

            // Redirect to home page on error
            return redirect('/');
        }
    }

    /**
     * Handle email unsubscribe
     */
    public function unsubscribe(Request $request, $campaignId, $recipientId, $token)
    {
        try {
            $campaign = EmailCampaign::findOrFail($campaignId);
            $recipient = EmailCampaignRecipient::where('campaign_id', $campaignId)
                ->where('id', $recipientId)
                ->firstOrFail();

            // Verify token
            $expectedToken = hash('sha256', $recipient->email . $campaign->id);
            if (!hash_equals($expectedToken, $token)) {
                abort(404);
            }

            // Mark recipient as unsubscribed
            $recipient->markAsUnsubscribed();

            // Also update user's notification preferences if they exist
            if ($recipient->user) {
                $user = $recipient->user;
                
                // Create or update notification settings
                $notificationSettings = $user->notificationSettings ?? new \App\Models\NotificationSetting();
                $notificationSettings->user_id = $user->id;
                $notificationSettings->email_promotions = false;
                $notificationSettings->save();

                Log::info('User unsubscribed from email campaigns', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'campaign_id' => $campaign->id,
                ]);
            }

            return view('emails.unsubscribe-success', [
                'email' => $recipient->email,
                'campaignName' => $campaign->name,
            ]);

        } catch (\Exception $e) {
            Log::error('Email unsubscribe error', [
                'campaign_id' => $campaignId,
                'recipient_id' => $recipientId,
                'error' => $e->getMessage(),
            ]);

            return view('emails.unsubscribe-error', [
                'error' => 'An error occurred while processing your unsubscribe request.',
            ]);
        }
    }

    /**
     * Show unsubscribe confirmation page
     */
    public function showUnsubscribe(Request $request, $campaignId, $recipientId, $token)
    {
        try {
            $campaign = EmailCampaign::findOrFail($campaignId);
            $recipient = EmailCampaignRecipient::where('campaign_id', $campaignId)
                ->where('id', $recipientId)
                ->firstOrFail();

            // Verify token
            $expectedToken = hash('sha256', $recipient->email . $campaign->id);
            if (!hash_equals($expectedToken, $token)) {
                abort(404);
            }

            return view('emails.unsubscribe-confirm', [
                'email' => $recipient->email,
                'campaignName' => $campaign->name,
                'unsubscribeUrl' => route('email.unsubscribe.process', [
                    'campaign' => $campaignId,
                    'recipient' => $recipientId,
                    'token' => $token,
                ]),
            ]);

        } catch (\Exception $e) {
            Log::error('Email unsubscribe page error', [
                'campaign_id' => $campaignId,
                'recipient_id' => $recipientId,
                'error' => $e->getMessage(),
            ]);

            return view('emails.unsubscribe-error', [
                'error' => 'An error occurred while loading the unsubscribe page.',
            ]);
        }
    }

    /**
     * Process unsubscribe request
     */
    public function processUnsubscribe(Request $request, $campaignId, $recipientId, $token)
    {
        return $this->unsubscribe($request, $campaignId, $recipientId, $token);
    }

    /**
     * Handle email bounces (webhook endpoint)
     */
    public function handleBounce(Request $request)
    {
        try {
            // This would be called by email service providers like SendGrid, Mailgun, etc.
            $bounceData = $request->all();
            
            Log::info('Email bounce received', $bounceData);

            // Process bounce based on the email service provider format
            // This is a generic implementation - you'd need to customize based on your ESP
            
            $email = $bounceData['email'] ?? null;
            $reason = $bounceData['reason'] ?? 'Unknown bounce reason';

            if ($email) {
                // Find and mark recipients as bounced
                $recipients = EmailCampaignRecipient::where('email', $email)
                    ->where('status', 'sent')
                    ->get();

                foreach ($recipients as $recipient) {
                    $recipient->markAsBounced($reason);
                    $recipient->campaign->increment('bounce_count');
                }

                Log::info('Processed email bounce', [
                    'email' => $email,
                    'reason' => $reason,
                    'affected_recipients' => $recipients->count(),
                ]);
            }

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            Log::error('Email bounce processing error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle email delivery confirmations (webhook endpoint)
     */
    public function handleDelivery(Request $request)
    {
        try {
            $deliveryData = $request->all();
            
            Log::info('Email delivery confirmation received', $deliveryData);

            $email = $deliveryData['email'] ?? null;

            if ($email) {
                // Find and mark recipients as delivered
                $recipients = EmailCampaignRecipient::where('email', $email)
                    ->where('status', 'sent')
                    ->get();

                foreach ($recipients as $recipient) {
                    $recipient->markAsDelivered();
                    $recipient->campaign->increment('delivered_count');
                }

                Log::info('Processed email delivery confirmation', [
                    'email' => $email,
                    'affected_recipients' => $recipients->count(),
                ]);
            }

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            Log::error('Email delivery processing error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }
}
