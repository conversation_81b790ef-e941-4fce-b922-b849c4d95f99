<?php

namespace Database\Factories;

use App\Models\EmailTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    protected $model = EmailTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        
        return [
            'name' => $name,
            'slug' => \Illuminate\Support\Str::slug($name),
            'type' => $this->faker->randomElement(['booking', 'payment', 'notification', 'marketing', 'system']),
            'category' => $this->faker->randomElement(['client', 'driver', 'admin']),
            'subject' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(3, true),
            'variables' => ['client_name', 'booking_number', 'company_name'],
            'preview_data' => [
                'client_name' => '<PERSON> Do<PERSON>',
                'booking_number' => 'YNR20240101001',
                'company_name' => 'YNR Cars',
            ],
            'description' => $this->faker->sentence(),
            'is_active' => true,
            'is_system' => false,
        ];
    }

    /**
     * Indicate that the template is a system template.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_system' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a booking confirmation template.
     */
    public function bookingConfirmation(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Booking Confirmation',
            'slug' => 'booking_confirmation',
            'type' => 'booking',
            'category' => 'client',
            'subject' => 'Booking Confirmation - #{booking_number}',
            'content' => 'Dear {client_name}, your booking #{booking_number} has been confirmed.',
            'variables' => ['client_name', 'booking_number', 'pickup_date', 'pickup_address'],
            'is_system' => true,
        ]);
    }

    /**
     * Create a payment receipt template.
     */
    public function paymentReceipt(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Payment Receipt',
            'slug' => 'payment_receipt',
            'type' => 'payment',
            'category' => 'client',
            'subject' => 'Payment Receipt - #{booking_number}',
            'content' => 'Dear {client_name}, thank you for your payment of {amount}.',
            'variables' => ['client_name', 'booking_number', 'amount'],
            'is_system' => true,
        ]);
    }

    /**
     * Create a welcome client template.
     */
    public function welcomeClient(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Welcome Client',
            'slug' => 'welcome_client',
            'type' => 'notification',
            'category' => 'client',
            'subject' => 'Welcome to {company_name}',
            'content' => 'Dear {client_name}, welcome to our transportation service!',
            'variables' => ['client_name', 'company_name'],
            'is_system' => true,
        ]);
    }
}
