APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Storage Configuration
FILESYSTEM_DISK=public
MAX_FILE_SIZE=10240
MAX_IMAGE_SIZE=5120

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="YNR Cars"

# Alternative Email Providers (uncomment to use)
# For Gmail:
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_ENCRYPTION=tls

# For Outlook/Hotmail:
# MAIL_HOST=smtp-mail.outlook.com
# MAIL_PORT=587
# MAIL_ENCRYPTION=tls

# For Yahoo:
# MAIL_HOST=smtp.mail.yahoo.com
# MAIL_PORT=587
# MAIL_ENCRYPTION=tls

# For SendGrid:
# MAIL_HOST=smtp.sendgrid.net
# MAIL_PORT=587
# MAIL_ENCRYPTION=tls

# For Mailgun:
# MAIL_HOST=smtp.mailgun.org
# MAIL_PORT=587
# MAIL_ENCRYPTION=tls

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# PayPal API Credentials
PAYPAL_MODE=sandbox
PAYPAL_SANDBOX_CLIENT_ID=
PAYPAL_SANDBOX_CLIENT_SECRET=
PAYPAL_LIVE_CLIENT_ID=
PAYPAL_LIVE_CLIENT_SECRET=

# Stripe API Credentials
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=

# Google Maps API
GOOGLE_MAPS_API_KEY=

# Application Settings
APP_COMPANY_NAME="Ynr Cars"
APP_COMPANY_EMAIL="<EMAIL>"
APP_COMPANY_PHONE="+1234567890"
APP_COMPANY_ADDRESS="123 Main Street, City, State 12345"
