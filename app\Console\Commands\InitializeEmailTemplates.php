<?php

namespace App\Console\Commands;

use App\Models\EmailTemplate;
use Illuminate\Console\Command;

class InitializeEmailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:init-templates {--force : Force overwrite existing templates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize default email templates for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing email templates...');

        $templates = $this->getDefaultTemplates();
        $created = 0;
        $updated = 0;

        foreach ($templates as $templateData) {
            $existing = EmailTemplate::where('slug', $templateData['slug'])->first();

            if ($existing) {
                if ($this->option('force')) {
                    $existing->update($templateData);
                    $updated++;
                    $this->line("Updated: {$templateData['name']}");
                } else {
                    $this->line("Skipped: {$templateData['name']} (already exists)");
                }
            } else {
                EmailTemplate::create($templateData);
                $created++;
                $this->info("Created: {$templateData['name']}");
            }
        }

        $this->info("\nEmail templates initialization completed!");
        $this->info("Created: {$created} templates");
        $this->info("Updated: {$updated} templates");

        if (!$this->option('force') && $updated === 0 && $created === 0) {
            $this->comment('Use --force flag to overwrite existing templates');
        }
    }

    /**
     * Get default email templates
     */
    private function getDefaultTemplates(): array
    {
        return [
            [
                'name' => 'Booking Confirmation',
                'slug' => 'booking_confirmation',
                'type' => 'booking',
                'category' => 'client',
                'subject' => 'Booking Confirmation - #{booking_number}',
                'content' => $this->getBookingConfirmationContent(),
                'variables' => ['client_name', 'booking_number', 'pickup_date', 'pickup_address', 'dropoff_address', 'amount', 'driver_name', 'vehicle_name', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients when a booking is confirmed',
                'preview_data' => [
                    'client_name' => 'John Doe',
                    'booking_number' => 'YNR20240101001',
                    'pickup_date' => '2024-01-15 10:00 AM',
                    'pickup_address' => '123 Main Street, London',
                    'dropoff_address' => 'Heathrow Airport',
                    'amount' => '£45.00',
                    'driver_name' => 'Mike Johnson',
                    'vehicle_name' => 'Mercedes E-Class',
                    'company_name' => 'YNR Cars',
                ],
            ],
            [
                'name' => 'Booking Reminder',
                'slug' => 'booking_reminder',
                'type' => 'booking',
                'category' => 'client',
                'subject' => 'Booking Reminder - Tomorrow at {pickup_time}',
                'content' => $this->getBookingReminderContent(),
                'variables' => ['client_name', 'booking_number', 'pickup_date', 'pickup_time', 'pickup_address', 'dropoff_address', 'driver_name', 'driver_phone', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients as a reminder before their booking',
                'preview_data' => [
                    'client_name' => 'John Doe',
                    'booking_number' => 'YNR20240101001',
                    'pickup_date' => 'Tomorrow',
                    'pickup_time' => '10:00 AM',
                    'pickup_address' => '123 Main Street, London',
                    'dropoff_address' => 'Heathrow Airport',
                    'driver_name' => 'Mike Johnson',
                    'driver_phone' => '+44 ************',
                    'company_name' => 'YNR Cars',
                ],
            ],
            [
                'name' => 'Payment Receipt',
                'slug' => 'payment_receipt',
                'type' => 'payment',
                'category' => 'client',
                'subject' => 'Payment Receipt - #{booking_number}',
                'content' => $this->getPaymentReceiptContent(),
                'variables' => ['client_name', 'booking_number', 'amount', 'payment_method', 'payment_date', 'transaction_id', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients after successful payment',
                'preview_data' => [
                    'client_name' => 'John Doe',
                    'booking_number' => 'YNR20240101001',
                    'amount' => '£45.00',
                    'payment_method' => 'Credit Card',
                    'payment_date' => '2024-01-10 14:30',
                    'transaction_id' => 'TXN123456789',
                    'company_name' => 'YNR Cars',
                ],
            ],
            [
                'name' => 'Driver Assignment',
                'slug' => 'driver_assignment',
                'type' => 'booking',
                'category' => 'driver',
                'subject' => 'New Ride Assignment - #{booking_number}',
                'content' => $this->getDriverAssignmentContent(),
                'variables' => ['driver_name', 'booking_number', 'pickup_date', 'pickup_address', 'dropoff_address', 'client_name', 'client_phone', 'amount', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to drivers when assigned to a booking',
                'preview_data' => [
                    'driver_name' => 'Mike Johnson',
                    'booking_number' => 'YNR20240101001',
                    'pickup_date' => '2024-01-15 10:00 AM',
                    'pickup_address' => '123 Main Street, London',
                    'dropoff_address' => 'Heathrow Airport',
                    'client_name' => 'John Doe',
                    'client_phone' => '+44 ************',
                    'amount' => '£45.00',
                    'company_name' => 'YNR Cars',
                ],
            ],
            [
                'name' => 'Welcome Client',
                'slug' => 'welcome_client',
                'type' => 'notification',
                'category' => 'client',
                'subject' => 'Welcome to {company_name}',
                'content' => $this->getWelcomeClientContent(),
                'variables' => ['client_name', 'company_name', 'login_url', 'support_email', 'support_phone'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to new clients upon registration',
                'preview_data' => [
                    'client_name' => 'John Doe',
                    'company_name' => 'YNR Cars',
                    'login_url' => 'https://ynrcars.com/login',
                    'support_email' => '<EMAIL>',
                    'support_phone' => '+44 ************',
                ],
            ],
            [
                'name' => 'Welcome Driver',
                'slug' => 'welcome_driver',
                'type' => 'notification',
                'category' => 'driver',
                'subject' => 'Welcome to Our Driver Team',
                'content' => $this->getWelcomeDriverContent(),
                'variables' => ['driver_name', 'company_name', 'login_url', 'support_email', 'support_phone'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to new drivers upon account creation',
                'preview_data' => [
                    'driver_name' => 'Mike Johnson',
                    'company_name' => 'YNR Cars',
                    'login_url' => 'https://ynrcars.com/driver/login',
                    'support_email' => '<EMAIL>',
                    'support_phone' => '+44 ************',
                ],
            ],
        ];
    }

    private function getBookingConfirmationContent(): string
    {
        return "Dear {client_name},

Thank you for choosing {company_name}! Your booking has been confirmed.

📋 BOOKING DETAILS:
• Booking Number: #{booking_number}
• Pickup Date & Time: {pickup_date}
• Pickup Address: {pickup_address}
• Drop-off Address: {dropoff_address}
• Total Amount: {amount}

🚗 DRIVER DETAILS:
• Driver: {driver_name}
• Vehicle: {vehicle_name}

Your driver will arrive at the pickup location 5-10 minutes before the scheduled time. You will receive an SMS notification when your driver is on the way.

If you need to make any changes or have questions, please contact us immediately.

Thank you for choosing {company_name}!

Best regards,
The {company_name} Team";
    }

    private function getBookingReminderContent(): string
    {
        return "Dear {client_name},

This is a friendly reminder about your upcoming ride with {company_name}.

📋 RIDE DETAILS:
• Booking Number: #{booking_number}
• Date & Time: {pickup_date} at {pickup_time}
• Pickup Location: {pickup_address}
• Destination: {dropoff_address}

🚗 YOUR DRIVER:
• Name: {driver_name}
• Phone: {driver_phone}

Please be ready at the pickup location 5 minutes before your scheduled time. Your driver will contact you upon arrival.

Safe travels!

Best regards,
The {company_name} Team";
    }

    private function getPaymentReceiptContent(): string
    {
        return "Dear {client_name},

Thank you for your payment! Here are your payment details:

💳 PAYMENT RECEIPT:
• Booking Number: #{booking_number}
• Amount Paid: {amount}
• Payment Method: {payment_method}
• Payment Date: {payment_date}
• Transaction ID: {transaction_id}

This receipt serves as confirmation of your payment. Please keep it for your records.

If you have any questions about this payment, please contact our support team.

Thank you for choosing {company_name}!

Best regards,
The {company_name} Team";
    }

    private function getDriverAssignmentContent(): string
    {
        return "Dear {driver_name},

You have been assigned a new ride. Please review the details below:

📋 RIDE DETAILS:
• Booking Number: #{booking_number}
• Pickup Date & Time: {pickup_date}
• Pickup Address: {pickup_address}
• Drop-off Address: {dropoff_address}
• Fare Amount: {amount}

👤 CLIENT DETAILS:
• Name: {client_name}
• Phone: {client_phone}

Please arrive at the pickup location 5-10 minutes early and contact the client upon arrival.

Safe driving!

Best regards,
The {company_name} Team";
    }

    private function getWelcomeClientContent(): string
    {
        return "Dear {client_name},

Welcome to {company_name}! We're excited to have you as our valued customer.

🎉 YOUR ACCOUNT IS READY:
You can now book rides, track your bookings, and manage your account through our platform.

🔗 Quick Links:
• Login to your account: {login_url}
• Book a ride: Start booking from our homepage
• Contact support: {support_email} or {support_phone}

💡 Getting Started:
1. Complete your profile with your preferred addresses
2. Add payment methods for faster checkout
3. Set your notification preferences

We're here to provide you with reliable, comfortable, and professional transportation services.

Welcome aboard!

Best regards,
The {company_name} Team";
    }

    private function getWelcomeDriverContent(): string
    {
        return "Dear {driver_name},

Welcome to the {company_name} driver team! We're thrilled to have you join our professional network.

🚗 YOUR DRIVER ACCOUNT IS READY:
You can now access your driver dashboard, view ride assignments, and manage your earnings.

🔗 Important Links:
• Driver Login: {login_url}
• Upload Documents: Complete your profile
• Contact Support: {support_email} or {support_phone}

📋 Next Steps:
1. Complete your driver profile and upload required documents
2. Set your availability schedule
3. Familiarize yourself with our driver app and policies

We're committed to helping you succeed and earn with us. Our support team is always available to assist you.

Welcome to the team!

Best regards,
The {company_name} Team";
    }
}
