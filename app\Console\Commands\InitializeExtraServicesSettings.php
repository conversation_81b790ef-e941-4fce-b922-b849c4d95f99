<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class InitializeExtraServicesSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:init-extra-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize Extra Services settings with default values';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing Extra Services settings...');
        $this->newLine();

        $extraServicesSettings = [
            [
                'key' => 'meet_and_greet_fee',
                'value' => '10.00',
                'group' => 'extra_services',
                'type' => 'number',
                'label' => 'Meet and Greet Fee',
                'description' => 'Fee for driver meeting client with name sign',
                'is_public' => true,
            ],
            [
                'key' => 'child_seat_fee',
                'value' => '15.00',
                'group' => 'extra_services',
                'type' => 'number',
                'label' => 'Child Seat Fee',
                'description' => 'Fee for providing child safety seat',
                'is_public' => true,
            ],
            [
                'key' => 'wheelchair_fee',
                'value' => '0.00',
                'group' => 'extra_services',
                'type' => 'number',
                'label' => 'Wheelchair Accessible Fee',
                'description' => 'Fee for wheelchair accessible vehicle',
                'is_public' => true,
            ],
            [
                'key' => 'extra_luggage_fee',
                'value' => '5.00',
                'group' => 'extra_services',
                'type' => 'number',
                'label' => 'Extra Luggage Fee',
                'description' => 'Fee for additional luggage space',
                'is_public' => true,
            ],
            [
                'key' => 'meet_and_greet_enabled',
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Enable Meet and Greet',
                'description' => 'Allow clients to request meet and greet service',
                'is_public' => true,
            ],
            [
                'key' => 'child_seat_enabled',
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Enable Child Seat',
                'description' => 'Allow clients to request child safety seats',
                'is_public' => true,
            ],
            [
                'key' => 'wheelchair_enabled',
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Enable Wheelchair Accessible',
                'description' => 'Allow clients to request wheelchair accessible vehicles',
                'is_public' => true,
            ],
            [
                'key' => 'extra_luggage_enabled',
                'value' => 'true',
                'group' => 'extra_services',
                'type' => 'boolean',
                'label' => 'Enable Extra Luggage',
                'description' => 'Allow clients to request additional luggage space',
                'is_public' => true,
            ],
        ];

        $created = 0;
        $updated = 0;

        foreach ($extraServicesSettings as $settingData) {
            $setting = Setting::where('key', $settingData['key'])->first();
            
            if ($setting) {
                // Update existing setting
                $setting->update($settingData);
                $this->line("✅ Updated: {$settingData['key']} = {$settingData['value']}");
                $updated++;
            } else {
                // Create new setting
                $settingData['created_at'] = now();
                $settingData['updated_at'] = now();
                Setting::create($settingData);
                $this->line("🆕 Created: {$settingData['key']} = {$settingData['value']}");
                $created++;
            }
        }

        $this->newLine();
        $this->info("Extra Services settings initialization completed!");
        $this->line("Created: {$created} settings");
        $this->line("Updated: {$updated} settings");

        return 0;
    }
}
