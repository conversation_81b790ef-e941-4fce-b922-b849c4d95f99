<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ride Receipt - {{ $ride->booking_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 20px;
        }
        .logo {
            max-width: 200px;
            margin-bottom: 10px;
        }
        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-subtitle {
            font-size: 16px;
            color: #666;
        }
        .receipt-number {
            font-size: 14px;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .info-value {
            flex: 1;
        }
        .address {
            margin-bottom: 5px;
        }
        .fare-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .fare-table th, .fare-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .fare-table th {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        .fare-total {
            font-weight: bold;
            font-size: 16px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .qr-code {
            text-align: center;
            margin-top: 30px;
        }
        .qr-code img {
            max-width: 100px;
        }
        .payment-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .payment-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .map-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 5px;
        }
        .map-container img {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ public_path('images/logo.png') }}" alt="YNR CARS" class="logo">
            <div class="receipt-title">Ride Receipt</div>
            <div class="receipt-subtitle">Thank you for riding with YNR CARS</div>
            <div class="receipt-number">Booking #{{ $ride->booking_number }}</div>
        </div>

        <div class="section">
            <div class="section-title">Ride Details</div>
            <div class="info-row">
                <div class="info-label">Date:</div>
                <div class="info-value">{{ $ride->pickup_date->format('M d, Y') }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Pickup Time:</div>
                <div class="info-value">{{ $ride->pickup_date->format('h:i A') }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Pickup Location:</div>
                <div class="info-value">{{ $ride->pickup_address }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Dropoff Location:</div>
                <div class="info-value">{{ $ride->dropoff_address }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Distance:</div>
                <div class="info-value">
                    @if($ride->distance && $ride->distance > 0)
                        @php
                            $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                            $displayDistance = $ride->distance;
                            $unitLabel = 'km';

                            if ($distanceUnit === 'miles') {
                                $displayDistance = $ride->distance * 0.621371; // Convert km to miles
                                $unitLabel = 'mi';
                            }
                        @endphp
                        {{ number_format($displayDistance, 1) }} {{ $unitLabel }}
                    @else
                        Not available
                    @endif
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">Duration:</div>
                <div class="info-value">
                    @if($duration)
                        @if($duration < 60)
                            {{ $duration }} minutes
                        @else
                            {{ floor($duration / 60) }} {{ floor($duration / 60) == 1 ? 'hour' : 'hours' }}
                            {{ $duration % 60 > 0 ? ($duration % 60) . ' minutes' : '' }}
                        @endif
                    @else
                        Not available
                    @endif
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">Vehicle:</div>
                <div class="info-value">{{ $ride->vehicle->name }} ({{ $ride->vehicle->model }})</div>
            </div>
            <div class="info-row">
                <div class="info-label">Booking Type:</div>
                <div class="info-value">{{ ucfirst($ride->booking_type) }}</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Fare Breakdown</div>
            @php
                // Calculate fare components based on the actual fare calculation logic
                $vehicleType = $ride->vehicle->type ?? 'sedan';
                $bookingType = $ride->booking_type ?? 'one_way';

                // Get vehicle multiplier
                $vehicleMultiplier = 1.0;
                switch (strtolower($vehicleType)) {
                    case 'economy': $vehicleMultiplier = 0.8; break;
                    case 'sedan': $vehicleMultiplier = 1.0; break;
                    case 'suv': $vehicleMultiplier = 1.3; break;
                    case 'professional': $vehicleMultiplier = 1.8; break;
                    case 'van': $vehicleMultiplier = 1.5; break;
                    case 'limo': $vehicleMultiplier = 2.5; break;
                }

                // Base fare calculation
                $baseFare = 5.0 * $vehicleMultiplier;

                // Distance fare calculation
                $distanceInKm = $ride->distance_value ? ($ride->distance_value / 1000) : ($ride->distance ?? 0);
                $ratePerKm = $ride->vehicle->price_per_km ?? 2.5;
                $adjustedRatePerKm = $ratePerKm * $vehicleMultiplier;

                // Calculate distance fare based on booking type
                $distanceFare = 0;
                $airportSurcharge = 0;

                switch ($bookingType) {
                    case 'return':
                        $distanceFare = ($distanceInKm * $adjustedRatePerKm) * 2;
                        break;
                    case 'hourly':
                        $hourlyRate = 30 * $vehicleMultiplier;
                        $durationHours = $ride->duration_hours ?? 1;
                        $distanceFare = $hourlyRate * $durationHours;
                        break;
                    case 'airport':
                        $airportSurcharge = 15.00;
                        $distanceFare = ($distanceInKm * $adjustedRatePerKm);
                        break;
                    default: // one_way
                        $distanceFare = $distanceInKm * $adjustedRatePerKm;
                        break;
                }

                // Booking fee
                $bookingFee = 2.50;

                // Calculate total fare components
                $calculatedTotalFare = $baseFare + $distanceFare + $airportSurcharge + $bookingFee;

                // Additional charges and discounts
                $additionalCharges = $ride->additional_charges ?? 0;
                $waitingCharge = $ride->waiting_charge ?? 0;
                $discount = $ride->discount ?? 0;

                // Final total with adjustments
                $finalTotal = $calculatedTotalFare + $additionalCharges + $waitingCharge - $discount;

                // If there's a significant difference between calculated fare and stored amount,
                // use the stored amount and adjust the distance fare to make it balance
                if (abs($finalTotal - $ride->amount) > 1) {
                    $adjustment = $ride->amount - ($baseFare + $airportSurcharge + $bookingFee + $additionalCharges + $waitingCharge - $discount);
                    $distanceFare = $adjustment;
                }
            @endphp

            <table class="fare-table">
                <tr>
                    <th>Item</th>
                    <th style="text-align: right;">Amount</th>
                </tr>
                <tr>
                    <td>Base Fare</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($baseFare, 2) }}</td>
                </tr>
                <tr>
                    @php
                        $distanceUnit = \App\Services\SettingsService::getDistanceUnit();
                        $displayDistanceInKm = $distanceInKm;
                        $unitLabel = 'km';

                        if ($distanceUnit === 'miles') {
                            $displayDistanceInKm = $distanceInKm * 0.621371; // Convert km to miles
                            $unitLabel = 'mi';
                        }
                    @endphp
                    <td>Distance Charge ({{ number_format($displayDistanceInKm, 1) }} {{ $unitLabel }})</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($distanceFare, 2) }}</td>
                </tr>
                @if($bookingType === 'airport' && $airportSurcharge > 0)
                <tr>
                    <td>Airport Surcharge</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($airportSurcharge, 2) }}</td>
                </tr>
                @endif
                <tr>
                    <td>Booking Fee</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($bookingFee, 2) }}</td>
                </tr>
                @if($ride->waiting_time > 0 && $waitingCharge > 0)
                <tr>
                    <td>Waiting Time</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($waitingCharge, 2) }}</td>
                </tr>
                @endif
                @if($additionalCharges > 0)
                <tr>
                    <td>Additional Charges</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($additionalCharges, 2) }}</td>
                </tr>
                @endif
                @if($discount > 0)
                <tr>
                    <td>Discount</td>
                    <td style="text-align: right;">-{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($discount, 2) }}</td>
                </tr>
                @endif
                <tr class="fare-total">
                    <td>Total Fare</td>
                    <td style="text-align: right;">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($ride->amount, 2) }}</td>
                </tr>
            </table>

            <div class="payment-info">
                <div class="info-row">
                    <div class="info-label">Payment Method:</div>
                    <div class="info-value">
                        @if($ride->payment && $ride->payment->payment_method)
                            @if($ride->payment->payment_method == 'card')
                                Credit/Debit Card
                            @elseif($ride->payment->payment_method == 'paypal')
                                PayPal
                            @elseif($ride->payment->payment_method == 'cash')
                                Cash
                            @else
                                {{ ucfirst($ride->payment->payment_method) }}
                            @endif
                        @else
                            Not specified
                        @endif
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Payment Status:</div>
                    <div class="info-value">
                        @if($ride->payment && $ride->payment->status)
                            @if($ride->payment->status == 'completed')
                                <span class="payment-status status-completed">Paid</span>
                            @elseif($ride->payment->status == 'pending')
                                <span class="payment-status status-pending">Pending</span>
                            @elseif($ride->payment->status == 'failed')
                                <span class="payment-status status-failed">Failed</span>
                            @else
                                {{ ucfirst($ride->payment->status) }}
                            @endif
                        @else
                            Unknown
                        @endif
                    </div>
                </div>
                @if($ride->payment && $ride->payment->transaction_id)
                <div class="info-row">
                    <div class="info-label">Transaction ID:</div>
                    <div class="info-value">{{ $ride->payment->transaction_id }}</div>
                </div>
                @endif
                @if($ride->payment && $ride->payment->paid_at)
                <div class="info-row">
                    <div class="info-label">Payment Date:</div>
                    <div class="info-value">{{ $ride->payment->paid_at->format('M d, Y h:i A') }}</div>
                </div>
                @endif
            </div>
        </div>

        <div class="footer">
            <p>This is an electronic receipt for your ride with YNR CARS.</p>
            <p>For any questions or concerns, please contact our support <NAME_EMAIL></p>
            <p>&copy; {{ date('Y') }} YNR CARS. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
