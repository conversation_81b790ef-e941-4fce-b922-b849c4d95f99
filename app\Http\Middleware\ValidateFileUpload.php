<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateFileUpload
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if request has file uploads
        if ($request->hasFile()) {
            $this->validateFileUploads($request);
        }

        return $next($request);
    }

    /**
     * Validate file uploads.
     */
    private function validateFileUploads(Request $request): void
    {
        $maxFileSize = config('filesystems.max_file_size', 10240); // 10MB default
        $allowedMimes = config('filesystems.allowed_mimes', [
            'jpeg', 'jpg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'
        ]);

        foreach ($request->allFiles() as $key => $files) {
            if (!is_array($files)) {
                $files = [$files];
            }

            foreach ($files as $file) {
                if ($file->isValid()) {
                    // Check file size
                    if ($file->getSize() > ($maxFileSize * 1024)) {
                        abort(413, "File {$file->getClientOriginalName()} is too large. Maximum size is " . ($maxFileSize / 1024) . "MB");
                    }

                    // Check file type
                    $extension = strtolower($file->getClientOriginalExtension());
                    if (!in_array($extension, $allowedMimes)) {
                        abort(415, "File type '{$extension}' is not allowed. Allowed types: " . implode(', ', $allowedMimes));
                    }

                    // Check for malicious files
                    $this->checkForMaliciousFile($file);
                }
            }
        }
    }

    /**
     * Check for potentially malicious files.
     */
    private function checkForMaliciousFile($file): void
    {
        $dangerousExtensions = [
            'php', 'php3', 'php4', 'php5', 'phtml', 'exe', 'bat', 'cmd', 
            'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'sh', 'py', 'pl', 'rb'
        ];

        $extension = strtolower($file->getClientOriginalExtension());
        $realExtension = strtolower(pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION));

        if (in_array($extension, $dangerousExtensions) || in_array($realExtension, $dangerousExtensions)) {
            abort(415, "File type '{$extension}' is not allowed for security reasons.");
        }

        // Check file content for PHP tags (basic check)
        if (in_array($extension, ['txt', 'html', 'htm'])) {
            $content = file_get_contents($file->getPathname());
            if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
                abort(415, "File contains potentially malicious content.");
            }
        }
    }
}
