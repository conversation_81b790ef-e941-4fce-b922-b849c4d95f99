<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Services\SettingsService;

class AdminSystemAlertMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $alertType;
    public $alertData;
    public $severity;

    /**
     * Create a new message instance.
     */
    public function __construct(string $alertType, array $alertData, string $severity = 'medium')
    {
        $this->alertType = $alertType;
        $this->alertData = $alertData;
        $this->severity = $severity; // low, medium, high, critical
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $severityIcon = [
            'low' => '🔵',
            'medium' => '🟡',
            'high' => '🟠',
            'critical' => '🔴'
        ];

        $icon = $severityIcon[$this->severity] ?? '⚠️';

        return new Envelope(
            subject: $icon . ' System Alert: ' . ucfirst($this->alertType) . ' - ' . SettingsService::getCompanyName(),
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.admin-system-alert',
            with: [
                'alertType' => $this->alertType,
                'alertData' => $this->alertData,
                'severity' => $this->severity,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
