<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PayPalService;
use App\Services\PayPalCardService;
use App\Helpers\SettingsHelper;

class TestPayPalConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paypal:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test PayPal API connection and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing PayPal Configuration...');
        $this->newLine();

        // Check configuration
        $mode = SettingsHelper::getPaypalMode();
        $clientId = SettingsHelper::getPaypalClientId();
        $clientSecret = SettingsHelper::getPaypalSecret();

        $this->info("PayPal Mode: {$mode}");
        $this->info("Client ID: " . ($clientId ? 'Set (' . substr($clientId, 0, 10) . '...)' : 'Not set'));
        $this->info("Client Secret: " . ($clientSecret ? 'Set (' . substr($clientSecret, 0, 10) . '...)' : 'Not set'));
        $this->newLine();

        if (!$clientId || !$clientSecret) {
            $this->error('PayPal credentials are not properly configured!');
            return 1;
        }

        // Test PayPal Service
        $this->info('Testing PayPal Service...');
        try {
            $paypalService = new PayPalService();
            
            // Try to create a test order
            $testOrder = $paypalService->createOrder(
                10.00, // $10 test amount
                'USD',
                'https://example.com/success',
                'https://example.com/cancel'
            );

            if ($testOrder && !isset($testOrder['error'])) {
                $this->info('✅ PayPal Service: Connection successful!');
                $this->info("Test Order ID: {$testOrder['id']}");
            } else {
                $this->error('❌ PayPal Service: Failed to create test order');
                if (isset($testOrder['error'])) {
                    $this->error("Error: " . json_encode($testOrder['error']));
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ PayPal Service: Exception occurred');
            $this->error("Error: " . $e->getMessage());
        }

        $this->newLine();

        // Test PayPal Card Service
        $this->info('Testing PayPal Card Service...');
        try {
            $paypalCardService = new PayPalCardService();
            
            // Try to create a test payment order
            $testPaymentOrder = $paypalCardService->createPaymentOrder(
                15.00, // $15 test amount
                'USD',
                [
                    'booking_id' => 'test',
                    'booking_number' => 'TEST123',
                    'user_id' => 1
                ]
            );

            if ($testPaymentOrder && !isset($testPaymentOrder['error'])) {
                $this->info('✅ PayPal Card Service: Connection successful!');
                $this->info("Test Payment Order ID: {$testPaymentOrder['id']}");
            } else {
                $this->error('❌ PayPal Card Service: Failed to create test payment order');
                if (isset($testPaymentOrder['error'])) {
                    $this->error("Error: " . json_encode($testPaymentOrder['error']));
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ PayPal Card Service: Exception occurred');
            $this->error("Error: " . $e->getMessage());
        }

        $this->newLine();
        $this->info('PayPal connection test completed.');

        return 0;
    }
}
