@extends('layouts.admin')

@section('title', 'Sent Emails')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>📧 Sent Emails</h2>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.email.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Email Management
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.email.sent') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Email Type</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">All Types</option>
                                <option value="booking_confirmation" {{ request('type') === 'booking_confirmation' ? 'selected' : '' }}>Booking Confirmation</option>
                                <option value="payment_receipt" {{ request('type') === 'payment_receipt' ? 'selected' : '' }}>Payment Receipt</option>
                                <option value="booking_reminder" {{ request('type') === 'booking_reminder' ? 'selected' : '' }}>Booking Reminder</option>
                                <option value="welcome_client" {{ request('type') === 'welcome_client' ? 'selected' : '' }}>Welcome Client</option>
                                <option value="welcome_driver" {{ request('type') === 'welcome_driver' ? 'selected' : '' }}>Welcome Driver</option>
                                <option value="contact_form" {{ request('type') === 'contact_form' ? 'selected' : '' }}>Contact Form</option>
                                <option value="test_email" {{ request('type') === 'test_email' ? 'selected' : '' }}>Test Email</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Sent</option>
                                <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                                <option value="bounced" {{ request('status') === 'bounced' ? 'selected' : '' }}>Bounced</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by email or subject..." value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ route('admin.email.sent') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sent Emails Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Sent Emails ({{ $sentEmails->total() }})</h5>
                </div>
                <div class="card-body">
                    @if($sentEmails->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>To</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Sent At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sentEmails as $email)
                                        <tr>
                                            <td>
                                                <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $email->type)) }}</span>
                                            </td>
                                            <td>{{ $email->to_email }}</td>
                                            <td>{{ Str::limit($email->subject, 50) }}</td>
                                            <td>
                                                <span class="badge {{ $email->status_badge_class }}">
                                                    {{ ucfirst($email->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($email->sent_at)
                                                    {{ $email->sent_at->format('M j, Y g:i A') }}
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if($email->status === 'failed')
                                                        <button type="button" class="btn btn-outline-warning" 
                                                                onclick="retryEmail({{ $email->id }})"
                                                                title="Retry sending">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                    @endif
                                                    <button type="button" class="btn btn-outline-info" 
                                                            onclick="viewEmailDetails({{ $email->id }})"
                                                            title="View details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $sentEmails->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sent emails found</h5>
                            <p class="text-muted">No emails match your current filters.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Details Modal -->
<div class="modal fade" id="emailDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="emailDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function viewEmailDetails(emailId) {
    // Load email details via AJAX
    fetch(`/admin/email/logs/${emailId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('emailDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('emailDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error loading email details:', error);
            alert('Failed to load email details');
        });
}

function retryEmail(emailId) {
    if (confirm('Are you sure you want to retry sending this email?')) {
        fetch(`/admin/email/logs/${emailId}/retry`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email queued for retry');
                location.reload();
            } else {
                alert('Failed to retry email: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error retrying email:', error);
            alert('Failed to retry email');
        });
    }
}
</script>
@endpush
