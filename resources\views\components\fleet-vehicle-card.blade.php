@props([
    'title',
    'image',
    'description',
    'features' => [],
    'price' => null,
    'capacity' => null,
    'reverse' => false,
    'delay' => 0
])

<div class="row mb-5" data-aos="fade-up" data-aos-delay="{{ $delay }}">
    <div class="col-lg-4 mb-4 mb-lg-0 {{ $reverse ? 'order-lg-2' : '' }}">
        <x-image-display 
            :src="$image" 
            :alt="$title" 
            type="vehicle"
            class="img-fluid rounded shadow"
            lazy
        />
    </div>
    <div class="col-lg-8 {{ $reverse ? 'order-lg-1' : '' }}">
        <div class="card h-100 border-0 shadow">
            <div class="card-body p-4">
                <h3 class="card-title text-primary mb-3">{{ $title }}</h3>
                
                @if($capacity || $price)
                <div class="row mb-3">
                    @if($capacity)
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-users text-primary me-2"></i>
                            <span><strong>Capacity:</strong> {{ $capacity }}</span>
                        </div>
                    </div>
                    @endif
                    
                    @if($price)
                    <div class="col-sm-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-pound-sign text-primary me-2"></i>
                            <span><strong>From:</strong> £{{ $price }}</span>
                        </div>
                    </div>
                    @endif
                </div>
                @endif
                
                <p class="card-text text-muted mb-3">{{ $description }}</p>
                
                @if(count($features) > 0)
                <div class="features-list">
                    <h6 class="text-primary mb-2">Features:</h6>
                    <div class="row">
                        @foreach($features as $feature)
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <span class="small">{{ $feature }}</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <div class="mt-4">
                    <a href="{{ route('booking.create') }}" class="btn btn-primary me-2">
                        <i class="fas fa-calendar-plus me-1"></i>
                        Book Now
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-phone me-1"></i>
                        Get Quote
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
