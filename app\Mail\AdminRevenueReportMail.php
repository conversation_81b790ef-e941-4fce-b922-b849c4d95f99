<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Services\SettingsService;

class AdminRevenueReportMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $revenueData;
    public $period;
    public $periodLabel;

    /**
     * Create a new message instance.
     */
    public function __construct(array $revenueData, string $period = 'weekly', string $periodLabel = null)
    {
        $this->revenueData = $revenueData;
        $this->period = $period;
        $this->periodLabel = $periodLabel ?? ucfirst($period) . ' Report';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '💰 ' . $this->periodLabel . ' Revenue Report - ' . SettingsService::getCompanyName(),
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.admin-revenue-report',
            with: [
                'revenueData' => $this->revenueData,
                'period' => $this->period,
                'periodLabel' => $this->periodLabel,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
