<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Preview - <?php echo e($template->name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .email-preview {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .email-header {
            background: #ee393d;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .email-content {
            padding: 30px;
            line-height: 1.6;
        }
        .email-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
        .preview-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .variable-highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Preview Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h4 mb-1">📧 Email Template Preview</h2>
                        <p class="text-muted mb-0"><?php echo e($template->name); ?></p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.close()">
                            <i class="fas fa-times me-1"></i> Close
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="preview-info">
                    <div class="row">
                        <div class="col-md-6">
                            <strong><i class="fas fa-info-circle me-1"></i> Template Information:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>Name:</strong> <?php echo e($template->name); ?></li>
                                <li><strong>Type:</strong> <?php echo e(ucfirst($template->type)); ?></li>
                                <li><strong>Category:</strong> <?php echo e($template->category ? ucfirst($template->category) : 'N/A'); ?></li>
                                <li><strong>Status:</strong> <?php echo e($template->is_active ? 'Active' : 'Inactive'); ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong><i class="fas fa-database me-1"></i> Sample Data Used:</strong>
                            <div class="mt-2 small">
                                <?php $__currentLoopData = $previewData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div><code><?php echo e($key); ?></code> = "<?php echo e($value); ?>"</div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Preview -->
        <div class="row">
            <div class="col-12">
                <div class="email-preview">
                    <!-- Email Header -->
                    <div class="email-header">
                        <h3 class="mb-1"><?php echo e(\App\Services\SettingsService::getCompanyName() ?? 'YNR Cars'); ?></h3>
                        <p class="mb-0 opacity-75">Professional Transportation Services</p>
                    </div>

                    <!-- Email Subject -->
                    <div class="bg-light p-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Subject:</strong>
                                <span class="ms-2"><?php echo e($renderedSubject); ?></span>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> <?php echo e(now()->format('M j, Y g:i A')); ?>

                            </small>
                        </div>
                    </div>

                    <!-- Email Content -->
                    <div class="email-content">
                        <div style="white-space: pre-wrap;"><?php echo e($renderedContent); ?></div>
                    </div>

                    <!-- Email Footer -->
                    <div class="email-footer">
                        <div class="mb-2">
                            <strong><?php echo e(\App\Services\SettingsService::getCompanyName() ?? 'YNR Cars'); ?></strong>
                        </div>
                        <div class="mb-2">
                            📧 <?php echo e(\App\Services\SettingsService::getCompanyEmail() ?? '<EMAIL>'); ?> | 
                            📞 <?php echo e(\App\Services\SettingsService::getCompanyPhone() ?? '+44 ************'); ?>

                        </div>
                        <div class="text-muted">
                            This is an automated email from our booking system. Please do not reply to this email.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Source -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📝 Template Source</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Original Subject:</h6>
                                <div class="p-2 bg-light rounded mb-3">
                                    <code><?php echo e($template->subject); ?></code>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Rendered Subject:</h6>
                                <div class="p-2 bg-light rounded mb-3">
                                    <?php echo e($renderedSubject); ?>

                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary">Original Content:</h6>
                        <div class="p-3 bg-light rounded mb-3" style="white-space: pre-wrap; font-family: monospace; font-size: 12px;"><?php echo e($template->content); ?></div>

                        <?php if($template->variables && count($template->variables) > 0): ?>
                            <h6 class="text-primary">Available Variables:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                <?php $__currentLoopData = $template->variables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge bg-primary"><?php echo e($variable); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <a href="<?php echo e(route('admin.email.templates.edit', $template)); ?>" 
                       class="btn btn-warning" target="_blank">
                        <i class="fas fa-edit me-1"></i> Edit Template
                    </a>
                    <a href="<?php echo e(route('admin.email.templates.show', $template)); ?>" 
                       class="btn btn-info" target="_blank">
                        <i class="fas fa-eye me-1"></i> View Details
                    </a>
                    <a href="<?php echo e(route('admin.email.templates.index')); ?>" 
                       class="btn btn-secondary" target="_blank">
                        <i class="fas fa-list me-1"></i> All Templates
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/email/templates/preview.blade.php ENDPATH**/ ?>