<?php

namespace Database\Factories;

use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Booking>
 */
class BookingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $pickupDate = $this->faker->dateTimeBetween('now', '+25 days');
        $returnDate = $this->faker->optional(0.3)->dateTimeBetween($pickupDate, '+30 days');
        
        return [
            'user_id' => User::factory(),
            'vehicle_id' => Vehicle::factory(),
            'driver_id' => User::factory()->driver(),
            'booking_number' => 'YNR' . $this->faker->unique()->numerify('##########'),
            'booking_type' => $this->faker->randomElement(['one_way', 'return', 'hourly', 'airport_transfer']),
            'pickup_address' => $this->faker->address,
            'pickup_lat' => $this->faker->latitude,
            'pickup_lng' => $this->faker->longitude,
            'dropoff_address' => $this->faker->address,
            'dropoff_lat' => $this->faker->latitude,
            'dropoff_lng' => $this->faker->longitude,
            'pickup_date' => $pickupDate,
            'return_date' => $returnDate,
            'duration_hours' => $this->faker->optional(0.3)->numberBetween(1, 8),
            'distance' => $this->faker->randomFloat(2, 5, 100),
            'distance_value' => $this->faker->numberBetween(5000, 100000),
            'duration_value' => $this->faker->numberBetween(600, 7200),
            'amount' => $this->faker->randomFloat(2, 25, 500),
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'assigned', 'in_progress', 'completed', 'cancelled']),
            'payment_status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'refunded']),
            'notes' => $this->faker->optional(0.3)->sentence,
            'rating' => $this->faker->optional(0.5)->numberBetween(1, 5),
            'review' => $this->faker->optional(0.3)->paragraph,
            'fare_details' => json_encode([
                'base_fare' => $this->faker->randomFloat(2, 10, 50),
                'distance_fare' => $this->faker->randomFloat(2, 15, 200),
                'time_fare' => $this->faker->randomFloat(2, 0, 100),
                'tax' => $this->faker->randomFloat(2, 2, 25),
                'total' => $this->faker->randomFloat(2, 25, 500),
            ]),
        ];
    }

    /**
     * Indicate that the booking is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'payment_status' => 'completed',
            'completed_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'rating' => $this->faker->numberBetween(3, 5),
            'review' => $this->faker->paragraph,
        ]);
    }

    /**
     * Indicate that the booking is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the booking is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'cancellation_reason' => $this->faker->sentence,
        ]);
    }
}
