<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailService;

class EmailQueueStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:queue-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display email queue status and statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📧 Email Queue Status');
        $this->line('');

        try {
            $status = EmailService::getEmailQueueStatus();

            // Display basic information
            $this->table(
                ['Property', 'Value'],
                [
                    ['Queue Connection', $status['connection'] ?? 'Unknown'],
                    ['Queue Name', $status['queue_name'] ?? 'Unknown'],
                    ['Status', $this->getStatusIcon($status['status'] ?? 'unknown') . ' ' . ucfirst($status['status'] ?? 'unknown')],
                    ['Worker Status', $this->getWorkerStatusIcon($status['worker_status'] ?? 'unknown') . ' ' . ucfirst($status['worker_status'] ?? 'unknown')],
                ]
            );

            $this->line('');

            // Display job statistics
            $this->table(
                ['Metric', 'Count'],
                [
                    ['Pending Jobs', $status['pending_jobs'] ?? 0],
                    ['Failed Jobs', $status['failed_jobs'] ?? 0],
                    ['Processed Jobs', $status['processed_jobs'] ?? 0],
                ]
            );

            if (isset($status['last_processed']) && $status['last_processed']) {
                $this->line('');
                $this->info('Last Job Processed: ' . $status['last_processed']);
            }

            if (isset($status['error'])) {
                $this->line('');
                $this->error('Error: ' . $status['error']);
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Error getting queue status: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Get status icon
     */
    private function getStatusIcon($status)
    {
        return match($status) {
            'idle' => '🟢',
            'processing' => '🟡',
            'error' => '🔴',
            default => '⚪',
        };
    }

    /**
     * Get worker status icon
     */
    private function getWorkerStatusIcon($status)
    {
        return match($status) {
            'active' => '🟢',
            'idle' => '🟡',
            'unknown' => '⚪',
            default => '⚪',
        };
    }
}
