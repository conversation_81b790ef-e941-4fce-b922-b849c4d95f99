@extends('layouts.admin')

@section('title', 'Call Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Call Details</h1>
                <div class="btn-group">
                    <a href="{{ route('admin.calls.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Calls
                    </a>
                    <a href="{{ route('admin.calls.dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Call Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Call Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Call ID:</strong></td>
                                    <td>{{ $call->call_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date/Time:</strong></td>
                                    <td>
                                        {{ $call->created_at->format('M j, Y \a\t H:i:s') }}<br>
                                        <small class="text-muted">{{ $call->created_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Caller Number:</strong></td>
                                    <td>
                                        <strong>{{ $call->caller_number }}</strong>
                                        @if($call->caller_number_e164 && $call->caller_number_e164 !== $call->caller_number)
                                            <br><small class="text-muted">E164: {{ $call->caller_number_e164 }}</small>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Dialled Number:</strong></td>
                                    <td>{{ $call->dialled_number ?? 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Event Type:</strong></td>
                                    <td>
                                        <span class="badge {{ $call->event_type_badge_class }} text-white">
                                            {{ ucfirst(str_replace('_', ' ', $call->event_type)) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge {{ $call->status_badge_class }} text-white">
                                            {{ ucfirst($call->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Handled By:</strong></td>
                                    <td>
                                        @if($call->handler)
                                            {{ $call->handler->name }}<br>
                                            <small class="text-muted">{{ $call->handled_at?->format('M j, Y H:i') }}</small>
                                        @else
                                            <span class="text-muted">Not handled</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Follow-up:</strong></td>
                                    <td>
                                        @if($call->follow_up_required)
                                            <span class="text-warning">
                                                <i class="fas fa-flag"></i> Required
                                            </span>
                                            @if($call->follow_up_at)
                                                <br><small class="text-muted">{{ $call->follow_up_at->format('M j, Y H:i') }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Not required</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Client Information</h5>
                    @if(!$call->client)
                        <button class="btn btn-sm btn-primary" onclick="identifyClient()">
                            <i class="fas fa-user-plus"></i> Identify Client
                        </button>
                    @endif
                </div>
                <div class="card-body">
                    @if($call->client)
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ $call->client->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $call->client->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $call->client->phone }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>{{ $call->client->address ?? 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Member Since:</strong></td>
                                        <td>{{ $call->client->created_at->format('M j, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Bookings:</strong></td>
                                        <td>{{ $call->client->bookings()->count() }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Booking:</strong></td>
                                        <td>
                                            @php $lastBooking = $call->client->bookings()->latest()->first(); @endphp
                                            {{ $lastBooking ? $lastBooking->created_at->format('M j, Y') : 'None' }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Spent:</strong></td>
                                        <td>£{{ number_format($call->client->bookings()->where('status', 'completed')->sum('amount'), 2) }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{ route('admin.users.show', $call->client) }}" class="btn btn-outline-primary">
                                <i class="fas fa-user"></i> View Client Profile
                            </a>
                            <a href="{{ route('admin.bookings.create', ['client_id' => $call->client->id]) }}" class="btn btn-outline-success">
                                <i class="fas fa-plus"></i> Create Booking
                            </a>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-question fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Client Not Identified</h6>
                            <p class="text-muted">This caller has not been identified as an existing client.</p>
                            <button class="btn btn-primary" onclick="identifyClient()">
                                <i class="fas fa-user-plus"></i> Identify Client
                            </button>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Call History -->
            @if($clientCallHistory && $clientCallHistory->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Client's Call History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Number</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($clientCallHistory as $historyCall)
                                <tr>
                                    <td>{{ $historyCall->created_at->format('M j, Y H:i') }}</td>
                                    <td>{{ $historyCall->caller_number }}</td>
                                    <td>
                                        <span class="badge {{ $historyCall->event_type_badge_class }} text-white">
                                            {{ ucfirst(str_replace('_', ' ', $historyCall->event_type)) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $historyCall->status_badge_class }} text-white">
                                            {{ ucfirst($historyCall->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions & Notes -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    @if($call->status !== 'handled')
                        <button class="btn btn-success w-100 mb-2" onclick="markAsHandled()">
                            <i class="fas fa-check"></i> Mark as Handled
                        </button>
                    @endif
                    
                    <button class="btn btn-warning w-100 mb-2" onclick="toggleFollowUp()">
                        <i class="fas fa-flag"></i> 
                        {{ $call->follow_up_required ? 'Remove Follow-up' : 'Require Follow-up' }}
                    </button>
                    
                    @if($call->client)
                        <a href="tel:{{ $call->client->phone }}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-phone"></i> Call Back
                        </a>
                    @endif
                </div>
            </div>

            <!-- Notes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Notes</h5>
                </div>
                <div class="card-body">
                    <form id="notesForm">
                        <div class="mb-3">
                            <textarea name="notes" class="form-control" rows="6" placeholder="Add notes about this call...">{{ $call->notes }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save"></i> Save Notes
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Client Identification Modal -->
<div class="modal fade" id="identifyClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Identify Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="identifyClientForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Search and Select Client</label>
                        <select name="client_id" class="form-select" required>
                            <option value="">Choose a client...</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Identify Client</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Ensure functions are in global scope
window.identifyClient = function() {
    // Load clients for selection
    $.get('/admin/users', { role: 'client' })
    .done(function(clients) {
        const select = $('#identifyClientModal select[name="client_id"]');
        select.empty().append('<option value="">Choose a client...</option>');

        clients.forEach(client => {
            select.append(`<option value="${client.id}">${client.name} - ${client.email} - ${client.phone}</option>`);
        });

        $('#identifyClientModal').modal('show');
    });
};

window.markAsHandled = function() {
    if (confirm('Mark this call as handled?')) {
        updateCall({ status: 'handled' });
    }
};

window.toggleFollowUp = function() {
    const required = {{ $call->follow_up_required ? 'false' : 'true' }};
    updateCall({
        follow_up_required: required,
        follow_up_at: required ? new Date().toISOString() : null
    });
};

function updateCall(data) {
    $.ajax({
        url: '{{ route("admin.calls.update", $call) }}',
        method: 'PUT',
        data: {
            ...data,
            _token: '{{ csrf_token() }}'
        },
        success: function() {
            location.reload();
        },
        error: function() {
            alert('Failed to update call');
        }
    });
}

$('#identifyClientForm').on('submit', function(e) {
    e.preventDefault();
    
    const clientId = $(this).find('select[name="client_id"]').val();
    
    if (!clientId) {
        alert('Please select a client');
        return;
    }
    
    $.ajax({
        url: '{{ route("admin.calls.identify-client", $call) }}',
        method: 'POST',
        data: {
            client_id: clientId,
            _token: '{{ csrf_token() }}'
        },
        success: function() {
            $('#identifyClientModal').modal('hide');
            location.reload();
        },
        error: function() {
            alert('Failed to identify client');
        }
    });
});

$('#notesForm').on('submit', function(e) {
    e.preventDefault();
    
    const notes = $(this).find('textarea[name="notes"]').val();
    
    updateCall({ notes: notes });
});
</script>
@endpush
