@extends('layouts.admin')

@section('title', 'All Calls')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">All Calls</h1>
                <a href="{{ route('admin.calls.dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.calls.index') }}" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="ringing" {{ request('status') === 'ringing' ? 'selected' : '' }}>Ringing</option>
                        <option value="answered" {{ request('status') === 'answered' ? 'selected' : '' }}>Answered</option>
                        <option value="missed" {{ request('status') === 'missed' ? 'selected' : '' }}>Missed</option>
                        <option value="handled" {{ request('status') === 'handled' ? 'selected' : '' }}>Handled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Event Type</label>
                    <select name="event_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="call_ringing" {{ request('event_type') === 'call_ringing' ? 'selected' : '' }}>Call Ringing</option>
                        <option value="call_missed" {{ request('event_type') === 'call_missed' ? 'selected' : '' }}>Call Missed</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Client Identified</label>
                    <select name="client_identified" class="form-select">
                        <option value="">All</option>
                        <option value="yes" {{ request('client_identified') === 'yes' ? 'selected' : '' }}>Yes</option>
                        <option value="no" {{ request('client_identified') === 'no' ? 'selected' : '' }}>No</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date From</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Date To</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" placeholder="Phone, name, email..." value="{{ request('search') }}">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('admin.calls.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Calls Table -->
    <div class="card">
        <div class="card-body">
            @if($calls->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>Caller Number</th>
                                <th>Client</th>
                                <th>Event Type</th>
                                <th>Status</th>
                                <th>Handler</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($calls as $call)
                            <tr>
                                <td>
                                    <strong>{{ $call->created_at->format('M j, Y') }}</strong><br>
                                    <small class="text-muted">{{ $call->created_at->format('H:i:s') }}</small><br>
                                    <small class="text-muted">{{ $call->created_at->diffForHumans() }}</small>
                                </td>
                                <td>
                                    <strong>{{ $call->caller_number }}</strong>
                                    @if($call->caller_number_e164 && $call->caller_number_e164 !== $call->caller_number)
                                        <br><small class="text-muted">{{ $call->caller_number_e164 }}</small>
                                    @endif
                                </td>
                                <td>
                                    @if($call->client)
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>{{ $call->client->name }}</strong><br>
                                                <small class="text-muted">{{ $call->client->email }}</small><br>
                                                <small class="text-muted">{{ $call->client->phone }}</small>
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-info mt-1" data-bs-toggle="modal" data-bs-target="#clientModal{{ $call->client->id }}">
                                            <i class="fas fa-search"></i> Look Up
                                        </button>
                                        <!-- Client Modal -->
                                        <div class="modal fade" id="clientModal{{ $call->client->id }}" tabindex="-1" aria-labelledby="clientModalLabel{{ $call->client->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="clientModalLabel{{ $call->client->id }}">Client Details</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <strong>Name:</strong> {{ $call->client->name }}<br>
                                                        <strong>Email:</strong> {{ $call->client->email }}<br>
                                                        <strong>Phone:</strong> {{ $call->client->phone }}<br>
                                                        <strong>Created:</strong> {{ $call->client->created_at->format('M d, Y H:i') }}<br>
                                                        <strong>Status:</strong> {{ $call->client->is_active ? 'Active' : 'Inactive' }}
                                                    </div>
                                                    <div class="modal-footer">
                                                        <a href="{{ route('admin.clients.details', $call->client->id) }}" class="btn btn-primary">Full Profile</a>
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @elseif($call->client_name)
                                        <span class="text-warning">{{ $call->client_name }}</span><br>
                                        <small class="text-muted">Client deleted</small>
                                    @else
                                        <span class="text-muted">Unknown</span><br>
                                        <button class="btn btn-sm btn-outline-primary" onclick="identifyClient({{ $call->id }})">
                                            <i class="fas fa-user-plus"></i> Identify
                                        </button>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge {{ $call->event_type_badge_class }} text-white">
                                        {{ ucfirst(str_replace('_', ' ', $call->event_type)) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {{ $call->status_badge_class }} text-white">
                                        {{ ucfirst($call->status) }}
                                    </span>
                                    @if($call->follow_up_required)
                                        <br><small class="text-warning">
                                            <i class="fas fa-flag"></i> Follow-up required
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    @if($call->handler)
                                        <strong>{{ $call->handler->name }}</strong><br>
                                        <small class="text-muted">{{ $call->handled_at?->format('M j, H:i') }}</small>
                                    @else
                                        <span class="text-muted">Not handled</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.calls.show', $call) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($call->status !== 'handled')
                                            <button class="btn btn-outline-success" onclick="markAsHandled({{ $call->id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $calls->firstItem() }} to {{ $calls->lastItem() }} of {{ $calls->total() }} calls
                    </div>
                    <div>
                        {{ $calls->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No calls found</h5>
                    <p class="text-muted">No calls match your current filters.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Client Identification Modal -->
<div class="modal fade" id="identifyClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Identify Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="identifyClientForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Client</label>
                        <select name="client_id" class="form-select" required>
                            <option value="">Choose a client...</option>
                            <!-- Options loaded via AJAX -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Identify Client</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentCallId = null;

// Ensure functions are in global scope
window.identifyClient = function(callId) {
    currentCallId = callId;

    // Load clients for selection
    $.get('/admin/users', { role: 'client' })
    .done(function(clients) {
        const select = $('#identifyClientModal select[name="client_id"]');
        select.empty().append('<option value="">Choose a client...</option>');

        clients.forEach(client => {
            select.append(`<option value="${client.id}">${client.name} - ${client.email}</option>`);
        });

        $('#identifyClientModal').modal('show');
    });
};

window.markAsHandled = function(callId) {
    if (confirm('Mark this call as handled?')) {
        $.ajax({
            url: `/admin/calls/${callId}`,
            method: 'PUT',
            data: {
                status: 'handled',
                _token: '{{ csrf_token() }}'
            },
            success: function() {
                location.reload();
            },
            error: function() {
                alert('Failed to update call status');
            }
        });
    }
};

$('#identifyClientForm').on('submit', function(e) {
    e.preventDefault();
    
    const clientId = $(this).find('select[name="client_id"]').val();
    
    if (!clientId) {
        alert('Please select a client');
        return;
    }
    
    $.ajax({
        url: `/admin/calls/${currentCallId}/identify-client`,
        method: 'POST',
        data: {
            client_id: clientId,
            _token: '{{ csrf_token() }}'
        },
        success: function() {
            $('#identifyClientModal').modal('hide');
            location.reload();
        },
        error: function() {
            alert('Failed to identify client');
        }
    });
});
</script>
@endpush
