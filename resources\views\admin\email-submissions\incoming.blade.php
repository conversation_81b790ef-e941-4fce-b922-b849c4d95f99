@extends('layouts.admin')

@section('title', 'Incoming Emails')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">📨 Incoming Emails</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.email-submissions.contact') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-envelope"></i> Contact Forms
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.email-submissions.incoming') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label for="type" class="form-label">Email Type</label>
                                    <select name="type" id="type" class="form-select">
                                        <option value="">All Types</option>
                                        @foreach($emailTypes as $type)
                                            <option value="{{ $type }}" {{ request('type') === $type ? 'selected' : '' }}>
                                                {{ ucfirst(str_replace('_', ' ', $type)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">All Statuses</option>
                                        <option value="processed" {{ request('status') === 'processed' ? 'selected' : '' }}>Processed</option>
                                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="search" class="form-label">Search</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control" placeholder="From email, subject..." value="{{ request('search') }}">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        @if(request()->hasAny(['type', 'status', 'search']))
                                            <a href="{{ route('admin.email-submissions.incoming') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $incomingEmails->total() }}</h4>
                                            <p class="mb-0">Total Incoming</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-inbox fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $incomingEmails->where('status', 'processed')->count() }}</h4>
                                            <p class="mb-0">Processed</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $incomingEmails->where('type', 'email_reply')->count() }}</h4>
                                            <p class="mb-0">Email Replies</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-reply fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $incomingEmails->where('created_at', '>=', now()->subDay())->count() }}</h4>
                                            <p class="mb-0">Last 24 Hours</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Incoming Emails Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Type</th>
                                    <th>From</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Received At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($incomingEmails as $email)
                                    <tr>
                                        <td>
                                            <span class="badge bg-{{ $email->type === 'email_reply' ? 'info' : 'secondary' }}">
                                                {{ ucfirst(str_replace('_', ' ', $email->type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>{{ $email->from_email }}</strong>
                                            @if($email->related_type && $email->related_id)
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-link"></i> 
                                                    Related to {{ ucfirst(str_replace('_', ' ', $email->related_type)) }} #{{ $email->related_id }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 300px;" title="{{ $email->subject }}">
                                                {{ $email->subject ?: 'No Subject' }}
                                            </div>
                                            @if($email->content)
                                                <small class="text-muted d-block">
                                                    {{ Str::limit(strip_tags($email->content), 100) }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $email->status_badge_class }}">
                                                {{ ucfirst($email->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                {{ $email->created_at->format('d M Y H:i') }}
                                                <br>
                                                <span class="text-muted">{{ $email->created_at->diffForHumans() }}</span>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewIncomingEmailDetails({{ $email->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($email->from_email)
                                                    <a href="mailto:{{ $email->from_email }}?subject=Re: {{ urlencode($email->subject) }}" class="btn btn-outline-success" title="Reply">
                                                        <i class="fas fa-reply"></i>
                                                    </a>
                                                @endif
                                                @if($email->related_type && $email->related_id)
                                                    <button type="button" class="btn btn-outline-info" onclick="viewRelatedSubmission('{{ $email->related_type }}', {{ $email->related_id }})" title="View Related">
                                                        <i class="fas fa-link"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p>No incoming emails found</p>
                                                <small>Incoming emails from contact forms, replies, and webhooks will appear here</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($incomingEmails->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $incomingEmails->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Incoming Email Details Modal -->
<div class="modal fade" id="incomingEmailDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📨 Incoming Email Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="incomingEmailDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="replyToEmailBtn">Reply to Email</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function viewIncomingEmailDetails(emailId) {
    $('#incomingEmailDetailsModal').modal('show');
    $('#incomingEmailDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    
    // In a real implementation, you would fetch email details via AJAX
    setTimeout(() => {
        $('#incomingEmailDetailsContent').html(`
            <div class="row">
                <div class="col-md-6">
                    <strong>Email ID:</strong> ${emailId}<br>
                    <strong>Type:</strong> Email Reply<br>
                    <strong>Status:</strong> <span class="badge bg-success">Processed</span><br>
                    <strong>Received At:</strong> ${new Date().toLocaleString()}
                </div>
                <div class="col-md-6">
                    <strong>From:</strong> <EMAIL><br>
                    <strong>Subject:</strong> Re: Booking Inquiry<br>
                    <strong>Related:</strong> Contact Form #123
                </div>
            </div>
            <hr>
            <div>
                <strong>Email Content:</strong>
                <div class="border p-3 mt-2" style="max-height: 300px; overflow-y: auto;">
                    <p>Thank you for your quick response! I would like to proceed with the booking.</p>
                    <p>Please send me the payment details and confirmation.</p>
                    <p>Best regards,<br>John Smith</p>
                </div>
            </div>
            <hr>
            <div>
                <strong>Metadata:</strong>
                <div class="bg-light p-2 rounded">
                    <small>
                        <strong>IP Address:</strong> *************<br>
                        <strong>Provider:</strong> Generic<br>
                        <strong>Processed At:</strong> ${new Date().toLocaleString()}
                    </small>
                </div>
            </div>
        `);
    }, 500);
}

function viewRelatedSubmission(type, id) {
    if (type === 'ContactSubmission') {
        window.open(`{{ route('admin.email-submissions.show-contact', '') }}/${id}`, '_blank');
    }
}

// Reply button functionality
$('#replyToEmailBtn').on('click', function() {
    // In a real implementation, this would open a compose email modal or redirect to email compose
    alert('Reply functionality would be implemented here');
});
</script>
@endsection
