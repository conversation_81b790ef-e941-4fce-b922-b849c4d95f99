<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('corporate_inquiries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone');
            $table->string('company');
            $table->string('position')->nullable();
            $table->integer('employees')->nullable();
            $table->enum('service_type', ['regular_transport', 'event_transport', 'airport_transfers', 'executive_travel', 'custom']);
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'occasional', 'one_time']);
            $table->enum('budget_range', ['under_1000', '1000_5000', '5000_10000', '10000_25000', '25000_plus'])->nullable();
            $table->date('start_date')->nullable();
            $table->text('message');
            $table->string('preferred_contact_time')->nullable();
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->enum('status', ['new', 'emailed', 'email_failed', 'contacted', 'quoted', 'converted', 'lost', 'closed'])->default('new');
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->timestamp('submitted_at');
            $table->timestamp('contacted_at')->nullable();
            $table->timestamp('quoted_at')->nullable();
            $table->timestamps();

            $table->index(['status', 'priority', 'submitted_at']);
            $table->index(['company', 'submitted_at']);
            $table->index(['service_type', 'frequency']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('corporate_inquiries');
    }
};
