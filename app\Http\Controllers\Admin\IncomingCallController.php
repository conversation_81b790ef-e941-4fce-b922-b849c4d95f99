<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IncomingCall;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class IncomingCallController extends Controller
{
    /**
     * Display call dashboard
     */
    public function dashboard()
    {
        // Get call statistics
        $stats = [
            'today' => [
                'total' => IncomingCall::today()->count(),
                'missed' => IncomingCall::today()->missed()->count(),
                'answered' => IncomingCall::today()->answered()->count(),
                'identified' => IncomingCall::today()->whereNotNull('client_id')->count(),
            ],
            'week' => [
                'total' => IncomingCall::where('created_at', '>=', now()->startOfWeek())->count(),
                'missed' => IncomingCall::where('created_at', '>=', now()->startOfWeek())->missed()->count(),
                'answered' => IncomingCall::where('created_at', '>=', now()->startOfWeek())->answered()->count(),
                'identified' => IncomingCall::where('created_at', '>=', now()->startOfWeek())->whereNotNull('client_id')->count(),
            ],
        ];

        // Get recent calls
        $recentCalls = IncomingCall::with('client')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get follow-up calls
        $followUpCalls = IncomingCall::requiresFollowUp()
            ->with('client')
            ->orderBy('follow_up_at')
            ->limit(5)
            ->get();

        return view('admin.calls.dashboard', compact('stats', 'recentCalls', 'followUpCalls'));
    }

    /**
     * Display all calls with filtering
     */
    public function index(Request $request)
    {
        $query = IncomingCall::with(['client', 'handler'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('client_identified')) {
            if ($request->client_identified === 'yes') {
                $query->whereNotNull('client_id');
            } else {
                $query->whereNull('client_id');
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('caller_number', 'like', "%{$search}%")
                  ->orWhere('client_name', 'like', "%{$search}%")
                  ->orWhere('client_email', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        $calls = $query->paginate(20);

        return view('admin.calls.index', compact('calls'));
    }

    /**
     * Show individual call details
     */
    public function show(IncomingCall $call)
    {
        $call->load(['client', 'handler']);
        
        // Get client's call history if identified
        $clientCallHistory = null;
        if ($call->client_id) {
            $clientCallHistory = IncomingCall::where('client_id', $call->client_id)
                ->where('id', '!=', $call->id)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
        }

        return view('admin.calls.show', compact('call', 'clientCallHistory'));
    }

    /**
     * Update call details
     */
    public function update(Request $request, IncomingCall $call)
    {
        $request->validate([
            'notes' => 'nullable|string',
            'status' => 'required|in:ringing,answered,missed,handled',
            'follow_up_required' => 'boolean',
            'follow_up_at' => 'nullable|date',
        ]);

        $call->update([
            'notes' => $request->notes,
            'status' => $request->status,
            'follow_up_required' => $request->boolean('follow_up_required'),
            'follow_up_at' => $request->follow_up_at,
        ]);

        if ($request->status === 'handled') {
            $call->markAsHandled(auth()->id(), $request->notes);
        }

        return redirect()->back()->with('success', 'Call updated successfully.');
    }

    /**
     * Identify client for a call
     */
    public function identifyClient(Request $request, IncomingCall $call)
    {
        $request->validate([
            'client_id' => 'required|exists:users,id',
        ]);

        $client = User::find($request->client_id);
        
        $call->update([
            'client_id' => $client->id,
            'client_name' => $client->name,
            'client_email' => $client->email,
        ]);

        return redirect()->back()->with('success', 'Client identified successfully.');
    }

    /**
     * Get live call popup
     */
    public function livePopup(Request $request)
    {
        $phoneNumber = $request->get('phone');

        if (!$phoneNumber) {
            return response()->json(['error' => 'Phone number required'], 400);
        }

        // Clean phone number for lookup (same logic as CircleLoop controller)
        $cleanNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Remove leading country codes if present
        if (strlen($cleanNumber) > 10) {
            // Remove UK country code (44)
            if (str_starts_with($cleanNumber, '44')) {
                $cleanNumber = '0' . substr($cleanNumber, 2);
            }
            // Remove US country code (1)
            elseif (str_starts_with($cleanNumber, '1') && strlen($cleanNumber) == 11) {
                $cleanNumber = substr($cleanNumber, 1);
            }
        }

        // Find client using multiple matching strategies
        $client = User::where('phone', $cleanNumber)->first();

        // Try partial matches if exact match fails
        if (!$client) {
            $client = User::where('phone', 'LIKE', '%' . substr($cleanNumber, -7) . '%')->first();
        }

        // Try without leading zero
        if (!$client && str_starts_with($cleanNumber, '0')) {
            $withoutZero = substr($cleanNumber, 1);
            $client = User::where('phone', 'LIKE', '%' . $withoutZero . '%')->first();
        }

        return view('admin.calls.live-popup', [
            'phoneNumber' => $phoneNumber,
            'client' => $client,
        ]);
    }

    /**
     * Get call statistics for AJAX
     */
    public function getStats()
    {
        $stats = [
            'today' => [
                'total' => IncomingCall::today()->count(),
                'missed' => IncomingCall::today()->missed()->count(),
                'answered' => IncomingCall::today()->answered()->count(),
                'identified' => IncomingCall::today()->whereNotNull('client_id')->count(),
            ],
            'recent_calls' => IncomingCall::with('client')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($call) {
                    return [
                        'id' => $call->id,
                        'caller_number' => $call->caller_number,
                        'client_name' => $call->client_name ?? 'Unknown',
                        'status' => $call->status,
                        'time_ago' => $call->created_at->diffForHumans(),
                    ];
                }),
        ];

        return response()->json($stats);
    }

    /**
     * Check for new calls (polling endpoint)
     */
    public function checkNewCalls(Request $request)
    {
        $since = $request->get('since', now()->subMinutes(5)->toISOString());

        $newCalls = IncomingCall::with('client')
            ->where('created_at', '>', $since)
            ->where('status', 'ringing')
            ->get()
            ->map(function ($call) {
                return [
                    'id' => $call->id,
                    'call_id' => $call->call_id,
                    'caller_number' => $call->caller_number,
                    'client' => $call->client ? [
                        'name' => $call->client->name,
                        'email' => $call->client->email,
                        'phone' => $call->client->phone,
                    ] : null,
                    'created_at' => $call->created_at->toISOString(),
                ];
            });

        return response()->json([
            'success' => true,
            'new_calls' => $newCalls,
            'count' => $newCalls->count(),
        ]);
    }

    /**
     * Show call analytics
     */
    public function analytics(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $period = $request->get('period', 'day');

        // Get base query
        $query = IncomingCall::whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59']);

        // Summary statistics
        $totalCalls = $query->count();
        $answeredCalls = $query->where('status', 'answered')->count();
        $missedCalls = $query->where('status', 'missed')->count();
        $identifiedCalls = $query->whereNotNull('client_id')->count();

        $summary = [
            'total_calls' => $totalCalls,
            'answered_calls' => $answeredCalls,
            'missed_calls' => $missedCalls,
            'identified_calls' => $identifiedCalls,
            'answer_rate' => $totalCalls > 0 ? round(($answeredCalls / $totalCalls) * 100, 1) : 0,
            'miss_rate' => $totalCalls > 0 ? round(($missedCalls / $totalCalls) * 100, 1) : 0,
            'identification_rate' => $totalCalls > 0 ? round(($identifiedCalls / $totalCalls) * 100, 1) : 0,
        ];

        // Chart data
        $charts = $this->generateChartData($dateFrom, $dateTo, $period);

        // Top callers
        $topCallers = IncomingCall::selectRaw('caller_number, client_name, COUNT(*) as call_count, MAX(created_at) as last_call')
            ->whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])
            ->groupBy('caller_number', 'client_name')
            ->orderBy('call_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();

        // Performance metrics
        $performance = [
            'avg_response_time' => $this->calculateAverageResponseTime($dateFrom, $dateTo),
            'calls_handled_today' => IncomingCall::whereDate('created_at', today())->where('status', 'handled')->count(),
            'pending_follow_ups' => IncomingCall::where('follow_up_required', true)->count(),
            'client_conversion_rate' => $this->calculateClientConversionRate($dateFrom, $dateTo),
        ];

        $analytics = [
            'summary' => $summary,
            'charts' => $charts,
            'top_callers' => $topCallers,
            'performance' => $performance,
        ];

        // Export functionality
        if ($request->get('export') === 'csv') {
            return $this->exportAnalytics($analytics, $dateFrom, $dateTo);
        }

        return view('admin.calls.analytics', compact('analytics'));
    }

    /**
     * Generate chart data for analytics
     */
    private function generateChartData($dateFrom, $dateTo, $period)
    {
        $format = match($period) {
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d',
        };

        $calls = IncomingCall::selectRaw("
            DATE_FORMAT(created_at, '{$format}') as period,
            COUNT(*) as total,
            SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered,
            SUM(CASE WHEN status = 'missed' THEN 1 ELSE 0 END) as missed,
            SUM(CASE WHEN status = 'handled' THEN 1 ELSE 0 END) as handled
        ")
        ->whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])
        ->groupBy('period')
        ->orderBy('period')
        ->get();

        // Peak hours analysis
        $peakHours = IncomingCall::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        return [
            'volume' => [
                'labels' => $calls->pluck('period')->toArray(),
                'total' => $calls->pluck('total')->toArray(),
                'answered' => $calls->pluck('answered')->toArray(),
                'missed' => $calls->pluck('missed')->toArray(),
            ],
            'status' => [
                'answered' => $calls->sum('answered'),
                'missed' => $calls->sum('missed'),
                'handled' => $calls->sum('handled'),
            ],
            'peak_hours' => [
                'labels' => $peakHours->pluck('hour')->map(fn($h) => sprintf('%02d:00', $h))->toArray(),
                'data' => $peakHours->pluck('count')->toArray(),
            ],
        ];
    }

    /**
     * Calculate average response time
     */
    private function calculateAverageResponseTime($dateFrom, $dateTo)
    {
        $avgSeconds = IncomingCall::whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])
            ->whereNotNull('handled_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, created_at, handled_at)) as avg_response')
            ->value('avg_response');

        if (!$avgSeconds) return 'N/A';

        $minutes = floor($avgSeconds / 60);
        $seconds = $avgSeconds % 60;

        return sprintf('%dm %ds', $minutes, $seconds);
    }

    /**
     * Calculate client conversion rate
     */
    private function calculateClientConversionRate($dateFrom, $dateTo)
    {
        $totalCalls = IncomingCall::whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])->count();
        $identifiedCalls = IncomingCall::whereBetween('created_at', [$dateFrom, $dateTo . ' 23:59:59'])
            ->whereNotNull('client_id')
            ->count();

        return $totalCalls > 0 ? round(($identifiedCalls / $totalCalls) * 100, 1) : 0;
    }

    /**
     * Export analytics data
     */
    private function exportAnalytics($analytics, $dateFrom, $dateTo)
    {
        $filename = "call_analytics_{$dateFrom}_to_{$dateTo}.csv";

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($analytics) {
            $file = fopen('php://output', 'w');

            // Summary data
            fputcsv($file, ['Call Analytics Summary']);
            fputcsv($file, ['Metric', 'Value']);
            fputcsv($file, ['Total Calls', $analytics['summary']['total_calls']]);
            fputcsv($file, ['Answered Calls', $analytics['summary']['answered_calls']]);
            fputcsv($file, ['Missed Calls', $analytics['summary']['missed_calls']]);
            fputcsv($file, ['Identified Calls', $analytics['summary']['identified_calls']]);
            fputcsv($file, ['Answer Rate', $analytics['summary']['answer_rate'] . '%']);
            fputcsv($file, ['Miss Rate', $analytics['summary']['miss_rate'] . '%']);
            fputcsv($file, ['Identification Rate', $analytics['summary']['identification_rate'] . '%']);

            fputcsv($file, []); // Empty row

            // Top callers
            fputcsv($file, ['Top Callers']);
            fputcsv($file, ['Caller Number', 'Client Name', 'Call Count', 'Last Call']);
            foreach ($analytics['top_callers'] as $caller) {
                fputcsv($file, [
                    $caller['caller_number'],
                    $caller['client_name'] ?? 'Unknown',
                    $caller['call_count'],
                    $caller['last_call']
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
