<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\IncomingCall;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CircleLoopController extends Controller
{
    /**
     * Handle CircleLoop webhook for incoming calls
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            Log::info('CircleLoop webhook received', $request->all());

            $eventType = $request->input('eventType');
            $originatingNumber = $request->input('originatingNumber');
            $originatingNumberE164 = $request->input('originatingNumberE164');
            $dialledNumber = $request->input('dialledNumber');
            $callID = $request->input('callID');
            $userID = $request->input('userID');
            $created = $request->input('created');

            // Only process call events
            if (!in_array($eventType, ['call_ringing', 'call_missed'])) {
                return response()->json(['status' => 'ignored', 'reason' => 'Not a call event']);
            }

            // Clean phone number for lookup
            $cleanNumber = $this->cleanPhoneNumber($originatingNumber);
            $cleanNumberE164 = $this->cleanPhoneNumber($originatingNumberE164);

            // Find client by phone number
            $client = $this->findClientByPhone($cleanNumber, $cleanNumberE164);

            // Store call information
            $callData = [
                'call_id' => $callID,
                'user_id' => $userID,
                'event_type' => $eventType,
                'caller_number' => $originatingNumber,
                'caller_number_e164' => $originatingNumberE164,
                'dialled_number' => $dialledNumber,
                'client_id' => $client ? $client->id : null,
                'client_name' => $client ? $client->name : null,
                'client_email' => $client ? $client->email : null,
                'created_at' => $created ? date('Y-m-d H:i:s', $created) : now(),
                'status' => $eventType === 'call_ringing' ? 'ringing' : 'missed',
            ];

            // Store in database
            IncomingCall::create($callData);

            // Cache for real-time display (expires in 5 minutes)
            $cacheKey = 'incoming_call_' . $callID;
            Cache::put($cacheKey, array_merge($callData, [
                'client' => $client ? $client->toArray() : null,
                'timestamp' => now()->toISOString(),
            ]), 300);

            // Broadcast to admin dashboard (if using WebSockets)
            $this->broadcastCallNotification($callData, $client);

            return response()->json([
                'status' => 'success',
                'message' => 'Call processed successfully',
                'client_identified' => $client ? true : false,
                'client_name' => $client ? $client->name : 'Unknown',
            ]);

        } catch (\Exception $e) {
            Log::error('CircleLoop webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to process webhook',
            ], 500);
        }
    }

    /**
     * Get current call information for admin dashboard
     */
    public function getCurrentCall(Request $request): JsonResponse
    {
        $phoneNumber = $request->input('phone');
        
        if (!$phoneNumber) {
            return response()->json(['error' => 'Phone number required'], 400);
        }

        $cleanNumber = $this->cleanPhoneNumber($phoneNumber);
        $client = $this->findClientByPhone($cleanNumber);

        $response = [
            'phone' => $phoneNumber,
            'timestamp' => now()->toISOString(),
            'client_identified' => false,
            'client' => null,
        ];

        if ($client) {
            $response['client_identified'] = true;
            $response['client'] = [
                'id' => $client->id,
                'name' => $client->name,
                'email' => $client->email,
                'phone' => $client->phone,
                'address' => $client->address,
                'total_bookings' => $client->bookings()->count(),
                'last_booking' => $client->bookings()->latest()->first()?->created_at?->format('M j, Y'),
                'total_spent' => $client->bookings()->where('status', 'completed')->sum('amount'),
                'member_since' => $client->created_at->format('M j, Y'),
            ];
        }

        return response()->json($response);
    }

    /**
     * Get recent calls for dashboard
     */
    public function getRecentCalls(): JsonResponse
    {
        $recentCalls = IncomingCall::with('client')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($call) {
                return [
                    'id' => $call->id,
                    'call_id' => $call->call_id,
                    'event_type' => $call->event_type,
                    'caller_number' => $call->caller_number,
                    'client_name' => $call->client_name ?? 'Unknown',
                    'client_email' => $call->client_email,
                    'status' => $call->status,
                    'created_at' => $call->created_at->format('M j, Y g:i A'),
                    'time_ago' => $call->created_at->diffForHumans(),
                ];
            });

        return response()->json([
            'success' => true,
            'calls' => $recentCalls,
        ]);
    }

    /**
     * Clean phone number for consistent lookup
     */
    private function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Remove leading country codes if present
        if (strlen($cleaned) > 10) {
            // Remove UK country code (44)
            if (str_starts_with($cleaned, '44')) {
                $cleaned = '0' . substr($cleaned, 2);
            }
            // Remove US country code (1)
            elseif (str_starts_with($cleaned, '1') && strlen($cleaned) == 11) {
                $cleaned = substr($cleaned, 1);
            }
        }

        return $cleaned;
    }

    /**
     * Find client by phone number
     */
    private function findClientByPhone(string $phoneNumber, string $phoneNumberE164 = null): ?User
    {
        // Try exact match first
        $client = User::where('phone', $phoneNumber)->first();
        
        if (!$client && $phoneNumberE164) {
            $client = User::where('phone', $phoneNumberE164)->first();
        }

        // Try partial matches
        if (!$client) {
            $client = User::where('phone', 'LIKE', '%' . substr($phoneNumber, -7) . '%')->first();
        }

        // Try without leading zero
        if (!$client && str_starts_with($phoneNumber, '0')) {
            $withoutZero = substr($phoneNumber, 1);
            $client = User::where('phone', 'LIKE', '%' . $withoutZero . '%')->first();
        }

        return $client;
    }

    /**
     * Broadcast call notification to admin dashboard
     */
    private function broadcastCallNotification(array $callData, ?User $client): void
    {
        // This would integrate with WebSockets or Server-Sent Events
        // For now, we'll just cache the notification
        $notification = [
            'type' => 'incoming_call',
            'call_id' => $callData['call_id'],
            'caller_number' => $callData['caller_number'],
            'client' => $client ? $client->name : 'Unknown Caller',
            'timestamp' => now()->toISOString(),
        ];

        Cache::put('latest_call_notification', $notification, 60);
    }

    /**
     * Test webhook endpoint
     */
    public function test(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => 'CircleLoop webhook endpoint is working',
            'timestamp' => now()->toISOString(),
            'url' => url('/api/circleloop/webhook'),
        ]);
    }
}
