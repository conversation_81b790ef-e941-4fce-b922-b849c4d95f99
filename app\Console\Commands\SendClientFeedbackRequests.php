<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Services\EmailService;
use Carbon\Carbon;

class SendClientFeedbackRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:feedback-requests {--hours=24 : Hours after completion to send feedback request}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send feedback requests to clients after completed rides';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hoursAfter = $this->option('hours');
        $this->info("Sending feedback requests for rides completed {$hoursAfter} hours ago...");

        $targetTime = Carbon::now()->subHours($hoursAfter);
        $feedbackSent = 0;

        // Find completed bookings from the target time
        $completedBookings = Booking::where('status', 'completed')
            ->whereBetween('updated_at', [
                $targetTime->copy()->subMinutes(30), // 30-minute window
                $targetTime->copy()->addMinutes(30)
            ])
            ->whereNull('feedback_requested_at') // Haven't sent feedback request yet
            ->with(['user', 'driver'])
            ->get();

        foreach ($completedBookings as $booking) {
            try {
                EmailService::sendClientFeedbackRequest($booking);

                // Mark feedback as requested
                $booking->update(['feedback_requested_at' => now()]);

                $feedbackSent++;
                $this->line("✅ Feedback request sent to {$booking->user->name} for booking #{$booking->booking_number}");
            } catch (\Exception $e) {
                $this->error("❌ Failed to send feedback request for booking #{$booking->booking_number}: " . $e->getMessage());
            }
        }

        $this->info("📧 Feedback requests sent for {$feedbackSent} completed rides");
        return Command::SUCCESS;
    }
}
