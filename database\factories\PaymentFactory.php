<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'booking_id' => Booking::factory(),
            'user_id' => User::factory(),
            'amount' => $this->faker->randomFloat(2, 10, 500),
            'payment_method' => $this->faker->randomElement(['card', 'paypal', 'pay_later']),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'cancelled']),
            'transaction_id' => $this->faker->uuid(),
            'payment_details' => [
                'gateway_response' => [
                    'status' => 'success',
                    'transaction_id' => $this->faker->uuid(),
                    'timestamp' => now()->toISOString(),
                ],
                'metadata' => [
                    'ip_address' => $this->faker->ipv4(),
                    'user_agent' => $this->faker->userAgent(),
                ],
            ],
        ];
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the payment failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'payment_details' => [
                'gateway_response' => [
                    'status' => 'failed',
                    'error' => $this->faker->sentence(),
                    'timestamp' => now()->toISOString(),
                ],
            ],
        ]);
    }

    /**
     * Create a PayPal payment.
     */
    public function paypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'paypal',
            'transaction_id' => 'PAYPAL-' . $this->faker->uuid(),
        ]);
    }

    /**
     * Create a card payment.
     */
    public function card(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'card',
            'transaction_id' => 'CARD-' . $this->faker->uuid(),
        ]);
    }

    /**
     * Create a pay later payment.
     */
    public function payLater(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'pay_later',
            'status' => 'pending',
            'transaction_id' => 'PAY_LATER-' . $this->faker->randomNumber(9),
        ]);
    }
}
