<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;

use App\Models\EmailLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class EmailSubmissionController extends Controller
{
    /**
     * Display contact submissions
     */
    public function contactSubmissions(Request $request)
    {
        $query = ContactSubmission::query();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('is_spam')) {
            $query->where('is_spam', $request->boolean('is_spam'));
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%");
            });
        }

        $submissions = $query->orderBy('submitted_at', 'desc')->paginate(20);

        return view('admin.email-submissions.contact', compact('submissions'));
    }



    /**
     * Display incoming emails only (received emails)
     */
    public function incomingEmails(Request $request)
    {
        $query = EmailLog::received(); // Only received emails

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('from_email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $incomingEmails = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get email types for filter dropdown
        $emailTypes = EmailLog::received()
            ->select('type')
            ->distinct()
            ->pluck('type')
            ->filter()
            ->sort()
            ->values();

        return view('admin.email-submissions.incoming', compact('incomingEmails', 'emailTypes'));
    }

    /**
     * Display all email logs (for comprehensive view)
     */
    public function emailLogs(Request $request)
    {
        $query = EmailLog::query();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('direction')) {
            $query->where('direction', $request->direction);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('from_email', 'like', "%{$search}%")
                  ->orWhere('to_email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $logs = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $emailTypes = EmailLog::select('type')->distinct()->pluck('type')->filter()->sort()->values();
        $directions = ['sent', 'received', 'bounced', 'failed'];
        $statuses = ['pending', 'sent', 'delivered', 'bounced', 'failed', 'processed'];

        return view('admin.email-submissions.logs', compact('logs', 'emailTypes', 'directions', 'statuses'));
    }

    /**
     * Show contact submission details
     */
    public function showContactSubmission(ContactSubmission $submission)
    {
        return view('admin.email-submissions.show-contact', compact('submission'));
    }



    /**
     * Update contact submission status
     */
    public function updateContactStatus(Request $request, ContactSubmission $submission): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:new,emailed,email_failed,spam_detected,responded,closed'
        ]);

        $submission->update([
            'status' => $request->status,
            'responded_at' => $request->status === 'responded' ? now() : $submission->responded_at,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully'
        ]);
    }



    /**
     * Mark submission as spam
     */
    public function markAsSpam(ContactSubmission $submission): JsonResponse
    {
        $submission->update([
            'is_spam' => true,
            'status' => 'spam_detected'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Marked as spam successfully'
        ]);
    }

    /**
     * Delete contact submission
     */
    public function deleteContactSubmission(ContactSubmission $submission): JsonResponse
    {
        $submission->delete();

        return response()->json([
            'success' => true,
            'message' => 'Contact submission deleted successfully'
        ]);
    }



    /**
     * Get dashboard statistics
     */
    public function getStats(): JsonResponse
    {
        $stats = [
            'contact_submissions' => [
                'total' => ContactSubmission::count(),
                'new' => ContactSubmission::where('status', 'new')->count(),
                'spam' => ContactSubmission::where('is_spam', true)->count(),
                'today' => ContactSubmission::whereDate('submitted_at', today())->count(),
            ],

            'email_logs' => [
                'total' => EmailLog::count(),
                'sent' => EmailLog::where('direction', 'sent')->count(),
                'received' => EmailLog::where('direction', 'received')->count(),
                'failed' => EmailLog::whereIn('status', ['failed', 'bounced'])->count(),
            ]
        ];

        return response()->json($stats);
    }
}
