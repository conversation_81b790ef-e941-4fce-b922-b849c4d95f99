<?php

namespace App\Services;

use App\Models\EmailLog;
use App\Models\User;
use App\Services\EmailReceivingService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class EmailWebhookService
{
    /**
     * Process incoming email from webhook
     *
     * @param array $webhookData
     * @param string $provider
     * @return array
     */
    public static function processIncomingEmail(array $webhookData, string $provider): array
    {
        try {
            // Parse email data based on provider
            $emailData = self::parseEmailData($webhookData, $provider);
            
            if (!$emailData) {
                return [
                    'success' => false,
                    'message' => 'Failed to parse email data'
                ];
            }

            Log::info('Processing incoming email', [
                'provider' => $provider,
                'from' => $emailData['from'] ?? null,
                'subject' => $emailData['subject'] ?? null,
            ]);

            // Process the email as a reply
            $result = EmailReceivingService::processEmailReply($emailData);

            return $result;

        } catch (\Exception $e) {
            Log::error('Failed to process incoming email', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'data' => $webhookData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process incoming email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process email bounce notification
     *
     * @param array $bounceData
     * @return array
     */
    public static function processBounce(array $bounceData): array
    {
        try {
            $processedCount = 0;
            $bounces = self::extractBounceData($bounceData);

            foreach ($bounces as $bounce) {
                $emailLog = EmailLog::where('to_email', $bounce['email'])
                    ->where('status', '!=', 'bounced')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($emailLog) {
                    $emailLog->update([
                        'status' => 'bounced',
                        'bounced_at' => now(),
                        'error_message' => $bounce['reason'] ?? 'Email bounced',
                    ]);

                    // Mark user email as invalid if hard bounce
                    if (isset($bounce['type']) && $bounce['type'] === 'hard') {
                        self::markEmailAsInvalid($bounce['email'], $bounce['reason'] ?? 'Hard bounce');
                    }

                    $processedCount++;
                }
            }

            Log::info('Email bounces processed', [
                'processed_count' => $processedCount,
                'total_bounces' => count($bounces),
            ]);

            return [
                'success' => true,
                'processed_count' => $processedCount,
                'message' => "Processed {$processedCount} bounces"
            ];

        } catch (\Exception $e) {
            Log::error('Failed to process email bounces', [
                'error' => $e->getMessage(),
                'data' => $bounceData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process bounces: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process email delivery confirmation
     *
     * @param array $deliveryData
     * @return array
     */
    public static function processDelivery(array $deliveryData): array
    {
        try {
            $processedCount = 0;
            $deliveries = self::extractDeliveryData($deliveryData);

            foreach ($deliveries as $delivery) {
                $emailLog = EmailLog::where('to_email', $delivery['email'])
                    ->where('status', 'sent')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($emailLog) {
                    $emailLog->update([
                        'status' => 'delivered',
                        'delivered_at' => $delivery['timestamp'] ?? now(),
                    ]);

                    $processedCount++;
                }
            }

            Log::info('Email deliveries processed', [
                'processed_count' => $processedCount,
                'total_deliveries' => count($deliveries),
            ]);

            return [
                'success' => true,
                'processed_count' => $processedCount,
                'message' => "Processed {$processedCount} deliveries"
            ];

        } catch (\Exception $e) {
            Log::error('Failed to process email deliveries', [
                'error' => $e->getMessage(),
                'data' => $deliveryData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process deliveries: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process email open tracking
     *
     * @param array $openData
     * @return array
     */
    public static function processOpen(array $openData): array
    {
        try {
            // Extract open data
            $email = $openData['email'] ?? null;
            $timestamp = $openData['timestamp'] ?? now();

            if (!$email) {
                return [
                    'success' => false,
                    'message' => 'No email address provided'
                ];
            }

            // Find the email log
            $emailLog = EmailLog::where('to_email', $email)
                ->whereIn('status', ['sent', 'delivered'])
                ->orderBy('created_at', 'desc')
                ->first();

            if ($emailLog) {
                // Update metadata to track opens
                $metadata = $emailLog->metadata ?? [];
                $metadata['opens'] = $metadata['opens'] ?? [];
                $metadata['opens'][] = [
                    'timestamp' => $timestamp,
                    'ip' => $openData['ip'] ?? null,
                    'user_agent' => $openData['user_agent'] ?? null,
                ];

                $emailLog->update([
                    'metadata' => $metadata,
                ]);
            }

            Log::info('Email open tracked', [
                'email' => $email,
                'timestamp' => $timestamp,
            ]);

            return [
                'success' => true,
                'message' => 'Email open tracked successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to track email open', [
                'error' => $e->getMessage(),
                'data' => $openData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to track email open: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Parse email data based on provider
     *
     * @param array $data
     * @param string $provider
     * @return array|null
     */
    private static function parseEmailData(array $data, string $provider): ?array
    {
        switch ($provider) {
            case 'sendgrid':
                return [
                    'from' => $data['from'] ?? null,
                    'to' => $data['to'] ?? null,
                    'subject' => $data['subject'] ?? null,
                    'text' => $data['text'] ?? null,
                    'html' => $data['html'] ?? null,
                    'timestamp' => $data['timestamp'] ?? now()->toISOString(),
                    'provider' => 'sendgrid',
                ];

            case 'mailgun':
                return [
                    'from' => $data['sender'] ?? $data['from'] ?? null,
                    'to' => $data['recipient'] ?? $data['to'] ?? null,
                    'subject' => $data['Subject'] ?? $data['subject'] ?? null,
                    'text' => $data['body-plain'] ?? $data['text'] ?? null,
                    'html' => $data['body-html'] ?? $data['html'] ?? null,
                    'timestamp' => $data['timestamp'] ?? now()->toISOString(),
                    'provider' => 'mailgun',
                ];

            case 'postmark':
                return [
                    'from' => $data['From'] ?? $data['from'] ?? null,
                    'to' => $data['To'] ?? $data['to'] ?? null,
                    'subject' => $data['Subject'] ?? $data['subject'] ?? null,
                    'text' => $data['TextBody'] ?? $data['text'] ?? null,
                    'html' => $data['HtmlBody'] ?? $data['html'] ?? null,
                    'timestamp' => $data['Date'] ?? now()->toISOString(),
                    'provider' => 'postmark',
                ];

            case 'ses':
                // Parse SES SNS message
                $message = json_decode($data['Message'] ?? '{}', true);
                return [
                    'from' => $message['mail']['commonHeaders']['from'][0] ?? null,
                    'to' => $message['mail']['commonHeaders']['to'][0] ?? null,
                    'subject' => $message['mail']['commonHeaders']['subject'] ?? null,
                    'text' => $message['content'] ?? null,
                    'html' => null,
                    'timestamp' => $message['mail']['timestamp'] ?? now()->toISOString(),
                    'provider' => 'ses',
                ];

            default:
                // Generic format
                return [
                    'from' => $data['from'] ?? null,
                    'to' => $data['to'] ?? null,
                    'subject' => $data['subject'] ?? null,
                    'text' => $data['text'] ?? $data['body'] ?? null,
                    'html' => $data['html'] ?? null,
                    'timestamp' => $data['timestamp'] ?? now()->toISOString(),
                    'provider' => 'generic',
                ];
        }
    }

    /**
     * Extract bounce data from webhook
     *
     * @param array $data
     * @return array
     */
    private static function extractBounceData(array $data): array
    {
        // This is a simplified implementation
        // In practice, you'd parse based on the specific provider format
        
        if (isset($data['email'])) {
            return [[
                'email' => $data['email'],
                'reason' => $data['reason'] ?? 'Unknown',
                'type' => $data['type'] ?? 'hard',
                'timestamp' => $data['timestamp'] ?? now(),
            ]];
        }

        return [];
    }

    /**
     * Extract delivery data from webhook
     *
     * @param array $data
     * @return array
     */
    private static function extractDeliveryData(array $data): array
    {
        // This is a simplified implementation
        // In practice, you'd parse based on the specific provider format
        
        if (isset($data['email'])) {
            return [[
                'email' => $data['email'],
                'timestamp' => $data['timestamp'] ?? now(),
            ]];
        }

        return [];
    }

    /**
     * Mark email as invalid due to bounce
     *
     * @param string $email
     * @param string $reason
     * @return void
     */
    private static function markEmailAsInvalid(string $email, string $reason): void
    {
        try {
            // Mark user email as invalid
            User::where('email', $email)->update([
                'email_verified_at' => null,
                // You might want to add an 'email_invalid' field
            ]);

            Log::info('Email marked as invalid due to bounce', [
                'email' => $email,
                'reason' => $reason,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to mark email as invalid', [
                'email' => $email,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
