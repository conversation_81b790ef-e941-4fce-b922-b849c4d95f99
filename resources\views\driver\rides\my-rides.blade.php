@extends('layouts.driver')

@section('title', 'My Rides')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }



    .ride-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s;
    }

    .ride-card:hover {
        transform: translateY(-5px);
    }

    .ride-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px 20px;
    }

    .ride-card .card-body {
        padding: 20px;
    }

    .ride-card .card-footer {
        background-color: #fff;
        border-top: 1px solid #f1f1f1;
        padding: 15px 20px;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-assigned {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-in-progress {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .ride-detail {
        margin-bottom: 10px;
    }

    .ride-detail-label {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .ride-detail-value {
        color: #6c757d;
    }

    .vehicle-img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 5px;
    }

    .client-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .client-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 10px;
    }

    .client-name {
        font-weight: 600;
        margin-bottom: 0;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>My Rides</h2>
            </div>

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link {{ !request('status') || request('status') == 'all' ? 'active' : '' }}" href="{{ route('driver.rides.my-rides', ['status' => 'all']) }}">All Rides</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request('status') == 'upcoming' ? 'active' : '' }}" href="{{ route('driver.rides.my-rides', ['status' => 'upcoming']) }}">Upcoming</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request('status') == 'completed' ? 'active' : '' }}" href="{{ route('driver.rides.my-rides', ['status' => 'completed']) }}">Completed</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request('status') == 'cancelled' ? 'active' : '' }}" href="{{ route('driver.rides.my-rides', ['status' => 'cancelled']) }}">Cancelled</a>
                </li>
            </ul>

            @if ($myRides->isEmpty())
                <div class="alert alert-info">
                    @if(!request('status') || request('status') == 'all')
                        No rides found. Check available rides to accept new bookings.
                    @elseif(request('status') == 'upcoming')
                        No upcoming rides found. Check available rides to accept new bookings.
                    @elseif(request('status') == 'completed')
                        You have not completed any rides yet.
                    @elseif(request('status') == 'cancelled')
                        You have not cancelled any rides.
                    @endif
                </div>
            @else
                <div class="row">
                    @foreach ($myRides as $ride)
                        <div class="col-md-6">
                            <div class="card ride-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Booking #{{ $ride->booking_number }}</h5>
                                    <span class="status-badge status-{{ str_replace('_', '-', $ride->status) }}">
                                        {{ ucfirst(str_replace('_', ' ', $ride->status)) }}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="client-info">
                                        @if ($ride->user->profile_photo)
                                            <img src="{{ asset('storage/' . $ride->user->profile_photo) }}" class="client-img" alt="Client">
                                        @else
                                            <img src="https://via.placeholder.com/40x40?text=Client" class="client-img" alt="Client">
                                        @endif
                                        <p class="client-name">Client #{{ substr($ride->user->id, 0, 5) }}</p>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            @if ($ride->vehicle->image)
                                                <img src="{{ asset('storage/' . $ride->vehicle->image) }}" class="vehicle-img" alt="{{ $ride->vehicle->name }}">
                                            @else
                                                <img src="https://via.placeholder.com/150x120?text=Vehicle" class="vehicle-img" alt="{{ $ride->vehicle->name }}">
                                            @endif
                                        </div>
                                        <div class="col-md-8">
                                            <div class="ride-detail">
                                                <div class="ride-detail-label">Vehicle</div>
                                                <div class="ride-detail-value">{{ $ride->vehicle->name }}</div>
                                            </div>
                                            <div class="ride-detail">
                                                <div class="ride-detail-label">Date & Time</div>
                                                <div class="ride-detail-value">{{ $ride->pickup_date->format('M d, Y h:i A') }}</div>
                                            </div>
                                            <div class="ride-detail">
                                                <div class="ride-detail-label">Pickup</div>
                                                <div class="ride-detail-value">{{ Str::limit($ride->pickup_address, 30) }}</div>
                                            </div>
                                            @if($ride->via_stops && count($ride->via_stops) > 0)
                                                <div class="ride-detail">
                                                    <div class="ride-detail-label">Via Stops</div>
                                                    <div class="ride-detail-value">
                                                        <span class="badge bg-info">{{ count($ride->via_stops) }} stop{{ count($ride->via_stops) > 1 ? 's' : '' }}</span>
                                                    </div>
                                                </div>
                                            @endif
                                            @if ($ride->booking_type !== 'hourly')
                                                <div class="ride-detail">
                                                    <div class="ride-detail-label">Dropoff</div>
                                                    <div class="ride-detail-value">{{ Str::limit($ride->dropoff_address, 30) }}</div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Fare:</strong> @currency(){{ number_format($ride->amount, 2) }}
                                    </div>
                                    <div>
                                        <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-primary">View Details</a>

                                        @if ($ride->status === 'assigned')
                                            <form action="{{ route('driver.rides.start', $ride->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-success">Start Ride</button>
                                            </form>
                                        @endif

                                        @if ($ride->status === 'in_progress')
                                            <a href="{{ route('driver.rides.track', $ride->id) }}" class="btn btn-primary btn-sm me-1">
                                                <i class="fas fa-map-marker-alt"></i> Track
                                            </a>
                                            <form action="{{ route('driver.rides.complete', $ride->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-success btn-sm">Complete</button>
                                            </form>
                                        @endif

                                        @if ($ride->status === 'completed')
                                            <a href="{{ route('driver.rides.summary', $ride->id) }}" class="btn btn-info btn-sm">
                                                <i class="fas fa-file-alt"></i> Summary
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
