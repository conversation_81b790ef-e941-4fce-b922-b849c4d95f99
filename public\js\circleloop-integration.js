/**
 * CircleLoop Integration - Real-time Call Notifications
 * Displays client information when incoming calls are received
 */

class CircleLoopIntegration {
    constructor() {
        this.isEnabled = true;
        this.checkInterval = 5000; // Check every 5 seconds
        this.lastCallCheck = null;
        this.activePopups = new Map();
        this.notificationSound = null;
        
        this.init();
    }

    init() {
        // Initialize notification sound
        this.initNotificationSound();
        
        // Start monitoring for new calls
        this.startCallMonitoring();
        
        // Listen for webhook events (if using WebSockets)
        this.initWebSocketListener();
        
        console.log('CircleLoop Integration initialized');
    }

    initNotificationSound() {
        // Create notification sound
        this.notificationSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    }

    startCallMonitoring() {
        if (!this.isEnabled) return;

        // Poll for new calls every few seconds
        setInterval(() => {
            this.checkForNewCalls();
        }, this.checkInterval);
    }

    async checkForNewCalls() {
        try {
            const response = await fetch('/admin/call-logs/live-call?' + new URLSearchParams({
                since: this.lastCallCheck || new Date(Date.now() - 60000).toISOString()
            }));

            if (response.ok) {
                const data = await response.json();
                
                if (data.new_calls && data.new_calls.length > 0) {
                    data.new_calls.forEach(call => {
                        this.handleIncomingCall(call);
                    });
                }
                
                this.lastCallCheck = new Date().toISOString();
            }
        } catch (error) {
            console.error('Error checking for new calls:', error);
        }
    }

    handleIncomingCall(callData) {
        const phoneNumber = callData.caller_number;
        
        // Don't show multiple popups for the same number
        if (this.activePopups.has(phoneNumber)) {
            return;
        }

        // Play notification sound
        this.playNotificationSound();

        // Show desktop notification
        this.showDesktopNotification(callData);

        // Open call popup window
        this.openCallPopup(callData);

        // Mark as active
        this.activePopups.set(phoneNumber, Date.now());

        // Auto-remove after 30 seconds
        setTimeout(() => {
            this.activePopups.delete(phoneNumber);
        }, 30000);
    }

    playNotificationSound() {
        if (this.notificationSound) {
            this.notificationSound.play().catch(error => {
                console.log('Could not play notification sound:', error);
            });
        }
    }

    showDesktopNotification(callData) {
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                const notification = new Notification('Incoming Call', {
                    body: `Call from ${callData.caller_number}${callData.client_name ? ` (${callData.client_name})` : ''}`,
                    icon: '/favicon.ico',
                    tag: 'incoming-call-' + callData.caller_number,
                    requireInteraction: true
                });

                notification.onclick = () => {
                    window.focus();
                    notification.close();
                };

                // Auto-close after 10 seconds
                setTimeout(() => {
                    notification.close();
                }, 10000);
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        this.showDesktopNotification(callData);
                    }
                });
            }
        }
    }

    openCallPopup(callData) {
        const phoneNumber = callData.caller_number;
        const popupUrl = `/admin/call-logs/live-popup?phone=${encodeURIComponent(phoneNumber)}`;
        
        const popup = window.open(
            popupUrl,
            'call-popup-' + phoneNumber.replace(/[^0-9]/g, ''),
            'width=500,height=700,scrollbars=no,resizable=no,toolbar=no,location=no,directories=no,status=no,menubar=no'
        );

        if (popup) {
            // Center the popup
            const left = (screen.width / 2) - (500 / 2);
            const top = (screen.height / 2) - (700 / 2);
            popup.moveTo(left, top);

            // Focus the popup
            popup.focus();

            // Store reference
            this.activePopups.set(phoneNumber + '_popup', popup);

            // Clean up when popup closes
            const checkClosed = setInterval(() => {
                if (popup.closed) {
                    clearInterval(checkClosed);
                    this.activePopups.delete(phoneNumber + '_popup');
                }
            }, 1000);
        }
    }

    initWebSocketListener() {
        // If you implement WebSocket support later, add it here
        // For now, we're using polling
    }

    // Manual call lookup
    async lookupClient(phoneNumber) {
        try {
            const response = await fetch('/admin/call-logs/client-info?' + new URLSearchParams({
                phone: phoneNumber
            }));

            if (response.ok) {
                const data = await response.json();
                return data;
            }
        } catch (error) {
            console.error('Error looking up client:', error);
        }
        return null;
    }

    // Show manual call popup
    showManualCallPopup(phoneNumber) {
        this.openCallPopup({ caller_number: phoneNumber });
    }

    // Enable/disable monitoring
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log('CircleLoop monitoring', enabled ? 'enabled' : 'disabled');
    }

    // Update check interval
    setCheckInterval(interval) {
        this.checkInterval = interval;
        console.log('CircleLoop check interval set to', interval, 'ms');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on admin pages
    if (window.location.pathname.startsWith('/admin')) {
        window.circleLoopIntegration = new CircleLoopIntegration();

        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Add manual lookup functionality
        window.lookupClientByPhone = function(phoneNumber) {
            if (window.circleLoopIntegration) {
                window.circleLoopIntegration.showManualCallPopup(phoneNumber);
            }
        };

        // Add keyboard shortcut for manual lookup (Ctrl+Shift+C)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                const phoneNumber = prompt('Enter phone number to lookup:');
                if (phoneNumber) {
                    window.lookupClientByPhone(phoneNumber);
                }
            }
        });
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CircleLoopIntegration;
}
