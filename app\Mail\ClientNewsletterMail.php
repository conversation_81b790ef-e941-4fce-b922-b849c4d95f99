<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Services\SettingsService;

class ClientNewsletterMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $newsletterData;
    public $newsletterType;

    /**
     * Create a new message instance.
     */
    public function __construct(array $newsletterData, string $newsletterType = 'general')
    {
        $this->newsletterData = $newsletterData;
        $this->newsletterType = $newsletterType;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->newsletterData['subject'] ?? 'Newsletter from ' . SettingsService::getCompanyName();

        return new Envelope(
            subject: $subject,
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.client-newsletter',
            with: [
                'newsletterData' => $this->newsletterData,
                'newsletterType' => $this->newsletterType,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
