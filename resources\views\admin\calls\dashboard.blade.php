@extends('layouts.admin')

@section('title', 'Call Management Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Call Management Dashboard</h1>
                <div class="btn-group">
                    <a href="{{ route('admin.calls.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> All Calls
                    </a>
                    <a href="{{ route('admin.calls.analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                    <a href="{{ route('admin.calls.settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="testWebhook()" data-action="test-webhook">
                        <i class="fas fa-test-tube"></i> Test Webhook
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Call Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="today-total">{{ $stats['today']['total'] }}</h4>
                            <p class="card-text">Today's Calls</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="today-answered">{{ $stats['today']['answered'] }}</h4>
                            <p class="card-text">Answered</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone-volume fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="today-missed">{{ $stats['today']['missed'] }}</h4>
                            <p class="card-text">Missed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone-slash fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="today-identified">{{ $stats['today']['identified'] }}</h4>
                            <p class="card-text">Identified Clients</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Call Notification Area -->
    <div id="live-call-notification" class="alert alert-info d-none" role="alert">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1">
                    <i class="fas fa-phone-alt fa-spin"></i> Incoming Call
                </h5>
                <p class="mb-0" id="live-call-details">Waiting for call...</p>
            </div>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="showCallPopup()" data-action="show-popup">
                View Details
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Recent Calls -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Calls</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="refreshCalls()" id="refresh-calls-btn" data-action="refresh">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">

                <!-- Inline script to ensure functions are immediately available -->
                <script>
                // Define functions immediately to ensure they're available for onclick
                if (typeof window.refreshCalls === 'undefined') {
                    window.refreshCalls = function() {
                        console.log('Inline refreshCalls called');
                        location.reload();
                    };
                }
                if (typeof window.showCallPopup === 'undefined') {
                    window.showCallPopup = function() {
                        console.log('Inline showCallPopup called');
                        $('#callPopupModal').modal('show');
                    };
                }
                if (typeof window.copyWebhookUrl === 'undefined') {
                    window.copyWebhookUrl = function() {
                        console.log('Inline copyWebhookUrl called');
                        const urlInput = document.getElementById('webhook-url');
                        if (urlInput) {
                            urlInput.select();
                            document.execCommand('copy');

                            // Show feedback
                            const btn = event.target.closest('button');
                            if (btn) {
                                const originalHtml = btn.innerHTML;
                                btn.innerHTML = '<i class="fas fa-check"></i>';
                                btn.classList.add('btn-success');
                                btn.classList.remove('btn-outline-secondary');

                                setTimeout(() => {
                                    btn.innerHTML = originalHtml;
                                    btn.classList.remove('btn-success');
                                    btn.classList.add('btn-outline-secondary');
                                }, 2000);
                            }
                        }
                    };
                }
                if (typeof window.testWebhook === 'undefined') {
                    window.testWebhook = function() {
                        console.log('Inline testWebhook called');
                        fetch('{{ url("/api/circleloop/test") }}')
                        .then(response => response.json())
                        .then(data => {
                            alert('Webhook test successful! Your endpoint is working.');
                        })
                        .catch(error => {
                            alert('Webhook test failed. Please check your configuration.');
                        });
                    };
                }
                console.log('Inline dashboard functions defined');

                // Also add event listeners as backup
                document.addEventListener('DOMContentLoaded', function() {
                    // Refresh calls button
                    const refreshBtn = document.getElementById('refresh-calls-btn');
                    if (refreshBtn) {
                        refreshBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.refreshCalls();
                        });
                    }

                    // Test webhook buttons
                    const testWebhookBtns = document.querySelectorAll('[onclick*="testWebhook"]');
                    testWebhookBtns.forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.testWebhook();
                        });
                    });

                    // Copy webhook URL buttons
                    const copyBtns = document.querySelectorAll('[onclick*="copyWebhookUrl"]');
                    copyBtns.forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.copyWebhookUrl();
                        });
                    });

                    // Show call popup buttons
                    const popupBtns = document.querySelectorAll('[onclick*="showCallPopup"]');
                    popupBtns.forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.showCallPopup();
                        });
                    });

                    console.log('Event listeners attached as backup');

                    // Additional event delegation for data attributes
                    document.addEventListener('click', function(e) {
                        const target = e.target.closest('[data-action]');
                        if (target) {
                            const action = target.getAttribute('data-action');
                            console.log('Data action clicked:', action);

                            switch(action) {
                                case 'refresh':
                                    e.preventDefault();
                                    console.log('Refreshing calls via data attribute');
                                    location.reload();
                                    break;
                                case 'test-webhook':
                                    e.preventDefault();
                                    window.testWebhook();
                                    break;
                                case 'copy-webhook':
                                    e.preventDefault();
                                    window.copyWebhookUrl();
                                    break;
                                case 'show-popup':
                                    e.preventDefault();
                                    window.showCallPopup();
                                    break;
                            }
                        }
                    });
                });
                </script>
                    @if($recentCalls->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Caller</th>
                                        <th>Client</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-calls-tbody">
                                    @foreach($recentCalls as $call)
                                    <tr>
                                        <td>
                                            <small class="text-muted">{{ $call->created_at->format('H:i') }}</small><br>
                                            <small>{{ $call->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ $call->caller_number }}</strong><br>
                                            <span class="badge {{ $call->event_type_badge_class }} text-white">
                                                {{ ucfirst(str_replace('_', ' ', $call->event_type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($call->client)
                                                <strong>{{ $call->client->name }}</strong><br>
                                                <small class="text-muted">{{ $call->client->email }}</small>
                                            @else
                                                <span class="text-muted">Unknown</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $call->status_badge_class }} text-white">
                                                {{ ucfirst($call->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.calls.show', $call) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No calls received yet</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Follow-up Calls & Setup -->
        <div class="col-md-4">
            <!-- Follow-up Calls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Follow-up Required</h5>
                </div>
                <div class="card-body">
                    @if($followUpCalls->count() > 0)
                        @foreach($followUpCalls as $call)
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                            <div>
                                <strong>{{ $call->client_name ?? $call->caller_number }}</strong><br>
                                <small class="text-muted">{{ $call->created_at->format('M j, H:i') }}</small>
                            </div>
                            <a href="{{ route('admin.calls.show', $call) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">No follow-ups needed</p>
                    @endif
                </div>
            </div>

            <!-- CircleLoop Setup -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">CircleLoop Setup</h5>
                </div>
                <div class="card-body">
                    <p class="small text-muted mb-3">Configure your CircleLoop webhook to receive call notifications:</p>
                    
                    <div class="mb-3">
                        <label class="form-label small">Webhook URL:</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm" 
                                   value="{{ url('/api/circleloop/webhook') }}" 
                                   id="webhook-url" readonly>
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyWebhookUrl()" data-action="copy-webhook">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label small">Method:</label>
                        <input type="text" class="form-control form-control-sm" value="POST" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label small">Events to Enable:</label>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success"></i> New Phonecalls</li>
                            <li><i class="fas fa-times text-muted"></i> New Voicemails (optional)</li>
                            <li><i class="fas fa-times text-muted"></i> New SMS Messages (optional)</li>
                        </ul>
                    </div>

                    <button type="button" class="btn btn-sm btn-primary w-100" onclick="testWebhook()" data-action="test-webhook">
                        <i class="fas fa-test-tube"></i> Test Connection
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call Popup Modal -->
<div class="modal fade" id="callPopupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-phone text-primary"></i> Incoming Call
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="call-popup-content">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Dashboard functions - ensuring global scope
$(document).ready(function() {
    console.log('Dashboard script loaded');

    // Ensure functions are in global scope
    window.showCallPopup = function() {
        console.log('showCallPopup called');
        $('#callPopupModal').modal('show');
    };

    window.refreshCalls = function() {
        console.log('refreshCalls called');
        location.reload();
    };

    window.copyWebhookUrl = function() {
        console.log('copyWebhookUrl called');
        const urlInput = document.getElementById('webhook-url');
        urlInput.select();
        document.execCommand('copy');

        // Show feedback
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    };

    window.testWebhook = function() {
        console.log('testWebhook called');
        $.get('{{ url("/api/circleloop/test") }}')
        .done(function(response) {
            alert('Webhook test successful! Your endpoint is working.');
        })
        .fail(function() {
            alert('Webhook test failed. Please check your configuration.');
        });
    };

    // Verify functions are available
    console.log('Functions available:', {
        showCallPopup: typeof window.showCallPopup,
        refreshCalls: typeof window.refreshCalls,
        copyWebhookUrl: typeof window.copyWebhookUrl,
        testWebhook: typeof window.testWebhook
    });
});

// The call monitoring is now handled by the global call-notifications.js system
// No need for duplicate monitoring code here
</script>
@endpush
