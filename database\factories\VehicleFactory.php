<?php

namespace Database\Factories;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vehicle>
 */
class VehicleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Vehicle::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['economy', 'sedan', 'suv', 'professional', 'van', 'limo'];
        $categories = ['economy', 'business', 'professional', 'family'];
        $transmissions = ['automatic', 'manual'];

        return [
            'name' => $this->faker->randomElement(['Economy', 'Standard', 'Premium', 'professional']) . ' ' . 
                     $this->faker->randomElement(['Sedan', 'SUV', 'Van', 'Limousine']),
            'type' => $this->faker->randomElement($types),
            'model' => $this->faker->randomElement(['Toyota Corolla', 'Honda Accord', 'BMW 5 Series', 'Mercedes E-Class', 'Ford Transit', 'Lincoln Continental']),
            'category' => $this->faker->randomElement($categories),
            'seats' => $this->faker->numberBetween(2, 10),
            'luggage_capacity' => $this->faker->numberBetween(1, 5),
            'transmission' => $this->faker->randomElement($transmissions),
            'price_per_km' => $this->faker->randomFloat(2, 1, 5),
            'price_per_hour' => $this->faker->randomFloat(2, 20, 100),
            'description' => $this->faker->paragraph(),
            'is_active' => true,
        ];
    }
}
