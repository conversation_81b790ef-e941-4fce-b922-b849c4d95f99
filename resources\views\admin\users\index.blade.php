@extends('layouts.admin')

@section('title', 'Manage Users')

@section('styles')
<style>
    /* Main Layout */
    .content-wrapper {
        padding: 30px;
        background: #f8f9fa;
        min-height: calc(100vh - 76px);
    }

    /* Header Section */
    .page-header {
        background: #ee393d;
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .page-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .page-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    /* Stats Cards */
    .stats-row {
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        border: none;
        transition: all 0.3s ease;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 15px;
    }

    .stat-icon.client { background: #ee393d; }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }

    .stat-label {
        color: #7f8c8d;
        font-weight: 500;
        margin: 5px 0 0 0;
    }

    /* Filters and Search */
    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .filter-tabs {
        border-bottom: 2px solid #f1f3f4;
        margin-bottom: 20px;
    }

    .filter-tab {
        background: none;
        border: none;
        padding: 15px 25px;
        color: #6c757d;
        font-weight: 600;
        border-radius: 10px 10px 0 0;
        margin-right: 5px;
        transition: all 0.3s ease;
        position: relative;
    }

    .filter-tab.active {
        color: #ee393d;
        background: #f8f9ff;
    }

    .filter-tab.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 2px;
        background: #ee393d;
    }

    .search-container {
        position: relative;
    }

    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 50px;
        padding: 15px 50px 15px 20px;
        font-size: 16px;
        transition: all 0.3s ease;
        width: 100%;
    }

    .search-input:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .search-btn {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background: #ee393d;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* User Cards */
    .users-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .user-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        border: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .user-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: #ee393d;
    }

    .user-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .user-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        object-fit: cover;
        margin-right: 15px;
        border: 3px solid #f8f9fa;
    }

    .user-info h5 {
        margin: 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .user-info p {
        margin: 5px 0 0 0;
        color: #7f8c8d;
        font-size: 14px;
    }

    .user-badges {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .role-badge, .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .role-client { background: #fce4ec; color: #c2185b; }

    .status-active { background: #e8f5e8; color: #2e7d32; }
    .status-inactive { background: #ffebee; color: #c62828; }

    .user-details {
        margin-bottom: 20px;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        color: #7f8c8d;
        font-size: 14px;
        font-weight: 500;
    }

    .detail-value {
        color: #2c3e50;
        font-weight: 600;
        font-size: 14px;
    }

    .user-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
    }

    .btn-view { background: #e3f2fd; color: #1565c0; }
    .btn-view:hover { background: #1565c0; color: white; }

    .btn-edit { background: #fff3e0; color: #ef6c00; }
    .btn-edit:hover { background: #ef6c00; color: white; }

    .btn-toggle { background: #f3e5f5; color: #7b1fa2; }
    .btn-toggle:hover { background: #7b1fa2; color: white; }

    .btn-delete { background: #ffebee; color: #c62828; }
    .btn-delete:hover { background: #c62828; color: white; }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .empty-icon {
        font-size: 4rem;
        color: #bdc3c7;
        margin-bottom: 20px;
    }

    .empty-title {
        font-size: 1.5rem;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .empty-text {
        color: #7f8c8d;
        margin-bottom: 30px;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .users-grid {
            grid-template-columns: 1fr;
        }

        .page-header h1 {
            font-size: 2rem;
        }

        .content-wrapper {
            padding: 20px;
        }
    }

    /* Loading Animation */
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #ee393d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endsection

@section('content')
<div class="content-wrapper">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-users me-3"></i>User Management</h1>
                <p>Manage all users, clients, drivers, and administrators in your system</p>
            </div>
            <div>
                <a href="{{ route('admin.users.create') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>Add New User
                </a>
            </div>
        </div>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {{-- Final cleanup: all admin/driver UI, stats, and badges removed. Only client content remains. --}}

    <!-- Filters and Search -->
    <div class="filters-section">
        <div class="filter-tabs">
            {{-- Only show the client filter tab (active by default) --}}
            <button class="filter-tab active" disabled>
                <i class="fas fa-user me-2"></i>Clients
            </button>
        </div>

        <div class="row align-items-center">
            <div class="col-md-8">
                <form action="{{ route('admin.users.index') }}" method="GET" id="searchForm">
                    <input type="hidden" name="role" value="{{ request('role') }}" id="roleInput">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search users by name, email, or phone..."
                               name="search" value="{{ request('search') }}" id="searchInput">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex justify-content-end gap-2">
                    <button class="btn btn-outline-primary" onclick="exportUsers()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshUsers()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Display -->
    @if ($users->isEmpty())
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3 class="empty-title">No Users Found</h3>
            <p class="empty-text">
                @if(request('search'))
                    No users match your search criteria. Try adjusting your search terms.
                @elseif(request('role'))
                    No {{ request('role') }}s found in the system.
                @else
                    No users have been added to the system yet.
                @endif
            </p>
            <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add First User
            </a>
        </div>
    @else
        <div class="users-grid" id="usersGrid">
            @foreach ($users as $user)
                <div class="user-card" data-user-id="{{ $user->id }}">
                    <div class="user-header">
                        @if ($user->profile_photo)
                            <img src="{{ asset('storage/' . $user->profile_photo) }}" class="user-avatar" alt="{{ $user->name }}">
                        @else
                            <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=667eea&color=fff&size=60" class="user-avatar" alt="{{ $user->name }}">
                        @endif
                        <div class="user-info">
                            <h5>{{ $user->name }}</h5>
                            <p>{{ $user->email }}</p>
                        </div>
                    </div>

                    <div class="user-details">
                        @if($user->phone)
                            <div class="detail-item">
                                <span class="detail-label">Phone</span>
                                <span class="detail-value">{{ $user->phone }}</span>
                            </div>
                        @endif

                        @if($user->role === 'driver')
                            @if($user->license_number)
                                <div class="detail-item">
                                    <span class="detail-label">License</span>
                                    <span class="detail-value">{{ $user->license_number }}</span>
                                </div>
                            @endif
                            @if($user->vehicle_info)
                                <div class="detail-item">
                                    <span class="detail-label">Vehicle</span>
                                    <span class="detail-value">{{ $user->vehicle_info }}</span>
                                </div>
                            @endif
                            <div class="detail-item">
                                <span class="detail-label">Available</span>
                                <span class="detail-value">
                                    <i class="fas fa-circle {{ $user->is_available ? 'text-success' : 'text-danger' }}"></i>
                                    {{ $user->is_available ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        @endif

                        @if($user->role === 'client')
                            <div class="detail-item">
                                <span class="detail-label">Bookings</span>
                                <span class="detail-value">{{ $user->bookings()->count() }}</span>
                            </div>
                        @endif

                        <div class="detail-item">
                            <span class="detail-label">Joined</span>
                            <span class="detail-value">{{ $user->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>

                    <div class="user-actions">
                        <a href="{{ route('admin.users.show', $user->id) }}" class="action-btn btn-view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ route('admin.users.edit', $user->id) }}" class="action-btn btn-edit" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.users.toggle-active', $user->id) }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="action-btn btn-toggle" title="{{ $user->is_active ? 'Deactivate' : 'Activate' }} User">
                                <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }}"></i>
                            </button>
                        </form>
                        <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST" class="d-inline delete-user-form">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="action-btn btn-delete" title="Delete User">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            {{ $users->withQueryString()->links() }}
        </div>
    @endif
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[title]').tooltip();

        // Confirm delete
        $('.delete-user-form').on('submit', function(e) {
            e.preventDefault();
            const form = $(this);
            const userName = form.closest('.user-card').find('.user-info h5').text();

            Swal.fire({
                title: 'Delete User?',
                text: `Are you sure you want to delete "${userName}"? This action cannot be undone!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, Delete User',
                cancelButtonText: 'Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Deleting...',
                        text: 'Please wait while we delete the user.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    form.off('submit').submit();
                }
            });
        });

        // Real-time search
        let searchTimeout;
        $('#searchInput').on('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val();

            searchTimeout = setTimeout(() => {
                if (searchTerm.length >= 2 || searchTerm.length === 0) {
                    $('#searchForm').submit();
                }
            }, 500);
        });

        // Animate cards on load
        $('.user-card').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 500).css('transform', 'translateY(0)');
        });
    });

    // Filter by role function
    function filterByRole(role) {
        $('#roleInput').val(role);
        $('#searchForm').submit();
    }

    // Export users function
    function exportUsers() {
        const role = $('#roleInput').val();
        const search = $('#searchInput').val();

        let url = '{{ route("admin.users.export") }}';
        const params = new URLSearchParams();

        if (role) params.append('role', role);
        if (search) params.append('search', search);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        // Show loading toast
        Swal.fire({
            toast: true,
            position: 'top-end',
            icon: 'info',
            title: 'Preparing export...',
            showConfirmButton: false,
            timer: 2000
        });

        window.location.href = url;
    }

    // Refresh users function
    function refreshUsers() {
        // Add loading spinner to refresh button
        const refreshBtn = $('button[onclick="refreshUsers()"]');
        const originalHtml = refreshBtn.html();
        refreshBtn.html('<div class="loading-spinner"></div> Refreshing...');

        // Reload the page
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    // Toggle user status with AJAX
    function toggleUserStatus(userId, currentStatus) {
        const userCard = $(`.user-card[data-user-id="${userId}"]`);
        const statusBadge = userCard.find('.status-badge');

        $.ajax({
            url: `/admin/users/${userId}/toggle-active`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                statusBadge.html('<div class="loading-spinner"></div>');
            },
            success: function(response) {
                if (response.success) {
                    // Update status badge
                    const newStatus = response.is_active ? 'active' : 'inactive';
                    const newText = response.is_active ? 'Active' : 'Inactive';

                    statusBadge.removeClass('status-active status-inactive')
                              .addClass(`status-${newStatus}`)
                              .text(newText);

                    // Update toggle button
                    const toggleBtn = userCard.find('.btn-toggle i');
                    toggleBtn.removeClass('fa-ban fa-check')
                            .addClass(response.is_active ? 'fa-ban' : 'fa-check');

                    // Show success toast
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        icon: 'success',
                        title: `User ${newText.toLowerCase()}`,
                        showConfirmButton: false,
                        timer: 3000
                    });
                }
            },
            error: function() {
                // Restore original status
                statusBadge.removeClass('status-active status-inactive')
                          .addClass(`status-${currentStatus ? 'active' : 'inactive'}`)
                          .text(currentStatus ? 'Active' : 'Inactive');

                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: 'error',
                    title: 'Failed to update status',
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        });
    }

    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 75) {
            e.preventDefault();
            $('#searchInput').focus();
        }

        // Ctrl/Cmd + N for new user
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 78) {
            e.preventDefault();
            window.location.href = '{{ route("admin.users.create") }}';
        }
    });
</script>
@endsection