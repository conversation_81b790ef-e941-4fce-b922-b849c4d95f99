<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Airport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class PayLaterPaymentTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vehicle;
    protected $airport;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'client',
            'email_verified_at' => now(),
        ]);

        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create([
            'is_active' => true,
        ]);

        // Create test airport
        $this->airport = Airport::factory()->create();

        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);
        
        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });
    }

    /** @test */
    public function it_can_process_pay_later_payment()
    {
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00,
            'distance' => '10.5',
            'duration_value' => 20,
        ];

        // Create booking first
        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);

        // Now test pay later payment
        $paymentData = [
            'payment_method' => 'pay_later',
            'notes' => 'Test pay later payment',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.process-payment', $booking->id), $paymentData);

        $response->assertStatus(200); // Should return view response

        // Verify booking status updated
        $booking->refresh();
        $this->assertEquals('confirmed', $booking->status);
        $this->assertEquals('pending', $booking->payment_status);

        // Verify payment record created
        $payment = Payment::where('booking_id', $booking->id)->first();
        $this->assertNotNull($payment);
        $this->assertEquals('pay_later', $payment->payment_method);
        $this->assertEquals('pending', $payment->status);
        $this->assertEquals($booking->amount, $payment->amount);
        $this->assertStringStartsWith('PAY_LATER-', $payment->transaction_id);

        // Verify payment details
        $paymentDetails = is_string($payment->payment_details)
            ? json_decode($payment->payment_details, true)
            : $payment->payment_details;
        $this->assertIsArray($paymentDetails);
        $this->assertEquals('Test pay later payment', $paymentDetails['notes']);
        $this->assertEquals('pay_later', $paymentDetails['payment_type']);
    }

    /** @test */
    public function pay_later_payment_updates_booking_notes()
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending',
            'notes' => 'Original notes',
        ]);

        $paymentData = [
            'payment_method' => 'pay_later',
            'notes' => 'Special payment instructions',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.process-payment', $booking->id), $paymentData);

        $response->assertStatus(200);

        // Check booking notes were updated
        $booking->refresh();
        $this->assertStringContainsString('Pay Later Notes: Special payment instructions', $booking->notes);
        $this->assertStringContainsString('Original notes', $booking->notes);
    }
}
