@extends('layouts.admin')

@section('title', 'View Email Template')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📄 {{ $template->name }}</h1>
            <p class="text-muted">{{ $template->description ?: 'Email template details' }}</p>
        </div>
        <div>
            <a href="{{ route('admin.email.templates.preview', $template) }}" 
               class="btn btn-info me-2" target="_blank">
                <i class="fas fa-eye me-1"></i> Preview
            </a>
            <a href="{{ route('admin.email.templates.edit', $template) }}" class="btn btn-warning me-2">
                <i class="fas fa-edit me-1"></i> Edit
            </a>
            <a href="{{ route('admin.email.templates.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Templates
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Template Content -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📧 Email Content</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-primary">Subject:</h6>
                        <div class="p-3 bg-light rounded">
                            <code>{{ $template->subject }}</code>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary">Content:</h6>
                        <div class="p-3 bg-light rounded" style="white-space: pre-wrap; font-family: monospace;">{{ $template->content }}</div>
                    </div>

                    @if($template->variables && count($template->variables) > 0)
                        <div class="mb-4">
                            <h6 class="text-primary">Available Variables:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($template->variables as $variable)
                                    <span class="badge bg-primary">{{{ $variable }}}</span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Preview with Sample Data -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">👁️ Preview with Sample Data</h6>
                </div>
                <div class="card-body">
                    <div class="border rounded p-4" style="background-color: #f8f9fa;">
                        <div class="mb-3">
                            <strong>Subject:</strong>
                            <div class="mt-1 p-2 bg-white rounded border">
                                {{ $template->renderSubject($template->getPreviewData()) }}
                            </div>
                        </div>
                        
                        <div>
                            <strong>Content:</strong>
                            <div class="mt-1 p-3 bg-white rounded border" style="white-space: pre-wrap;">
                                {{ $template->renderContent($template->getPreviewData()) }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Sample Data Used:</strong>
                            @foreach($template->getPreviewData() as $key => $value)
                                <code>{{{ $key }}}</code> = "{{ $value }}"{{ !$loop->last ? ', ' : '' }}
                            @endforeach
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Template Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📊 Template Information</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-end">
                                <span class="badge {{ $template->type_badge_class }} fs-6">{{ ucfirst($template->type) }}</span>
                                <div><small class="text-muted">Type</small></div>
                            </div>
                        </div>
                        <div class="col-6">
                            <span class="badge {{ $template->status_badge_class }} fs-6">
                                {{ $template->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <div><small class="text-muted">Status</small></div>
                        </div>
                    </div>

                    <table class="table table-sm table-borderless">
                        <tr>
                            <td class="text-muted">Category:</td>
                            <td>
                                @if($template->category)
                                    <span class="badge bg-secondary">{{ ucfirst($template->category) }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="text-muted">Slug:</td>
                            <td><code>{{ $template->slug }}</code></td>
                        </tr>
                        <tr>
                            <td class="text-muted">System Template:</td>
                            <td>
                                @if($template->is_system)
                                    <i class="fas fa-check text-success"></i> Yes
                                @else
                                    <i class="fas fa-times text-danger"></i> No
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="text-muted">Created:</td>
                            <td>{{ $template->created_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        <tr>
                            <td class="text-muted">Updated:</td>
                            <td>{{ $template->updated_at->format('M j, Y g:i A') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">⚡ Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.email.templates.edit', $template) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit Template
                        </a>
                        <a href="{{ route('admin.email.templates.preview', $template) }}" 
                           class="btn btn-info btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i> Full Preview
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="duplicateTemplate()">
                            <i class="fas fa-copy me-1"></i> Duplicate Template
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleStatus()">
                            <i class="fas fa-power-off me-1"></i> 
                            {{ $template->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                        @if(!$template->is_system)
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteTemplate()">
                                <i class="fas fa-trash me-1"></i> Delete Template
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Usage Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">💡 Usage Information</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <h6 class="text-primary">When is this template used?</h6>
                        @if($template->description)
                            <p>{{ $template->description }}</p>
                        @endif

                        @switch($template->slug)
                            @case('booking_confirmation')
                                <p>This template is automatically sent when a booking is confirmed by the system or admin.</p>
                                @break
                            @case('booking_reminder')
                                <p>This template is sent as a reminder before the scheduled pickup time.</p>
                                @break
                            @case('payment_receipt')
                                <p>This template is sent after a successful payment is processed.</p>
                                @break
                            @case('driver_assignment')
                                <p>This template is sent to drivers when they are assigned to a booking.</p>
                                @break
                            @case('welcome_client')
                                <p>This template is sent to new clients when they register an account.</p>
                                @break
                            @case('welcome_driver')
                                <p>This template is sent to new drivers when their account is created.</p>
                                @break
                            @default
                                <p>This is a custom template that can be used for email campaigns or manual sending.</p>
                        @endswitch

                        <h6 class="text-primary mt-3">Template Variables:</h6>
                        @if($template->variables && count($template->variables) > 0)
                            <ul class="mb-0">
                                @foreach($template->variables as $variable)
                                    <li><code>{{{ $variable }}}</code></li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted mb-0">No variables defined for this template.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function duplicateTemplate() {
    if (confirm('Are you sure you want to duplicate this template?')) {
        $.post('{{ route("admin.email.templates.duplicate", $template) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function() {
            window.location.href = '{{ route("admin.email.templates.index") }}';
        }).fail(function() {
            alert('Error duplicating template');
        });
    }
}

function toggleStatus() {
    const action = {{ $template->is_active ? 'false' : 'true' }} ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this template?`)) {
        $.post('{{ route("admin.email.templates.toggle-status", $template) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function() {
            location.reload();
        }).fail(function() {
            alert('Error toggling template status');
        });
    }
}

function deleteTemplate() {
    if (confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
        const form = $('<form>', {
            method: 'POST',
            action: '{{ route("admin.email.templates.destroy", $template) }}'
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_method',
            value: 'DELETE'
        }));
        
        $('body').append(form);
        form.submit();
    }
}
</script>
@endpush
