<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Booking;
use App\Services\SettingsService;

class AdminBookingAlertMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $booking;
    public $alertType;
    public $alertData;

    /**
     * Create a new message instance.
     */
    public function __construct(Booking $booking, string $alertType, array $alertData = [])
    {
        $this->booking = $booking;
        $this->alertType = $alertType;
        $this->alertData = $alertData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $alertTitles = [
            'high_value' => '💎 High-Value Booking Alert',
            'urgent_assignment' => '🚨 Urgent Driver Assignment Needed',
            'cancellation' => '❌ Booking Cancellation Alert',
            'payment_failed' => '💳 Payment Failed Alert',
            'client_complaint' => '😟 Client Complaint Alert',
            'no_driver_available' => '🚗 No Driver Available Alert',
            'last_minute' => '⏰ Last-Minute Booking Alert',
        ];

        $title = $alertTitles[$this->alertType] ?? '📋 Booking Alert';

        return new Envelope(
            subject: $title . ' - Booking #' . $this->booking->booking_number,
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.admin-booking-alert',
            with: [
                'booking' => $this->booking,
                'alertType' => $this->alertType,
                'alertData' => $this->alertData,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
