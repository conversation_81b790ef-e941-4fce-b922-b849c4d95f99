<?php if($client): ?>
    <!-- Client Identified -->
    <div class="alert alert-success">
        <h5 class="alert-heading">
            <i class="fas fa-user-check"></i> Client Identified
        </h5>
        <p class="mb-0">This caller is a known client in your system.</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h6>Client Information</h6>
            <table class="table table-borderless table-sm">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td><?php echo e($client->name); ?></td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td><?php echo e($client->email); ?></td>
                </tr>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td><?php echo e($client->phone); ?></td>
                </tr>
                <tr>
                    <td><strong>Address:</strong></td>
                    <td><?php echo e($client->address ?? 'N/A'); ?></td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Client History</h6>
            <table class="table table-borderless table-sm">
                <tr>
                    <td><strong>Member Since:</strong></td>
                    <td><?php echo e($client->created_at->format('M j, Y')); ?></td>
                </tr>
                <tr>
                    <td><strong>Total Bookings:</strong></td>
                    <td><?php echo e($client->bookings()->count()); ?></td>
                </tr>
                <tr>
                    <td><strong>Last Booking:</strong></td>
                    <td>
                        <?php $lastBooking = $client->bookings()->latest()->first(); ?>
                        <?php echo e($lastBooking ? $lastBooking->created_at->format('M j, Y') : 'None'); ?>

                    </td>
                </tr>
                <tr>
                    <td><strong>Total Spent:</strong></td>
                    <td>£<?php echo e(number_format($client->bookings()->where('status', 'completed')->sum('amount'), 2)); ?></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Recent Bookings -->
    <?php $recentBookings = $client->bookings()->latest()->limit(3)->get(); ?>
    <?php if($recentBookings->count() > 0): ?>
        <hr>
        <h6>Recent Bookings</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>From</th>
                        <th>To</th>
                        <th>Status</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $recentBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($booking->pickup_date->format('M j, Y')); ?></td>
                        <td><?php echo e($booking->pickup_address); ?></td>
                        <td><?php echo e($booking->destination_address); ?></td>
                        <td>
                            <span class="badge bg-<?php echo e($booking->status === 'completed' ? 'success' : ($booking->status === 'cancelled' ? 'danger' : 'warning')); ?>">
                                <?php echo e(ucfirst($booking->status)); ?>

                            </span>
                        </td>
                        <td>£<?php echo e(number_format($booking->amount, 2)); ?></td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <hr>
    <div class="d-flex gap-2">
        <a href="<?php echo e(route('admin.users.show', $client)); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
            <i class="fas fa-user"></i> View Profile
        </a>
        <a href="<?php echo e(route('admin.bookings.create', ['client_id' => $client->id])); ?>" class="btn btn-outline-success btn-sm" target="_blank">
            <i class="fas fa-plus"></i> New Booking
        </a>
        <a href="mailto:<?php echo e($client->email); ?>" class="btn btn-outline-info btn-sm">
            <i class="fas fa-envelope"></i> Email
        </a>
    </div>

<?php else: ?>
    <!-- Unknown Caller -->
    <div class="alert alert-warning">
        <h5 class="alert-heading">
            <i class="fas fa-user-question"></i> Unknown Caller
        </h5>
        <p class="mb-0">This phone number is not associated with any client in your system.</p>
    </div>

    <div class="text-center py-4">
        <h4><?php echo e($phoneNumber); ?></h4>
        <p class="text-muted">Caller not identified</p>
        
        <div class="d-flex gap-2 justify-content-center">
            <button class="btn btn-primary" onclick="createNewClient()">
                <i class="fas fa-user-plus"></i> Create New Client
            </button>
            <button class="btn btn-outline-secondary" onclick="searchExistingClients()">
                <i class="fas fa-search"></i> Search Existing
            </button>
        </div>
    </div>

    <!-- Quick Client Creation Form -->
    <div id="quickClientForm" class="d-none mt-4">
        <hr>
        <h6>Quick Client Creation</h6>
        <form id="newClientForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Phone</label>
                <input type="text" name="phone" class="form-control" value="<?php echo e($phoneNumber); ?>" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Address (Optional)</label>
                <textarea name="address" class="form-control" rows="2"></textarea>
            </div>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-save"></i> Create Client
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideClientForm()">
                Cancel
            </button>
        </form>
    </div>
<?php endif; ?>

<script>
// Ensure functions are in global scope
window.createNewClient = function() {
    $('#quickClientForm').removeClass('d-none');
};

window.hideClientForm = function() {
    $('#quickClientForm').addClass('d-none');
};

window.searchExistingClients = function() {
    // This would open a search modal or redirect to client search
    window.open('/admin/users?role=client&search=<?php echo e($phoneNumber); ?>', '_blank');
};

$('#newClientForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        name: $(this).find('input[name="name"]').val(),
        email: $(this).find('input[name="email"]').val(),
        phone: $(this).find('input[name="phone"]').val(),
        address: $(this).find('textarea[name="address"]').val(),
        role: 'client',
        _token: '<?php echo e(csrf_token()); ?>'
    };
    
    $.ajax({
        url: '/admin/users',
        method: 'POST',
        data: formData,
        success: function(response) {
            alert('Client created successfully!');
            // Reload the popup with the new client info
            location.reload();
        },
        error: function(xhr) {
            alert('Failed to create client: ' + xhr.responseJSON.message);
        }
    });
});
</script>
<?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/calls/live-popup.blade.php ENDPATH**/ ?>