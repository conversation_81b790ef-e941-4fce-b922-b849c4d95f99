<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class MonitorSystemHealth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:monitor-system-health';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor system health and send alerts to administrators';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Monitoring system health...');

        $healthChecks = [
            'database' => $this->checkDatabase(),
            'email_queue' => $this->checkEmailQueue(),
            'storage' => $this->checkStorage(),
            'cache' => $this->checkCache(),
            'failed_jobs' => $this->checkFailedJobs(),
        ];

        $criticalIssues = [];
        $warnings = [];

        foreach ($healthChecks as $component => $result) {
            if ($result['status'] === 'critical') {
                $criticalIssues[] = $result;
            } elseif ($result['status'] === 'warning') {
                $warnings[] = $result;
            }
        }

        // Send critical alerts immediately
        if (!empty($criticalIssues)) {
            foreach ($criticalIssues as $issue) {
                $this->sendSystemAlert($issue['component'], $issue, 'critical');
            }
        }

        // Send warning alerts (but not too frequently)
        if (!empty($warnings)) {
            $lastWarningAlert = Cache::get('last_warning_alert', null);
            if (!$lastWarningAlert || Carbon::parse($lastWarningAlert)->diffInHours(now()) >= 4) {
                foreach ($warnings as $warning) {
                    $this->sendSystemAlert($warning['component'], $warning, 'medium');
                }
                Cache::put('last_warning_alert', now(), 60 * 60 * 4); // 4 hours
            }
        }

        // Display results
        $this->displayHealthStatus($healthChecks);

        return empty($criticalIssues) ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Check database connectivity and performance
     */
    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $responseTime = (microtime(true) - $start) * 1000;

            if ($responseTime > 1000) {
                return [
                    'component' => 'database',
                    'status' => 'warning',
                    'message' => 'Database response time is slow',
                    'details' => ['response_time' => round($responseTime, 2) . 'ms'],
                ];
            }

            return [
                'component' => 'database',
                'status' => 'healthy',
                'message' => 'Database connection is healthy',
                'details' => ['response_time' => round($responseTime, 2) . 'ms'],
            ];
        } catch (\Exception $e) {
            return [
                'component' => 'database',
                'status' => 'critical',
                'message' => 'Database connection failed',
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check email queue status
     */
    private function checkEmailQueue(): array
    {
        try {
            $queueSize = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            if ($queueSize > 100) {
                return [
                    'component' => 'email_queue',
                    'status' => 'warning',
                    'message' => 'Email queue is backing up',
                    'details' => [
                        'pending_jobs' => $queueSize,
                        'failed_jobs' => $failedJobs,
                    ],
                ];
            }

            return [
                'component' => 'email_queue',
                'status' => 'healthy',
                'message' => 'Email queue is processing normally',
                'details' => [
                    'pending_jobs' => $queueSize,
                    'failed_jobs' => $failedJobs,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'component' => 'email_queue',
                'status' => 'critical',
                'message' => 'Cannot access email queue',
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check storage space
     */
    private function checkStorage(): array
    {
        try {
            $storagePath = storage_path();
            $freeBytes = disk_free_space($storagePath);
            $totalBytes = disk_total_space($storagePath);
            $usedPercentage = (($totalBytes - $freeBytes) / $totalBytes) * 100;

            if ($usedPercentage > 90) {
                return [
                    'component' => 'storage',
                    'status' => 'critical',
                    'message' => 'Storage space critically low',
                    'details' => [
                        'used_percentage' => round($usedPercentage, 1) . '%',
                        'free_space' => $this->formatBytes($freeBytes),
                    ],
                ];
            } elseif ($usedPercentage > 80) {
                return [
                    'component' => 'storage',
                    'status' => 'warning',
                    'message' => 'Storage space running low',
                    'details' => [
                        'used_percentage' => round($usedPercentage, 1) . '%',
                        'free_space' => $this->formatBytes($freeBytes),
                    ],
                ];
            }

            return [
                'component' => 'storage',
                'status' => 'healthy',
                'message' => 'Storage space is adequate',
                'details' => [
                    'used_percentage' => round($usedPercentage, 1) . '%',
                    'free_space' => $this->formatBytes($freeBytes),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'component' => 'storage',
                'status' => 'warning',
                'message' => 'Cannot check storage space',
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check cache functionality
     */
    private function checkCache(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);

            if ($retrieved !== $testValue) {
                return [
                    'component' => 'cache',
                    'status' => 'warning',
                    'message' => 'Cache is not working properly',
                    'details' => ['test_result' => 'failed'],
                ];
            }

            return [
                'component' => 'cache',
                'status' => 'healthy',
                'message' => 'Cache is working properly',
                'details' => ['test_result' => 'passed'],
            ];
        } catch (\Exception $e) {
            return [
                'component' => 'cache',
                'status' => 'warning',
                'message' => 'Cache check failed',
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check failed jobs
     */
    private function checkFailedJobs(): array
    {
        try {
            $failedJobsCount = DB::table('failed_jobs')->count();
            $recentFailures = DB::table('failed_jobs')
                ->where('failed_at', '>', Carbon::now()->subHour())
                ->count();

            if ($recentFailures > 10) {
                return [
                    'component' => 'failed_jobs',
                    'status' => 'critical',
                    'message' => 'High number of recent job failures',
                    'details' => [
                        'total_failed' => $failedJobsCount,
                        'recent_failures' => $recentFailures,
                    ],
                ];
            } elseif ($failedJobsCount > 50) {
                return [
                    'component' => 'failed_jobs',
                    'status' => 'warning',
                    'message' => 'Accumulated failed jobs need attention',
                    'details' => [
                        'total_failed' => $failedJobsCount,
                        'recent_failures' => $recentFailures,
                    ],
                ];
            }

            return [
                'component' => 'failed_jobs',
                'status' => 'healthy',
                'message' => 'Job failure rate is normal',
                'details' => [
                    'total_failed' => $failedJobsCount,
                    'recent_failures' => $recentFailures,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'component' => 'failed_jobs',
                'status' => 'warning',
                'message' => 'Cannot check failed jobs',
                'details' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Send system alert
     */
    private function sendSystemAlert(string $component, array $healthData, string $severity): void
    {
        try {
            $alertData = [
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'affected_component' => $component,
                'description' => $healthData['message'],
                'technical_details' => $healthData['details'],
                'recommended_actions' => $this->getRecommendedActions($component, $healthData),
            ];

            EmailService::sendAdminSystemAlert($component . '_health', $alertData, $severity);
            $this->warn("🚨 {$severity} alert sent for {$component}: {$healthData['message']}");
        } catch (\Exception $e) {
            $this->error("Failed to send system alert: " . $e->getMessage());
        }
    }

    /**
     * Get recommended actions for component issues
     */
    private function getRecommendedActions(string $component, array $healthData): array
    {
        $actions = [
            'database' => [
                'Check database server status and connectivity',
                'Review slow query logs and optimize if needed',
                'Monitor database server resources (CPU, memory, disk)',
                'Consider scaling database if performance issues persist',
            ],
            'email_queue' => [
                'Check queue worker processes are running',
                'Review failed jobs and retry if appropriate',
                'Monitor email service provider status',
                'Consider increasing queue worker capacity',
            ],
            'storage' => [
                'Clean up old log files and temporary files',
                'Archive or delete old backup files',
                'Monitor disk usage trends',
                'Plan for storage expansion if needed',
            ],
            'cache' => [
                'Check cache service (Redis/Memcached) status',
                'Clear cache if corrupted',
                'Review cache configuration',
                'Monitor cache hit rates and performance',
            ],
            'failed_jobs' => [
                'Review failed job logs for patterns',
                'Retry failed jobs if issues are resolved',
                'Fix underlying issues causing job failures',
                'Consider job timeout and retry configurations',
            ],
        ];

        return $actions[$component] ?? ['Investigate the issue and take appropriate action'];
    }

    /**
     * Display health status in console
     */
    private function displayHealthStatus(array $healthChecks): void
    {
        $this->info("\n📊 System Health Status:");

        foreach ($healthChecks as $component => $result) {
            $icon = match($result['status']) {
                'healthy' => '✅',
                'warning' => '⚠️',
                'critical' => '🔴',
                default => '❓'
            };

            $this->line("{$icon} {$component}: {$result['message']}");
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
