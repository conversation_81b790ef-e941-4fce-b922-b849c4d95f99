<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Vehicle;
use App\Models\Airport;
use App\Models\Booking;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;

class FlightDetailsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vehicle;
    protected $airport;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'client',
            'email_verified_at' => now(),
        ]);

        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create([
            'is_active' => true,
            'base_fare' => 50.00,
        ]);

        // Create test airport
        $this->airport = Airport::factory()->test()->create();
    }

    /** @test */
    public function it_can_create_airport_transfer_booking_with_flight_details()
    {
        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $flightData = [
            'flight_number' => 'BA123',
            'airline' => 'British Airways',
            'departure_time' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'arrival_time' => Carbon::now()->addDays(1)->addHours(2)->format('Y-m-d H:i:s'),
            'terminal' => 'Terminal 1',
            'flight_status' => 'scheduled',
            'flight_notes' => 'Test flight notes',
        ];

        $bookingData = [
            'booking_type' => 'airport_transfer',
            'airport_direction' => 'from_airport',
            'airport_id' => $this->airport->id,
            'airport_dropoff_address' => '123 Test Street, Test City', // Changed from dropoff_address
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 10:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 75.00,
            'distance' => '15.5',
            'duration_value' => 25,
        ];

        // Merge flight data with booking data
        $bookingData = array_merge($bookingData, $flightData);

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        // Verify booking was created with flight details
        $booking = Booking::where('user_id', $this->user->id)->first();

        $this->assertNotNull($booking);
        $this->assertEquals('airport_transfer', $booking->booking_type);
        $this->assertEquals('BA123', $booking->flight_number);
        $this->assertEquals('British Airways', $booking->airline);
        $this->assertEquals('Terminal 1', $booking->terminal);
        $this->assertEquals('scheduled', $booking->flight_status);
        $this->assertEquals('Test flight notes', $booking->flight_notes);
        $this->assertNotNull($booking->departure_time);
        $this->assertNotNull($booking->arrival_time);
    }

    /** @test */
    public function it_can_create_airport_transfer_booking_without_flight_details()
    {
        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $bookingData = [
            'booking_type' => 'airport_transfer',
            'airport_direction' => 'to_airport',
            'airport_id' => $this->airport->id,
            'airport_pickup_address' => '123 Test Street, Test City', // Changed from pickup_address
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 10:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 75.00,
            'distance' => '15.5',
            'duration_value' => 25,
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        // Verify booking was created without flight details
        $booking = Booking::where('user_id', $this->user->id)->first();

        $this->assertNotNull($booking);
        $this->assertEquals('airport_transfer', $booking->booking_type);
        $this->assertNull($booking->flight_number);
        $this->assertNull($booking->airline);
        $this->assertNull($booking->terminal);
        $this->assertNull($booking->flight_status);
        $this->assertNull($booking->flight_notes);
        $this->assertNull($booking->departure_time);
        $this->assertNull($booking->arrival_time);
    }

    /** @test */
    public function it_validates_flight_details_fields()
    {
        $bookingData = [
            'booking_type' => 'airport_transfer',
            'airport_direction' => 'from_airport',
            'airport_id' => $this->airport->id,
            'pickup_address' => $this->airport->name,
            'dropoff_address' => '123 Test Street, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 10:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 75.00,
            'distance' => '15.5',
            'duration_value' => 25,
            // Invalid flight details
            'flight_number' => str_repeat('A', 25), // Too long
            'airline' => str_repeat('B', 105), // Too long
            'terminal' => str_repeat('C', 55), // Too long
            'flight_status' => 'invalid_status',
            'flight_notes' => str_repeat('D', 505), // Too long
            'departure_time' => 'invalid-date',
            'arrival_time' => 'invalid-date',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertSessionHasErrors([
            'flight_number',
            'airline', 
            'terminal',
            'flight_status',
            'flight_notes',
            'departure_time',
            'arrival_time',
        ]);
    }

    /** @test */
    public function booking_model_helper_methods_work_correctly()
    {
        $booking = Booking::factory()->create([
            'booking_type' => 'airport_transfer',
            'flight_number' => 'BA123',
            'airline' => 'British Airways',
            'flight_status' => 'delayed',
        ]);

        $this->assertTrue($booking->hasFlightDetails());
        $this->assertTrue($booking->isAirportTransfer());
        $this->assertEquals('British Airways BA123', $booking->formatted_flight_info);
        $this->assertEquals('badge-warning', $booking->flight_status_badge_class);

        // Test booking without flight details
        $bookingWithoutFlight = Booking::factory()->create([
            'booking_type' => 'one_way',
            'flight_number' => null,
            'airline' => null,
        ]);

        $this->assertFalse($bookingWithoutFlight->hasFlightDetails());
        $this->assertFalse($bookingWithoutFlight->isAirportTransfer());
        $this->assertNull($bookingWithoutFlight->formatted_flight_info);
    }

    /** @test */
    public function admin_can_create_booking_with_flight_details()
    {
        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $admin = User::factory()->create(['role' => 'admin']);

        $flightData = [
            'flight_number' => 'EK456',
            'airline' => 'Emirates',
            'departure_time' => Carbon::now()->addDays(2)->format('Y-m-d\TH:i'),
            'arrival_time' => Carbon::now()->addDays(2)->addHours(3)->format('Y-m-d\TH:i'),
            'terminal' => 'Terminal 3',
            'flight_status' => 'boarding',
            'flight_notes' => 'VIP passenger',
        ];

        $bookingData = [
            'client_type' => 'existing', // Required by admin controller
            'existing_client_id' => $this->user->id, // Required when client_type is 'existing'
            'assign_driver' => false, // Required by admin controller
            'booking_type' => 'airport_transfer',
            'airport_direction' => 'to_airport',
            'airport_id' => $this->airport->id,
            'pickup_address' => '456 Admin Street, Admin City', // Admin controller uses pickup_address
            'dropoff_address' => $this->airport->name, // Admin controller uses dropoff_address
            'pickup_date' => Carbon::now()->addDays(2)->format('Y-m-d'),
            'pickup_time' => '14:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 85.00,
            'distance' => '20.0',
            'duration_value' => 30,
            'status' => 'confirmed',
        ];

        $bookingData = array_merge($bookingData, $flightData);

        $response = $this->actingAs($admin)
            ->post(route('admin.bookings.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();
        
        $this->assertNotNull($booking);
        $this->assertEquals('EK456', $booking->flight_number);
        $this->assertEquals('Emirates', $booking->airline);
        $this->assertEquals('Terminal 3', $booking->terminal);
        $this->assertEquals('boarding', $booking->flight_status);
        $this->assertEquals('VIP passenger', $booking->flight_notes);
    }

    /** @test */
    public function it_can_create_one_way_booking_with_flight_details()
    {
        // Mock the PayPal services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $flightData = [
            'oneway_flight_number' => 'LH789',
            'oneway_airline' => 'Lufthansa',
            'oneway_departure_time' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'oneway_arrival_time' => Carbon::now()->addDays(1)->addHours(1)->format('Y-m-d H:i:s'),
            'oneway_terminal' => 'Terminal 2',
            'oneway_flight_status' => 'scheduled',
            'oneway_flight_notes' => 'One way flight notes',
        ];

        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Pickup Street, Test City',
            'dropoff_address' => '456 Dropoff Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 09:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 45.00,
            'distance' => '10.5',
            'duration_value' => 20,
        ];

        $bookingData = array_merge($bookingData, $flightData);

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();

        $this->assertNotNull($booking);
        $this->assertEquals('one_way', $booking->booking_type);
        $this->assertEquals('LH789', $booking->flight_number);
        $this->assertEquals('Lufthansa', $booking->airline);
        $this->assertEquals('Terminal 2', $booking->terminal);
        $this->assertEquals('scheduled', $booking->flight_status);
        $this->assertEquals('One way flight notes', $booking->flight_notes);
    }

    /** @test */
    public function it_can_create_return_booking_with_flight_details()
    {
        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $flightData = [
            'return_flight_number' => 'AF101',
            'return_airline' => 'Air France',
            'return_departure_time' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'return_arrival_time' => Carbon::now()->addDays(1)->addHours(2)->format('Y-m-d H:i:s'),
            'return_terminal' => 'Terminal 1',
            'return_flight_status' => 'delayed',
            'return_flight_notes' => 'Return flight notes',
        ];

        $bookingData = [
            'booking_type' => 'return',
            'return_pickup_address' => '789 Return Pickup Street, Test City', // Changed from pickup_address
            'return_dropoff_address' => '321 Return Dropoff Avenue, Test City', // Changed from dropoff_address
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 11:00',
            'return_datetime' => Carbon::now()->addDays(3)->format('Y-m-d') . ' 15:00',
            'vehicle_id' => $this->vehicle->id,
            'amount' => 90.00,
            'distance' => '25.0',
            'duration_value' => 35,
        ];

        $bookingData = array_merge($bookingData, $flightData);

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();

        $this->assertNotNull($booking);
        $this->assertEquals('return', $booking->booking_type);
        $this->assertEquals('AF101', $booking->flight_number);
        $this->assertEquals('Air France', $booking->airline);
        $this->assertEquals('Terminal 1', $booking->terminal);
        $this->assertEquals('delayed', $booking->flight_status);
        $this->assertEquals('Return flight notes', $booking->flight_notes);
    }

    /** @test */
    public function it_can_create_hourly_booking_with_flight_details()
    {
        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);

        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });

        $flightData = [
            'hourly_flight_number' => 'KL202',
            'hourly_airline' => 'KLM',
            'hourly_departure_time' => Carbon::now()->addDays(1)->format('Y-m-d H:i:s'),
            'hourly_arrival_time' => Carbon::now()->addDays(1)->addHours(1)->format('Y-m-d H:i:s'),
            'hourly_terminal' => 'Terminal 3',
            'hourly_flight_status' => 'boarding',
            'hourly_flight_notes' => 'Hourly service for airport pickup',
        ];

        $bookingData = [
            'booking_type' => 'hourly',
            'pickup_address' => '555 Hourly Service Location, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d') . ' 13:00',
            'duration_hours' => 3,
            'vehicle_id' => $this->vehicle->id,
            'amount' => 120.00,
        ];

        $bookingData = array_merge($bookingData, $flightData);

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();

        $this->assertNotNull($booking);
        $this->assertEquals('hourly', $booking->booking_type);
        $this->assertEquals('KL202', $booking->flight_number);
        $this->assertEquals('KLM', $booking->airline);
        $this->assertEquals('Terminal 3', $booking->terminal);
        $this->assertEquals('boarding', $booking->flight_status);
        $this->assertEquals('Hourly service for airport pickup', $booking->flight_notes);
    }
}
