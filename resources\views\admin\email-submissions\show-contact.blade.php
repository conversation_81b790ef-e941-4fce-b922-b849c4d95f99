@extends('layouts.admin')

@section('title', 'Contact Submission Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">📝 Contact Submission #{{ $submission->id }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.email-submissions.contact') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Contact Forms
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Contact Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">👤 Contact Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td>{{ $submission->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td>
                                                <a href="mailto:{{ $submission->email }}" class="text-decoration-none">
                                                    {{ $submission->email }}
                                                </a>
                                            </td>
                                        </tr>
                                        @if($submission->phone)
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td>
                                                <a href="tel:{{ $submission->phone }}" class="text-decoration-none">
                                                    {{ $submission->phone }}
                                                </a>
                                            </td>
                                        </tr>
                                        @endif
                                        @if($submission->company)
                                        <tr>
                                            <td><strong>Company:</strong></td>
                                            <td>{{ $submission->company }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td><strong>Preferred Contact:</strong></td>
                                            <td>{{ ucfirst($submission->preferred_contact) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Submission Details -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">📊 Submission Details</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge {{ $submission->status_badge_class }}">
                                                    {{ ucfirst(str_replace('_', ' ', $submission->status)) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Priority:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $submission->priority === 'high' ? 'danger' : ($submission->priority === 'medium' ? 'warning' : 'secondary') }}">
                                                    {{ ucfirst($submission->priority) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Spam Status:</strong></td>
                                            <td>
                                                @if($submission->is_spam)
                                                    <span class="badge bg-warning">SPAM DETECTED</span>
                                                @else
                                                    <span class="badge bg-success">NOT SPAM</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Submitted:</strong></td>
                                            <td>
                                                {{ $submission->submitted_at->format('d M Y H:i:s') }}
                                                <br><small class="text-muted">{{ $submission->submitted_at->diffForHumans() }}</small>
                                            </td>
                                        </tr>
                                        @if($submission->responded_at)
                                        <tr>
                                            <td><strong>Responded:</strong></td>
                                            <td>
                                                {{ $submission->responded_at->format('d M Y H:i:s') }}
                                                <br><small class="text-muted">{{ $submission->responded_at->diffForHumans() }}</small>
                                            </td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subject and Message -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">💬 Message Content</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Subject:</strong>
                                        <p class="mt-2">{{ $submission->subject }}</p>
                                    </div>
                                    <div>
                                        <strong>Message:</strong>
                                        <div class="mt-2 p-3 bg-light rounded">
                                            {!! nl2br(e($submission->message)) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">🔧 Technical Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>IP Address:</strong></td>
                                                    <td>{{ $submission->ip_address ?: 'Not recorded' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>User Agent:</strong></td>
                                                    <td>
                                                        @if($submission->user_agent)
                                                            <small>{{ Str::limit($submission->user_agent, 100) }}</small>
                                                        @else
                                                            Not recorded
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>Created:</strong></td>
                                                    <td>{{ $submission->created_at->format('d M Y H:i:s') }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Updated:</strong></td>
                                                    <td>{{ $submission->updated_at->format('d M Y H:i:s') }}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">⚡ Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group me-2">
                                        <a href="mailto:{{ $submission->email }}?subject=Re: {{ urlencode($submission->subject) }}" class="btn btn-success">
                                            <i class="fas fa-reply"></i> Reply via Email
                                        </a>
                                        @if($submission->phone)
                                            <a href="tel:{{ $submission->phone }}" class="btn btn-info">
                                                <i class="fas fa-phone"></i> Call
                                            </a>
                                        @endif
                                    </div>

                                    <div class="btn-group me-2">
                                        @if($submission->status !== 'responded')
                                            <button type="button" class="btn btn-warning" onclick="updateStatus('responded')">
                                                <i class="fas fa-check"></i> Mark as Responded
                                            </button>
                                        @endif
                                        @if($submission->status !== 'closed')
                                            <button type="button" class="btn btn-secondary" onclick="updateStatus('closed')">
                                                <i class="fas fa-archive"></i> Mark as Closed
                                            </button>
                                        @endif
                                    </div>

                                    @if(!$submission->is_spam)
                                        <div class="btn-group me-2">
                                            <button type="button" class="btn btn-warning" onclick="markAsSpam()">
                                                <i class="fas fa-shield-alt"></i> Mark as Spam
                                            </button>
                                        </div>
                                    @endif

                                    <div class="btn-group">
                                        <button type="button" class="btn btn-danger" onclick="deleteSubmission()">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function updateStatus(status) {
    if (confirm(`Are you sure you want to mark this submission as ${status}?`)) {
        fetch(`/admin/email-submissions/contact/{{ $submission->id }}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating status: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating status');
        });
    }
}

function markAsSpam() {
    if (confirm('Are you sure you want to mark this submission as spam?')) {
        fetch(`/admin/email-submissions/contact/{{ $submission->id }}/spam`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error marking as spam: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error marking as spam');
        });
    }
}

function deleteSubmission() {
    if (confirm('Are you sure you want to delete this submission? This action cannot be undone.')) {
        fetch(`/admin/email-submissions/contact/{{ $submission->id }}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{{ route("admin.email-submissions.contact") }}';
            } else {
                alert('Error deleting submission: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting submission');
        });
    }
}
</script>
@endsection
