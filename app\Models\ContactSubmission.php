<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ContactSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'company',
        'preferred_contact',
        'ip_address',
        'user_agent',
        'is_spam',
        'status',
        'submitted_at',
        'responded_at',
    ];

    protected $casts = [
        'submitted_at' => 'datetime',
        'responded_at' => 'datetime',
        'is_spam' => 'boolean',
    ];

    /**
     * Get the status badge class for display
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'new' => 'badge-warning',
            'emailed' => 'badge-info',
            'email_failed' => 'badge-danger',
            'spam_detected' => 'badge-dark',
            'responded' => 'badge-success',
            'closed' => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    /**
     * Get the priority based on content and timing
     */
    public function getPriorityAttribute(): string
    {
        if ($this->is_spam) return 'low';

        // Check for urgent keywords
        $urgentKeywords = ['urgent', 'asap', 'emergency', 'immediately', 'today'];
        $message = strtolower($this->message);

        foreach ($urgentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return 'high';
            }
        }

        // Check if submitted recently
        if ($this->submitted_at && $this->submitted_at->diffInHours(now()) < 2) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Scope for non-spam submissions
     */
    public function scopeNotSpam($query)
    {
        return $query->where('is_spam', false);
    }

    /**
     * Scope for new submissions
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope for recent submissions
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('submitted_at', '>=', now()->subHours($hours));
    }
}
