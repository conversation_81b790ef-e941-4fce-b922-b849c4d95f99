<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Change the value column from TEXT to LONGTEXT to handle larger content
            $table->longText('value')->nullable()->change();

            // Also change options column to LONGTEXT for consistency
            $table->longText('options')->nullable()->change();

            // Change description column to LONGTEXT as well
            $table->longText('description')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Revert back to TEXT columns
            $table->text('value')->nullable()->change();
            $table->text('options')->nullable()->change();
            $table->text('description')->nullable()->change();
        });
    }
};
