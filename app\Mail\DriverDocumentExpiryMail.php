<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Services\SettingsService;

class DriverDocumentExpiryMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $driver;
    public $expiringDocuments;

    /**
     * Create a new message instance.
     */
    public function __construct(User $driver, array $expiringDocuments)
    {
        $this->driver = $driver;
        $this->expiringDocuments = $expiringDocuments;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Document Expiry Reminder - Action Required',
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.driver-document-expiry',
            with: [
                'driver' => $this->driver,
                'expiringDocuments' => $this->expiringDocuments,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
