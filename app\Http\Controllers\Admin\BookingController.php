<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use App\Services\SettingsService;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the bookings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $status = $request->input('status');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $search = $request->input('search');

        // Build query
        $query = Booking::with(['user', 'vehicle', 'driver'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($dateFrom) {
            $query->whereDate('pickup_date', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('pickup_date', '<=', $dateTo);
        }

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('booking_number', 'like', "%{$search}%")
                  ->orWhere('pickup_address', 'like', "%{$search}%")
                  ->orWhere('dropoff_address', 'like', "%{$search}%")
                  ->orWhereHas('user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Get bookings with pagination
        $bookings = $query->paginate(10);

        // Get booking statistics
        $bookingStats = [
            'total' => Booking::count(),
            'pending' => Booking::where('status', 'pending')->count(),
            'confirmed' => Booking::where('status', 'confirmed')->count(),
            'completed' => Booking::where('status', 'completed')->count(),
            'cancelled' => Booking::where('status', 'cancelled')->count(),
        ];

        return view('admin.bookings.index', compact('bookings', 'bookingStats', 'status', 'dateFrom', 'dateTo', 'search'));
    }





    /**
     * Show the form for creating a new booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function create(Request $request)
    {
        // Get active vehicles
        $vehicles = Vehicle::where('is_active', true)->get();

        // Get active drivers
        $drivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->get();

        // Get existing clients
        $clients = User::where('role', 'client')->get();

        // Check if duplicating an existing booking
        $duplicateBooking = null;
        if ($request->has('duplicate')) {
            $duplicateBooking = Booking::with(['user', 'vehicle'])->find($request->duplicate);
        }

        // Get pricing settings
        $baseFare = SettingsService::get('base_fare', 5);
        $pricePerKm = SettingsService::get('price_per_km', 2.5);
        $hourlyRate = SettingsService::get('hourly_rate', 50);
        $airportSurcharge = SettingsService::get('airport_surcharge', 10.00);

        // Get Google Maps settings
        $googleMapsApiKey = SettingsService::getGoogleMapsApiKey();
        $mapDefaultLat = SettingsService::get('google_maps_default_lat', 51.5074);
        $mapDefaultLng = SettingsService::get('google_maps_default_lng', -0.1278);
        $mapDefaultZoom = SettingsService::get('google_maps_default_zoom', 12);
        $countryCode = SettingsService::get('country_code', 'US');

        // Get conversion factors and multipliers
        $kmToMilesFactor = SettingsService::get('km_to_miles_factor', 0.621371);
        $estimatedMilesPerHour = SettingsService::get('estimated_miles_per_hour', 30);
        $returnTripMultiplier = SettingsService::get('return_trip_multiplier', 1.8);

        // Get business settings
        $businessName = SettingsService::get('business_name', 'Transportation Service');
        $businessPhone = SettingsService::get('business_phone', '');
        $businessEmail = SettingsService::get('business_email', '');
        $businessAddress = SettingsService::get('business_address', '');

        // Get booking settings
        $maxAdvanceBookingDays = SettingsService::get('max_advance_booking_days', 30);
        $minAdvanceBookingHours = SettingsService::get('min_advance_booking_hours', 2);
        $defaultBookingStatus = SettingsService::get('default_booking_status', 'pending');
        $autoAssignDriver = SettingsService::get('auto_assign_driver', false);

        // Get payment settings
        $currencySymbol = SettingsService::getCurrencySymbol();
        $currencyCode = SettingsService::get('currency_code', 'USD');
        $taxRate = SettingsService::get('tax_rate', 0);
        $serviceFee = SettingsService::get('service_fee', 0);

        // Vehicle types for filtering - get actual types from database
        $vehicleTypes = Vehicle::where('is_active', true)
            ->distinct()
            ->pluck('type')
            ->filter()
            ->sort()
            ->values()
            ->toArray();

        // Fallback to settings if no vehicles exist
        if (empty($vehicleTypes)) {
            $vehicleTypes = SettingsService::get('vehicle_types', ['sedan', 'suv', 'professional', 'van']);
            if (!is_array($vehicleTypes)) {
                $vehicleTypes = explode(',', $vehicleTypes);
            }
        }

        // Capacity ranges for filtering
        $capacityRanges = [
            '1-3' => '1-3 Passengers',
            '4-5' => '4-5 Passengers',
            '6+' => '6+ Passengers'
        ];

        // Rating options for filtering
        $ratingOptions = [
            '5' => '5 Stars',
            '4' => '4+ Stars',
            '3' => '3+ Stars'
        ];

        // Experience options for filtering
        $experienceOptions = [
            '1' => '1+ Years',
            '3' => '3+ Years',
            '5' => '5+ Years'
        ];

        // Get airports for airport transfer option
        $airports = \App\Models\Airport::orderBy('name')->get();

        // Get extra services settings
        $extraServicesSettings = SettingsService::getExtraServicesSettings();

        return view('admin.bookings.create', compact(
            'vehicles',
            'drivers',
            'clients',
            'airports',
            'duplicateBooking',
            'baseFare',
            'pricePerKm',
            'hourlyRate',
            'airportSurcharge',
            'googleMapsApiKey',
            'mapDefaultLat',
            'mapDefaultLng',
            'mapDefaultZoom',
            'countryCode',
            'kmToMilesFactor',
            'estimatedMilesPerHour',
            'returnTripMultiplier',
            'businessName',
            'businessPhone',
            'businessEmail',
            'businessAddress',
            'maxAdvanceBookingDays',
            'minAdvanceBookingHours',
            'defaultBookingStatus',
            'autoAssignDriver',
            'currencySymbol',
            'currencyCode',
            'taxRate',
            'serviceFee',
            'vehicleTypes',
            'capacityRanges',
            'ratingOptions',
            'experienceOptions',
            'extraServicesSettings'
        ));
    }

    /**
     * Store a newly created booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = \Validator::make($request->all(), [
            // Client information
            'client_type' => 'required|in:existing,new',
            'existing_client_id' => 'required_if:client_type,existing|exists:users,id',
            'client_name' => 'required_if:client_type,new|string|max:255',
            'client_email' => 'required_if:client_type,new|string|email|max:255|unique:users,email',
            'client_phone' => 'required_if:client_type,new|string|max:20',
            'client_address' => 'nullable|string|max:255',

            // Booking information
            'booking_type' => 'required|in:one_way,return,hourly,airport_transfer',
            'pickup_address' => 'required|string|max:255',
            'pickup_lat' => 'nullable|numeric',
            'pickup_lng' => 'nullable|numeric',
            'pickup_date' => 'required|date',
            'pickup_time' => 'required',
            'dropoff_address' => 'required_if:booking_type,one_way,return,airport_transfer|string|max:255',
            'dropoff_lat' => 'nullable|numeric',
            'dropoff_lng' => 'nullable|numeric',
            'return_date' => 'required_if:booking_type,return|nullable|date',
            'return_time' => 'required_if:booking_type,return|nullable',
            'duration_hours' => 'required_if:booking_type,hourly|nullable|integer|min:1',
            'distance_value' => 'nullable|numeric',
            'duration_value' => 'nullable|numeric',
            'vehicle_id' => 'required|exists:vehicles,id',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',

            // Via stops
            'via_stops' => 'nullable|array|max:5',
            'via_stops.*.address' => 'required_with:via_stops|string|max:255',
            'via_stops.*.lat' => 'nullable|numeric',
            'via_stops.*.lng' => 'nullable|numeric',

            // Driver assignment
            'assign_driver' => 'required|boolean',
            'driver_id' => 'required_if:assign_driver,1|exists:users,id',

            // Extra services
            'meet_and_greet' => 'nullable|boolean',
            'child_seat' => 'nullable|boolean',
            'wheelchair_accessible' => 'nullable|boolean',
            'extra_luggage' => 'nullable|boolean',

            // Flight details (for airport transfers)
            'flight_number' => 'nullable|string|max:20',
            'airline' => 'nullable|string|max:100',
            'departure_time' => 'nullable|date',
            'arrival_time' => 'nullable|date',
            'terminal' => 'nullable|string|max:50',
            'flight_status' => 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived',
            'flight_notes' => 'nullable|string|max:500',
        ]);

        // If validation fails, return error response for AJAX requests
        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();

        try {
            // Create or get client
            if ($request->client_type === 'new') {
                // Create new client
                $client = new User();
                $client->name = $request->client_name;
                $client->email = $request->client_email;
                $client->phone = $request->client_phone;
                $client->address = $request->client_address;
                $client->role = 'client'; // Set role as client
                $client->password = bcrypt(Str::random(12)); // Generate random password
                $client->email_verified_at = now(); // Auto-verify for admin-created users
                $client->save();

                // Log the user creation
                \Log::info('New user created via admin booking', [
                    'user_id' => $client->id,
                    'name' => $client->name,
                    'email' => $client->email,
                    'created_by' => auth()->id()
                ]);
            } else {
                // Get existing client
                $client = User::findOrFail($request->existing_client_id);
            }

            // Format pickup date and time
            $pickupDateTime = $request->pickup_date . ' ' . $request->pickup_time;
            $pickupDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $pickupDateTime);

            // Format return date and time if applicable
            $returnDate = null;
            if ($request->booking_type === 'return' && $request->return_date && $request->return_time) {
                $returnDateTime = $request->return_date . ' ' . $request->return_time;
                $returnDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $returnDateTime);
            }

            // Create booking
            $booking = new Booking();
            $booking->user_id = $client->id;
            $booking->vehicle_id = $request->vehicle_id;
            $booking->booking_number = Booking::generateBookingNumber();
            $booking->booking_type = $request->booking_type;
            $booking->pickup_address = $request->pickup_address;
            $booking->pickup_lat = $request->pickup_lat;
            $booking->pickup_lng = $request->pickup_lng;

            if ($request->booking_type !== 'hourly') {
                $booking->dropoff_address = $request->dropoff_address;
                $booking->dropoff_lat = $request->dropoff_lat;
                $booking->dropoff_lng = $request->dropoff_lng;
            }

            $booking->pickup_date = $pickupDate;
            $booking->return_date = $returnDate;
            $booking->duration_hours = $request->duration_hours;

            // Store distance and duration if provided
            if ($request->has('distance_value')) {
                $booking->distance_value = $request->distance_value;
                $booking->distance = $request->distance_value / 1000; // Convert to kilometers
            }

            if ($request->has('duration_value')) {
                $booking->duration_value = $request->duration_value;
            }

            // Store fare details
            $baseFare = SettingsService::get('base_fare', 5);
            $pricePerKm = SettingsService::get('price_per_km', 2.5);
            $hourlyRate = SettingsService::get('hourly_rate', 50);

            $fareDetails = [
                'base_fare' => $baseFare,
                'price_per_km' => $pricePerKm,
                'hourly_rate' => $hourlyRate,
                'booking_type' => $request->booking_type,
                'calculated_at' => now()->toDateTimeString(),
            ];

            if ($request->booking_type === 'hourly') {
                $fareDetails['hours'] = $request->duration_hours;
            } else {
                $fareDetails['distance'] = $request->distance_value ? ($request->distance_value / 1000) : null;
            }

            $booking->fare_details = $fareDetails;
            $booking->amount = $request->amount;
            $booking->status = 'pending';
            $booking->payment_status = 'pending';
            $booking->notes = $request->notes;

            // Handle via stops
            $viaStops = $request->input('via_stops', []);
            if (!empty($viaStops) && is_array($viaStops)) {
                $booking->via_stops = $viaStops;
                $booking->via_count = count($viaStops);

                // Calculate via charges
                $vehicle = \App\Models\Vehicle::find($request->vehicle_id);
                if ($vehicle) {
                    $viaChargePerStop = $vehicle->via_charge ?? 5.00;
                    $booking->via_charges = $booking->via_count * $viaChargePerStop;
                }
            }

            // Handle extra services
            $booking->meet_and_greet = $request->boolean('meet_and_greet');
            $booking->child_seat = $request->boolean('child_seat');
            $booking->wheelchair_accessible = $request->boolean('wheelchair_accessible');
            $booking->extra_luggage = $request->boolean('extra_luggage');

            // Handle airport transfer fields
            if ($request->booking_type === 'airport_transfer') {
                $booking->airport_direction = $request->input('airport_direction');
                $booking->airport_surcharge = $request->input('airport_surcharge', 0);

                if ($request->input('airport_direction') === 'to_airport') {
                    $booking->dropoff_airport_id = $request->input('airport_id');
                } else {
                    $booking->pickup_airport_id = $request->input('airport_id');
                }

            }

            // Handle flight information for all booking types
            $booking->flight_number = $request->input('flight_number');
            $booking->airline = $request->input('airline');
            $booking->terminal = $request->input('terminal');
            $booking->flight_status = $request->input('flight_status');
            $booking->flight_notes = $request->input('flight_notes');

            // Handle flight times
            if ($request->input('departure_time')) {
                try {
                    $booking->departure_time = \Carbon\Carbon::parse($request->input('departure_time'));
                } catch (\Exception $e) {
                    \Log::warning('Invalid departure time format in admin booking: ' . $request->input('departure_time'));
                }
            }

            if ($request->input('arrival_time')) {
                try {
                    $booking->arrival_time = \Carbon\Carbon::parse($request->input('arrival_time'));
                } catch (\Exception $e) {
                    \Log::warning('Invalid arrival time format in admin booking: ' . $request->input('arrival_time'));
                }
            }

            // Handle extra services (if the booking model supports these fields)
            if (Schema::hasColumn('bookings', 'meet_and_greet')) {
                $booking->meet_and_greet = $request->has('meet_and_greet') ? 1 : 0;
            }
            if (Schema::hasColumn('bookings', 'child_seat')) {
                $booking->child_seat = $request->has('child_seat') ? 1 : 0;
            }
            if (Schema::hasColumn('bookings', 'wheelchair_accessible')) {
                $booking->wheelchair_accessible = $request->has('wheelchair_accessible') ? 1 : 0;
            }
            if (Schema::hasColumn('bookings', 'extra_luggage')) {
                $booking->extra_luggage = $request->has('extra_luggage') ? 1 : 0;
            }

            // Assign driver if requested
            if ($request->assign_driver) {
                $booking->driver_id = $request->driver_id;
                $booking->status = 'assigned';
            }

            $booking->save();

            // Add booking history
            $booking->addHistory('booking_created', [
                'booking_type' => $booking->booking_type,
                'amount' => $booking->amount,
                'vehicle_id' => $booking->vehicle_id,
                'created_by' => 'admin',
            ]);

            // Add driver assignment history if applicable
            if ($request->assign_driver) {
                $driver = User::findOrFail($request->driver_id);
                $booking->addHistory('driver_assigned', [
                    'driver_id' => $driver->id,
                    'driver_name' => $driver->name,
                    'assigned_by' => 'admin',
                ]);

                // Send driver assignment email
                try {
                    EmailService::sendDriverAssignment($booking);
                } catch (\Exception $e) {
                    \Log::error('Failed to send driver assignment email: ' . $e->getMessage(), [
                        'booking_id' => $booking->id,
                        'driver_id' => $driver->id,
                    ]);
                }
            }

            // Send booking confirmation email to client
            try {
                EmailService::sendBookingConfirmation($booking);
                // Also send client booking status update
                EmailService::sendClientBookingStatus($booking, 'confirmed');
                // Send admin notification for new booking
                EmailService::sendAdminNewBookingNotification($booking);
            } catch (\Exception $e) {
                \Log::error('Failed to send booking confirmation email: ' . $e->getMessage(), [
                    'booking_id' => $booking->id,
                    'client_email' => $client->email,
                ]);
            }

            // Send admin alerts for special conditions
            try {
                // High-value booking alert
                if ($booking->amount >= 200) {
                    EmailService::sendAdminBookingAlert($booking, 'high_value', [
                        'additional_info' => 'This high-value booking may require special attention and premium service.'
                    ]);
                }

                // Last-minute booking alert (within 2 hours)
                if ($booking->pickup_date->diffInHours(now()) <= 2) {
                    EmailService::sendAdminBookingAlert($booking, 'last_minute', [
                        'additional_info' => 'This booking was made with short notice. Ensure driver availability.'
                    ]);
                }

                // Urgent assignment alert if no driver assigned
                if (!$booking->driver_id && $booking->pickup_date->diffInHours(now()) <= 6) {
                    EmailService::sendAdminBookingAlert($booking, 'urgent_assignment', [
                        'additional_info' => 'This booking needs a driver assigned urgently.'
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Failed to send admin booking alerts: ' . $e->getMessage(), [
                    'booking_id' => $booking->id,
                ]);
            }

            DB::commit();

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Booking created successfully.',
                    'booking' => $booking,
                    'redirect' => route('admin.bookings.show', $booking->id)
                ]);
            }

            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('success', 'Booking created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating booking: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error creating booking: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $booking = Booking::with(['user', 'vehicle', 'driver', 'payments', 'pickupAirport', 'dropoffAirport'])->findOrFail($id);

        // Get available drivers for assignment
        $availableDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->get();

        return view('admin.bookings.show', compact('booking', 'availableDrivers'));
    }

    /**
     * Show the form for editing the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit($id)
    {
        $booking = Booking::with(['user', 'vehicle', 'driver'])->findOrFail($id);

        // Check if booking can be edited
        if (in_array($booking->status, ['completed', 'cancelled'])) {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Cannot edit a ' . $booking->status . ' booking.');
        }

        // Get all necessary data for the edit form
        $vehicles = Vehicle::where('is_active', true)->get();
        $drivers = User::where('role', 'driver')->where('is_active', true)->get();
        $clients = User::where('role', 'client')->get();
        $airports = Airport::where('is_active', true)->get();

        // Get settings
        $baseFare = SettingsService::get('base_fare', 5.00);
        $pricePerKm = SettingsService::get('price_per_km', 1.50);
        $hourlyRate = SettingsService::get('hourly_rate', 50.00);
        $airportSurcharge = SettingsService::get('airport_surcharge', 10.00);
        $googleMapsApiKey = SettingsService::get('google_maps_api_key', '');
        $mapDefaultLat = SettingsService::get('map_default_lat', 51.5074);
        $mapDefaultLng = SettingsService::get('map_default_lng', -0.1278);
        $mapDefaultZoom = SettingsService::get('map_default_zoom', 10);
        $countryCode = SettingsService::get('country_code', 'GB');
        $kmToMilesFactor = SettingsService::get('km_to_miles_factor', 0.621371);
        $estimatedMilesPerHour = SettingsService::get('estimated_miles_per_hour', 25);
        $returnTripMultiplier = SettingsService::get('return_trip_multiplier', 1.8);
        $businessName = SettingsService::get('business_name', 'YNR Cars');
        $businessPhone = SettingsService::get('business_phone', '');
        $businessEmail = SettingsService::get('business_email', '');
        $businessAddress = SettingsService::get('business_address', '');
        $maxAdvanceBookingDays = SettingsService::get('max_advance_booking_days', 30);
        $minAdvanceBookingHours = SettingsService::get('min_advance_booking_hours', 2);
        $defaultBookingStatus = SettingsService::get('default_booking_status', 'pending');
        $autoAssignDriver = SettingsService::get('auto_assign_driver', false);
        $currencySymbol = SettingsService::getCurrencySymbol();
        $currencyCode = SettingsService::get('currency_code', 'GBP');
        $taxRate = SettingsService::get('tax_rate', 0);
        $serviceFee = SettingsService::get('service_fee', 0);
        $extraServicesSettings = SettingsService::getExtraServicesSettings();

        return view('admin.bookings.edit', compact(
            'booking',
            'vehicles',
            'drivers',
            'clients',
            'airports',
            'baseFare',
            'pricePerKm',
            'hourlyRate',
            'airportSurcharge',
            'googleMapsApiKey',
            'mapDefaultLat',
            'mapDefaultLng',
            'mapDefaultZoom',
            'countryCode',
            'kmToMilesFactor',
            'estimatedMilesPerHour',
            'returnTripMultiplier',
            'businessName',
            'businessPhone',
            'businessEmail',
            'businessAddress',
            'maxAdvanceBookingDays',
            'minAdvanceBookingHours',
            'defaultBookingStatus',
            'autoAssignDriver',
            'currencySymbol',
            'currencyCode',
            'taxRate',
            'serviceFee',
            'extraServicesSettings'
        ));
    }

    /**
     * Update the specified booking in storage (admin can update anytime).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $booking = Booking::findOrFail($id);
        $request->validate([
            'pickup_address' => 'required|string',
            'pickup_date' => 'required|date',
            'dropoff_address' => 'required_if:booking_type,one_way,return',
            'return_date' => 'required_if:booking_type,return|nullable|date',
            'duration_hours' => 'required_if:booking_type,hourly|nullable|integer|min:1',
            'vehicle_id' => 'required|exists:vehicles,id',
            'booking_type' => 'required|in:one_way,return,hourly',
            'amount' => 'required|numeric|min:1',
            'via_stops' => 'nullable|array|max:5',
            'via_stops.*.address' => 'required_with:via_stops|string|max:255',
            'via_stops.*.lat' => 'nullable|numeric',
            'via_stops.*.lng' => 'nullable|numeric',

            // Flight information fields (optional for all booking types)
            'flight_number' => 'nullable|string|max:20',
            'airline' => 'nullable|string|max:100',
            'departure_time' => 'nullable|date',
            'arrival_time' => 'nullable|date|after_or_equal:departure_time',
            'terminal' => 'nullable|string|max:50',
            'flight_status' => 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived',
            'flight_notes' => 'nullable|string|max:500',
        ]);
        $pickupDate = \Carbon\Carbon::parse($request->input('pickup_date'));
        $returnDate = null;
        if ($request->input('booking_type') === 'return' && $request->input('return_date')) {
            $returnDate = \Carbon\Carbon::parse($request->input('return_date'));
        }
        $booking->vehicle_id = $request->input('vehicle_id');
        $booking->booking_type = $request->input('booking_type');
        $booking->pickup_address = $request->input('pickup_address');
        $booking->pickup_date = $pickupDate;
        $booking->return_date = $returnDate;
        $booking->duration_hours = $request->input('duration_hours');
        $booking->amount = $request->input('amount');
        $booking->notes = $request->input('notes');

        // Handle flight information
        $booking->flight_number = $request->input('flight_number');
        $booking->airline = $request->input('airline');
        $booking->departure_time = $request->input('departure_time') ? \Carbon\Carbon::parse($request->input('departure_time')) : null;
        $booking->arrival_time = $request->input('arrival_time') ? \Carbon\Carbon::parse($request->input('arrival_time')) : null;
        $booking->terminal = $request->input('terminal');
        $booking->flight_status = $request->input('flight_status');
        $booking->flight_notes = $request->input('flight_notes');

        if ($request->input('booking_type') !== 'hourly') {
            $booking->dropoff_address = $request->input('dropoff_address');
        }
        $viaStops = $request->input('via_stops', []);
        if (!empty($viaStops) && is_array($viaStops)) {
            $booking->via_stops = $viaStops;
            $booking->via_count = count($viaStops);
            $vehicle = \App\Models\Vehicle::find($request->input('vehicle_id'));
            if ($vehicle) {
                $viaChargePerStop = $vehicle->via_charge ?? 5.00;
                $booking->via_charges = $booking->via_count * $viaChargePerStop;
            }
        }
        $booking->save();
        $booking->addHistory('booking_updated', [
            'booking_type' => $booking->booking_type,
            'amount' => $booking->amount,
            'vehicle_id' => $booking->vehicle_id,
        ]);
        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Booking updated successfully.');
    }

    /**
     * Update the status of the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,completed,cancelled',
        ]);

        $booking = Booking::findOrFail($id);
        $oldStatus = $booking->status;
        $newStatus = $request->status;

        // Update booking status
        $booking->status = $newStatus;
        $booking->save();

        // Add status change to booking history
        $booking->history()->create([
            'user_id' => Auth::id(),
            'action' => 'status_change',
            'details' => json_encode([
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ]),
        ]);

        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Booking status updated successfully.');
    }

    /**
     * Assign a driver to the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function assignDriver(Request $request, $id)
    {
        $request->validate([
            'driver_id' => 'required|exists:users,id',
        ]);

        $booking = Booking::findOrFail($id);
        $oldDriverId = $booking->driver_id;
        $newDriverId = $request->driver_id;

        // Get driver information
        $driver = User::where('role', 'driver')->findOrFail($newDriverId);

        // Check if driver is active and available
        if (!$driver->is_active) {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Cannot assign inactive driver. Please activate the driver first.');
        }

        // Check for scheduling conflicts
        $conflictingBookings = Booking::where('driver_id', $newDriverId)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->where(function($query) use ($booking) {
                // Check for overlapping time periods
                // Case 1: Other booking spans over this booking's pickup date
                $query->where(function($q) use ($booking) {
                    $q->where('pickup_date', '<=', $booking->pickup_date)
                      ->whereNotNull('return_date')
                      ->where('return_date', '>=', $booking->pickup_date);
                });

                // Case 2: This booking's return date exists and other booking starts during this booking
                if ($booking->return_date) {
                    $query->orWhere(function($q) use ($booking) {
                        $q->where('pickup_date', '>=', $booking->pickup_date)
                          ->where('pickup_date', '<=', $booking->return_date);
                    });
                }

                // Case 3: This booking has no return date, but it overlaps with other bookings on the same day
                if (!$booking->return_date) {
                    $query->orWhere(function($q) use ($booking) {
                        $q->whereDate('pickup_date', '=', $booking->pickup_date->toDateString());
                    });
                }
            })
            ->count();

        if ($conflictingBookings > 0) {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('warning', 'This driver has potential scheduling conflicts. Please check their schedule before assigning.');
        }

        // Store original status for history
        $originalStatus = $booking->status;

        // Update booking driver
        $booking->driver_id = $newDriverId;

        // If assigning a driver, also update the booking status
        if ($booking->status == 'pending') {
            $booking->status = 'confirmed';
        } elseif ($booking->status == 'confirmed') {
            $booking->status = 'assigned';
        }

        // Make sure the changes are saved
        $saved = $booking->save();

        if (!$saved) {
            \Log::error('Failed to save booking when assigning driver', [
                'booking_id' => $booking->id,
                'driver_id' => $newDriverId
            ]);
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Failed to assign driver. Please try again.');
        }

        // Add driver assignment to booking history
        $booking->history()->create([
            'user_id' => Auth::id(),
            'action' => 'driver_assigned',
            'details' => json_encode([
                'old_driver_id' => $oldDriverId,
                'new_driver_id' => $newDriverId,
                'driver_name' => $driver->name,
                'assigned_by' => Auth::user()->name
            ]),
            'status_before' => $originalStatus,
            'status_after' => $booking->status
        ]);

        // Verify the driver was actually assigned
        $refreshedBooking = Booking::find($booking->id);
        if ($refreshedBooking->driver_id != $newDriverId) {
            \Log::error('Driver assignment verification failed', [
                'booking_id' => $booking->id,
                'expected_driver_id' => $newDriverId,
                'actual_driver_id' => $refreshedBooking->driver_id
            ]);
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Driver assignment failed. Please try again.');
        }

        // Notify the driver about the new assignment
        try {
            $driver->notify(new \App\Notifications\DriverAssigned($booking));
        } catch (\Exception $e) {
            // Log notification error but continue
            \Log::error('Failed to send driver assignment notification: ' . $e->getMessage());
        }

        // Notify the client about driver assignment
        try {
            $booking->user->notify(new \App\Notifications\BookingDriverAssigned($booking));
        } catch (\Exception $e) {
            // Log notification error but continue
            \Log::error('Failed to send client notification: ' . $e->getMessage());
        }

        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Driver assigned successfully.');
    }

    /**
     * Show available drivers for a booking.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showAvailableDrivers($id)
    {
        $booking = Booking::findOrFail($id);

        // Get all active and available drivers
        $availableDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->where('is_available', true)
            ->get();

        // Get drivers with potential conflicts
        $busyDriverIds = Booking::whereIn('status', ['assigned', 'in_progress'])
            ->where(function($query) use ($booking) {
                // Check for overlapping time periods
                // Case 1: Other booking spans over this booking's pickup date
                $query->where(function($q) use ($booking) {
                    $q->where('pickup_date', '<=', $booking->pickup_date)
                      ->whereNotNull('return_date')
                      ->where('return_date', '>=', $booking->pickup_date);
                });

                // Case 2: This booking's return date exists and other booking starts during this booking
                if ($booking->return_date) {
                    $query->orWhere(function($q) use ($booking) {
                        $q->where('pickup_date', '>=', $booking->pickup_date)
                          ->where('pickup_date', '<=', $booking->return_date);
                    });
                }

                // Case 3: This booking has no return date, but it overlaps with other bookings on the same day
                if (!$booking->return_date) {
                    $query->orWhere(function($q) use ($booking) {
                        $q->whereDate('pickup_date', '=', $booking->pickup_date->toDateString());
                    });
                }
            })
            ->pluck('driver_id')
            ->toArray();

        return view('admin.bookings.available-drivers', compact('booking', 'availableDrivers', 'busyDriverIds'));
    }

    /**
     * Cancel the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, $id)
    {
        $request->validate([
            'cancellation_reason' => 'required|string|max:255',
        ]);

        $booking = Booking::findOrFail($id);

        // Only allow cancellation if booking is not already completed or cancelled
        if (in_array($booking->status, ['completed', 'cancelled'])) {
            return redirect()->route('admin.bookings.show', $booking->id)
                ->with('error', 'Cannot cancel a booking that is already completed or cancelled.');
        }

        // Update booking status
        $booking->status = 'cancelled';
        $booking->cancellation_reason = $request->cancellation_reason;
        $booking->cancelled_by = 'admin';
        $booking->cancelled_at = now();
        $booking->save();

        // Add cancellation to booking history
        $booking->history()->create([
            'user_id' => Auth::id(),
            'action' => 'cancelled',
            'details' => json_encode([
                'reason' => $request->cancellation_reason,
            ]),
        ]);

        return redirect()->route('admin.bookings.show', $booking->id)
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Remove the specified booking from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $booking = Booking::findOrFail($id);
        $booking->delete();
        return redirect()->route('admin.bookings.index')
            ->with('success', 'Booking deleted successfully.');
    }
}
