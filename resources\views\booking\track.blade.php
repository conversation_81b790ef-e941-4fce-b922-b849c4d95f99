@extends('layouts.guest')

@section('title', 'Track Your Booking')

@section('styles')
<style>
    .tracking-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .tracking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .tracking-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .tracking-card .card-body {
        padding: 30px;
    }

    .status-badge {
        display: inline-block;
        padding: 8px 20px;
        border-radius: 30px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-assigned {
        background-color: #e2e3e5;
        color: #41464b;
    }

    .status-in-progress {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .booking-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
    }

    .detail-value {
        color: #6c757d;
    }

    #map {
        height: 400px;
        width: 100%;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 20px;
        width: 4px;
        background-color: #e9ecef;
    }

    .timeline-item {
        position: relative;
        padding-left: 50px;
        margin-bottom: 30px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-marker {
        position: absolute;
        left: 10px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #fff;
        border: 4px solid #e9ecef;
        z-index: 1;
    }

    .timeline-marker.active {
        background-color: #ee393d;
        border-color: #ee393d;
        box-shadow: 0 0 0 4px rgba(248, 193, 44, 0.3);
    }

    .timeline-marker.completed {
        background-color: #28a745;
        border-color: #28a745;
    }

    .timeline-content {
        background-color: #fff;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }

    .timeline-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-date {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .timeline-description {
        color: #495057;
    }

    .driver-card {
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }

    .driver-info {
        display: flex;
        align-items: center;
    }

    .driver-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
    }

    .driver-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .driver-details h5 {
        margin-bottom: 5px;
    }

    .driver-details p {
        margin-bottom: 0;
        color: #6c757d;
    }

    .driver-rating {
        display: flex;
        align-items: center;
        margin-top: 5px;
    }

    .driver-rating i {
        color: #ee393d;
        margin-right: 2px;
    }

    .refresh-button {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: #f8f9fa;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        transition: all 0.3s;
    }

    .refresh-button:hover {
        background-color: #e9ecef;
        transform: rotate(180deg);
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #000;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e5b429;
        border-color: #e5b429;
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }
</style>
@endsection

@section('content')
<section class="tracking-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="tracking-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">Track Your Booking</h3>
                        <span class="status-badge status-{{ strtolower(str_replace('_', '-', $booking->status)) }}">
                            {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                        </span>
                    </div>
                    <div class="card-body position-relative">
                        <button id="refreshTracking" class="refresh-button" title="Refresh tracking information">
                            <i class="fas fa-sync-alt"></i>
                        </button>

                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Booking Details</h5>
                                <div class="booking-details">
                                    <div class="detail-row">
                                        <div class="detail-label">Booking Number:</div>
                                        <div class="detail-value">{{ $booking->booking_number }}</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Vehicle:</div>
                                        <div class="detail-value">{{ $booking->vehicle->name }}</div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Pickup:</div>
                                        <div class="detail-value">{{ $booking->pickup_address }}</div>
                                    </div>
                                    @if($booking->via_stops && count($booking->via_stops) > 0)
                                        @foreach($booking->via_stops as $index => $viaStop)
                                            <div class="detail-row">
                                                <div class="detail-label">Via Stop {{ $index + 1 }}:</div>
                                                <div class="detail-value">{{ $viaStop['address'] ?? 'Address not specified' }}</div>
                                            </div>
                                        @endforeach
                                    @endif
                                    @if ($booking->booking_type !== 'hourly')
                                        <div class="detail-row">
                                            <div class="detail-label">Dropoff:</div>
                                            <div class="detail-value">{{ $booking->dropoff_address }}</div>
                                        </div>
                                    @endif
                                    <div class="detail-row">
                                        <div class="detail-label">Date & Time:</div>
                                        <div class="detail-value">{{ $booking->pickup_date->format('M d, Y h:i A') }}</div>
                                    </div>
                                    @if ($booking->booking_type === 'return' && $booking->return_date)
                                        <div class="detail-row">
                                            <div class="detail-label">Return Date:</div>
                                            <div class="detail-value">{{ $booking->return_date->format('M d, Y h:i A') }}</div>
                                        </div>
                                    @endif
                                    @if ($booking->booking_type === 'hourly' && $booking->duration_hours)
                                        <div class="detail-row">
                                            <div class="detail-label">Duration:</div>
                                            <div class="detail-value">{{ $booking->duration_hours }} {{ $booking->duration_hours > 1 ? 'hours' : 'hour' }}</div>
                                        </div>
                                    @endif

                                    {{-- Flight Information Section --}}
                                    @if($booking->hasFlightDetails())
                                        <div class="mt-3 pt-3" style="border-top: 1px solid #dee2e6;">
                                            <h6 class="mb-2 text-primary">
                                                <i class="fas fa-plane me-2"></i>Flight Information
                                            </h6>
                                            @if($booking->flight_number)
                                                <div class="detail-row">
                                                    <div class="detail-label">Flight Number:</div>
                                                    <div class="detail-value">{{ $booking->flight_number }}</div>
                                                </div>
                                            @endif
                                            @if($booking->airline)
                                                <div class="detail-row">
                                                    <div class="detail-label">Airline:</div>
                                                    <div class="detail-value">{{ $booking->airline }}</div>
                                                </div>
                                            @endif
                                            @if($booking->departure_time)
                                                <div class="detail-row">
                                                    <div class="detail-label">Departure:</div>
                                                    <div class="detail-value">{{ $booking->departure_time->format('M d, Y h:i A') }}</div>
                                                </div>
                                            @endif
                                            @if($booking->arrival_time)
                                                <div class="detail-row">
                                                    <div class="detail-label">Arrival:</div>
                                                    <div class="detail-value">{{ $booking->arrival_time->format('M d, Y h:i A') }}</div>
                                                </div>
                                            @endif
                                            @if($booking->terminal)
                                                <div class="detail-row">
                                                    <div class="detail-label">Terminal:</div>
                                                    <div class="detail-value">{{ $booking->terminal }}</div>
                                                </div>
                                            @endif
                                            @if($booking->flight_status)
                                                <div class="detail-row">
                                                    <div class="detail-label">Flight Status:</div>
                                                    <div class="detail-value">
                                                        <span class="badge {{ $booking->flight_status_badge_class }}">
                                                            {{ ucfirst(str_replace('_', ' ', $booking->flight_status)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            @endif
                                            @if($booking->flight_notes)
                                                <div class="detail-row">
                                                    <div class="detail-label">Flight Notes:</div>
                                                    <div class="detail-value">{{ $booking->flight_notes }}</div>
                                                </div>
                                            @endif
                                        </div>
                                    @endif

                                    <div class="detail-row">
                                        <div class="detail-label">Amount:</div>
                                        <div class="detail-value">@currency(){{ number_format($booking->amount, 2) }}</div>
                                    </div>
                                </div>

                                @if($booking->driver)
                                <h5 class="mb-3">Driver Information</h5>
                                <div class="driver-card">
                                    <div class="driver-info">
                                        <div class="driver-avatar">
                                            @if($booking->driver->avatar)
                                                <img src="{{ asset('storage/' . $booking->driver->avatar) }}" alt="{{ $booking->driver->name }}">
                                            @else
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center h-100">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="driver-details">
                                            <h5>{{ $booking->driver->name }}</h5>
                                            <p><i class="fas fa-phone-alt me-2"></i>{{ $booking->driver->phone }}</p>
                                            <div class="driver-rating">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= 4)
                                                        <i class="fas fa-star"></i>
                                                    @else
                                                        <i class="far fa-star"></i>
                                                    @endif
                                                @endfor
                                                <span class="ms-1">4.0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">Booking Status</h5>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ in_array($booking->status, ['pending', 'confirmed', 'assigned', 'in_progress', 'completed']) ? 'completed' : '' }}"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Booking Created</div>
                                            <div class="timeline-date">{{ $booking->created_at->format('M d, Y h:i A') }}</div>
                                            <div class="timeline-description">Your booking has been successfully created.</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ in_array($booking->status, ['confirmed', 'assigned', 'in_progress', 'completed']) ? 'completed' : ($booking->status === 'pending' ? 'active' : '') }}"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Booking Confirmed</div>
                                            <div class="timeline-date">
                                                @if(in_array($booking->status, ['confirmed', 'assigned', 'in_progress', 'completed']))
                                                    {{ $booking->history->where('action', 'status_changed')->where('status_after', 'confirmed')->first()->created_at->format('M d, Y h:i A') ?? 'Pending' }}
                                                @else
                                                    Pending
                                                @endif
                                            </div>
                                            <div class="timeline-description">Your booking has been confirmed by our team.</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ in_array($booking->status, ['assigned', 'in_progress', 'completed']) ? 'completed' : ($booking->status === 'confirmed' ? 'active' : '') }}"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Driver Assigned</div>
                                            <div class="timeline-date">
                                                @if(in_array($booking->status, ['assigned', 'in_progress', 'completed']))
                                                    {{ $booking->history->where('action', 'driver_assigned')->first()->created_at->format('M d, Y h:i A') ?? 'Pending' }}
                                                @else
                                                    Pending
                                                @endif
                                            </div>
                                            <div class="timeline-description">A driver has been assigned to your booking.</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ in_array($booking->status, ['in_progress', 'completed']) ? 'completed' : ($booking->status === 'assigned' ? 'active' : '') }}"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Ride in Progress</div>
                                            <div class="timeline-date">
                                                @if(in_array($booking->status, ['in_progress', 'completed']))
                                                    {{ $booking->started_at ? $booking->started_at->format('M d, Y h:i A') : 'Pending' }}
                                                @else
                                                    Pending
                                                @endif
                                            </div>
                                            <div class="timeline-description">Your ride is currently in progress.</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker {{ $booking->status === 'completed' ? 'completed' : ($booking->status === 'in_progress' ? 'active' : '') }}"></div>
                                        <div class="timeline-content">
                                            <div class="timeline-title">Ride Completed</div>
                                            <div class="timeline-date">
                                                @if($booking->status === 'completed')
                                                    {{ $booking->completed_at ? $booking->completed_at->format('M d, Y h:i A') : 'Pending' }}
                                                @else
                                                    Pending
                                                @endif
                                            </div>
                                            <div class="timeline-description">Your ride has been successfully completed.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($booking->booking_type !== 'hourly')
                        <div class="mt-4">
                            <h5 class="mb-3">Route Map</h5>
                            <div id="map"></div>
                        </div>
                        @endif

                        <div class="text-center mt-4">
                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <a href="{{ route('client.dashboard') }}" class="btn btn-primary">Go to Dashboard</a>
                                <a href="{{ route('booking.index') }}" class="btn btn-outline-secondary">Book Another Ride</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<!-- Google Maps JavaScript API -->
@php
    $googleMapsApiKey = \App\Services\SettingsService::getGoogleMapsApiKey();
@endphp
@if($googleMapsApiKey)
<script src="https://maps.googleapis.com/maps/api/js?key={{ $googleMapsApiKey }}&callback=initMap" async defer></script>
@else
<script>
    console.error('Google Maps API key is not configured. Please set it in the admin settings.');
</script>
@endif
<script>
    function initMap() {
        @if($booking->booking_type !== 'hourly' && $booking->pickup_lat && $booking->pickup_lng && $booking->dropoff_lat && $booking->dropoff_lng)
            // Create map
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: {{ $booking->pickup_lat }}, lng: {{ $booking->pickup_lng }} }
            });

            // Create markers for pickup and dropoff
            const pickupMarker = new google.maps.Marker({
                position: { lat: {{ $booking->pickup_lat }}, lng: {{ $booking->pickup_lng }} },
                map: map,
                title: 'Pickup Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
                }
            });

            const dropoffMarker = new google.maps.Marker({
                position: { lat: {{ $booking->dropoff_lat }}, lng: {{ $booking->dropoff_lng }} },
                map: map,
                title: 'Dropoff Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
                }
            });

            // Create route
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            // Prepare waypoints for via stops
            const waypoints = [];
            @if($booking->via_stops && count($booking->via_stops) > 0)
                @foreach($booking->via_stops as $viaStop)
                    @if(isset($viaStop['lat']) && isset($viaStop['lng']) && $viaStop['lat'] && $viaStop['lng'])
                        waypoints.push({
                            location: { lat: {{ $viaStop['lat'] }}, lng: {{ $viaStop['lng'] }} },
                            stopover: true
                        });
                    @endif
                @endforeach
            @endif

            // Get directions
            directionsService.route({
                origin: { lat: {{ $booking->pickup_lat }}, lng: {{ $booking->pickup_lng }} },
                destination: { lat: {{ $booking->dropoff_lat }}, lng: {{ $booking->dropoff_lng }} },
                waypoints: waypoints,
                optimizeWaypoints: false,
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);

                    // Add info windows
                    const pickupInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Pickup:</strong> {{ $booking->pickup_address }}</div>'
                    });

                    const dropoffInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Dropoff:</strong> {{ $booking->dropoff_address }}</div>'
                    });

                    pickupMarker.addListener('click', function() {
                        pickupInfoWindow.open(map, pickupMarker);
                    });

                    dropoffMarker.addListener('click', function() {
                        dropoffInfoWindow.open(map, dropoffMarker);
                    });

                    // Open pickup info window by default
                    pickupInfoWindow.open(map, pickupMarker);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        @elseif($booking->booking_type !== 'hourly')
            // If we don't have coordinates, use the addresses
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: 51.5074, lng: -0.1278 } // Default to London
            });

            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            directionsService.route({
                origin: '{{ $booking->pickup_address }}',
                destination: '{{ $booking->dropoff_address }}',
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        @endif
    }

    // Refresh tracking information
    document.getElementById('refreshTracking').addEventListener('click', function() {
        location.reload();
    });
</script>
@endsection
