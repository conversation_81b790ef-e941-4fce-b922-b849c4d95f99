<?php

namespace Database\Seeders;

use App\Models\BlogPost;
use App\Models\Comment;
use App\Models\User;
use Illuminate\Database\Seeder;

class CommentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get blog posts
        $posts = BlogPost::all();

        if ($posts->isEmpty()) {
            // If no posts exist, run the BlogPostSeeder first
            $this->call(BlogPostSeeder::class);
            $posts = BlogPost::all();
        }

        // Get client users
        $clients = User::where('role', 'client')->get();

        if ($clients->isEmpty()) {
            // If no client users exist, use all users
            $clients = User::all();
        }

        $comments = [
            [
                'content' => 'Great article! I really enjoyed reading this and found the information very helpful.',
                'is_approved' => true,
            ],
            [
                'content' => 'Thanks for sharing these tips. I\'ll definitely be using them on my next trip.',
                'is_approved' => true,
            ],
            [
                'content' => 'I\'ve been using Ynr Cars for years and have always had a great experience. Highly recommended!',
                'is_approved' => true,
            ],
            [
                'content' => 'The new professional fleet sounds amazing! Can\'t wait to try it out on my next booking.',
                'is_approved' => true,
            ],
            [
                'content' => 'I had a similar experience with Ynr Cars for my wedding. The service was impeccable!',
                'is_approved' => true,
            ],
            [
                'content' => 'Interesting insights about the future of transportation. I\'m curious to see how these trends develop.',
                'is_approved' => true,
            ],
            [
                'content' => 'Do you offer any discounts for regular clients?',
                'is_approved' => false,
            ],
            [
                'content' => 'I had a question about your services. What\'s the best way to contact client support?',
                'is_approved' => false,
            ],
        ];

        // Add 2-3 comments to each post
        foreach ($posts as $post) {
            // Get 2-3 random comments
            $randomComments = array_rand($comments, rand(2, 3));

            foreach ($randomComments as $index) {
                // Get a random client
                $client = $clients->random();

                // Create the comment
                Comment::create([
                    'blog_post_id' => $post->id,
                    'user_id' => $client->id,
                    'content' => $comments[$index]['content'],
                    'is_approved' => $comments[$index]['is_approved'],
                ]);
            }
        }
    }
}
