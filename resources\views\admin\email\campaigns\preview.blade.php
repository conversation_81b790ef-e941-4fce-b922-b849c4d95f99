<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campaign Preview - {{ $campaign->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .email-preview {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .email-content {
            padding: 30px;
            line-height: 1.6;
        }
        .email-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
        .preview-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Preview Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h4 mb-1">📢 Campaign Preview</h2>
                        <p class="text-muted mb-0">{{ $campaign->name }}</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.close()">
                            <i class="fas fa-times me-1"></i> Close
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="preview-info">
                    <div class="row">
                        <div class="col-md-6">
                            <strong><i class="fas fa-info-circle me-1"></i> Campaign Information:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>Name:</strong> {{ $campaign->name }}</li>
                                <li><strong>Status:</strong> {{ ucfirst($campaign->status) }}</li>
                                <li><strong>Recipients:</strong> {{ ucfirst($campaign->recipient_type) }} ({{ $recipientCount ?? $campaign->total_recipients }} users)</li>
                                @if($campaign->scheduled_at)
                                    <li><strong>Scheduled:</strong> {{ $campaign->scheduled_at->format('M j, Y g:i A') }}</li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <strong><i class="fas fa-user me-1"></i> Sample Recipient:</strong>
                            <div class="mt-2 small">
                                @if(isset($sampleRecipient))
                                    <div><strong>Name:</strong> {{ $sampleRecipient->name }}</div>
                                    <div><strong>Email:</strong> {{ $sampleRecipient->email }}</div>
                                    <div><strong>Type:</strong> {{ ucfirst($sampleRecipient->role ?? 'user') }}</div>
                                @else
                                    <div class="text-muted">No recipients found for preview</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Preview -->
        <div class="row">
            <div class="col-12">
                <div class="email-preview">
                    <!-- Email Header -->
                    <div class="email-header">
                        <h3 class="mb-1">{{ \App\Services\SettingsService::getCompanyName() ?? 'YNR Cars' }}</h3>
                        <p class="mb-0 opacity-75">Professional Transportation Services</p>
                    </div>

                    <!-- Email Subject -->
                    <div class="bg-light p-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Subject:</strong>
                                <span class="ms-2">{{ $campaign->renderSubject($sampleData ?? []) }}</span>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> {{ now()->format('M j, Y g:i A') }}
                            </small>
                        </div>
                    </div>

                    <!-- Email Content -->
                    <div class="email-content">
                        <div style="white-space: pre-wrap;">{{ $campaign->renderContent($sampleData ?? []) }}</div>
                    </div>

                    <!-- Email Footer -->
                    <div class="email-footer">
                        <div class="mb-2">
                            <strong>{{ \App\Services\SettingsService::getCompanyName() ?? 'YNR Cars' }}</strong>
                        </div>
                        <div class="mb-2">
                            📧 {{ \App\Services\SettingsService::getCompanyEmail() ?? '<EMAIL>' }} | 
                            📞 {{ \App\Services\SettingsService::getCompanyPhone() ?? '+44 ************' }}
                        </div>
                        <div class="text-muted">
                            This is an automated email from our booking system. Please do not reply to this email.
                        </div>
                        <div class="mt-2">
                            <a href="#" class="text-muted small">Unsubscribe from these emails</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Source -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📝 Campaign Source</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Original Subject:</h6>
                                <div class="p-2 bg-light rounded mb-3">
                                    <code>{{ $campaign->subject }}</code>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Rendered Subject:</h6>
                                <div class="p-2 bg-light rounded mb-3">
                                    {{ $campaign->renderSubject($sampleData ?? []) }}
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary">Original Content:</h6>
                        <div class="p-3 bg-light rounded mb-3" style="white-space: pre-wrap; font-family: monospace; font-size: 12px;">{{ $campaign->content }}</div>

                        @if($campaign->template)
                            <h6 class="text-primary">Template Used:</h6>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">{{ $campaign->template->name }}</span>
                                <a href="{{ route('admin.email.templates.show', $campaign->template) }}" target="_blank" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-external-link-alt me-1"></i> View Template
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Recipient Targeting -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">👥 Recipient Targeting</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Target Audience:</h6>
                                <ul class="mb-0">
                                    <li><strong>Type:</strong> {{ ucfirst($campaign->recipient_type) }}</li>
                                    <li><strong>Total Recipients:</strong> {{ $recipientCount ?? $campaign->total_recipients }}</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Filters Applied:</h6>
                                @if($campaign->recipient_criteria && count($campaign->recipient_criteria) > 0)
                                    <ul class="mb-0">
                                        @foreach($campaign->recipient_criteria as $key => $value)
                                            @if($value)
                                                <li>{{ ucwords(str_replace('_', ' ', $key)) }}: {{ is_bool($value) ? 'Yes' : $value }}</li>
                                            @endif
                                        @endforeach
                                    </ul>
                                @else
                                    <p class="text-muted mb-0">No additional filters applied</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    @if($campaign->canBeEdited())
                        <a href="{{ route('admin.email.campaigns.edit', $campaign) }}" 
                           class="btn btn-warning" target="_blank">
                            <i class="fas fa-edit me-1"></i> Edit Campaign
                        </a>
                    @endif
                    <a href="{{ route('admin.email.campaigns.show', $campaign) }}" 
                       class="btn btn-info" target="_blank">
                        <i class="fas fa-eye me-1"></i> View Details
                    </a>
                    <a href="{{ route('admin.email.campaigns.index') }}" 
                       class="btn btn-secondary" target="_blank">
                        <i class="fas fa-list me-1"></i> All Campaigns
                    </a>
                    @if($campaign->canBeSent())
                        <button type="button" class="btn btn-success" onclick="sendCampaign()">
                            <i class="fas fa-paper-plane me-1"></i> Send Now
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function sendCampaign() {
            if (confirm('Are you sure you want to send this campaign now?')) {
                $.post('{{ route("admin.email.campaigns.send", $campaign) }}', {
                    _token: '{{ csrf_token() }}'
                }).done(function(response) {
                    if (response.success) {
                        alert('Campaign queued for sending!');
                        window.opener.location.reload();
                        window.close();
                    } else {
                        alert('Error: ' + response.message);
                    }
                }).fail(function() {
                    alert('Error sending campaign');
                });
            }
        }
    </script>
</body>
</html>

@php
    // Set sample data for preview if not provided
    if (!isset($sampleData)) {
        $sampleData = [
            'user_name' => $sampleRecipient->name ?? 'John Doe',
            'user_email' => $sampleRecipient->email ?? '<EMAIL>',
            'company_name' => \App\Services\SettingsService::getCompanyName() ?? 'YNR Cars',
            'company_email' => \App\Services\SettingsService::getCompanyEmail() ?? '<EMAIL>',
            'company_phone' => \App\Services\SettingsService::getCompanyPhone() ?? '+44 ************',
        ];
    }
@endphp
