<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\User;
use App\Models\Vehicle;
use App\Notifications\BookingStatusChanged;
use App\Notifications\NewRideAvailable;
use App\Services\GoogleMapsService;
use App\Services\PayPalService;
use App\Services\PayPalCardService;
use App\Services\SettingsService;
use App\Services\EmailService;
use App\Helpers\SettingsHelper;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Google Maps Service
     *
     * @var \App\Services\GoogleMapsService
     */
    protected $googleMapsService;

    /**
     * PayPal Service
     *
     * @var \App\Services\PayPalService
     */
    protected $paypalService;

    /**
     * PayPal Card Payment Service
     *
     * @var \App\Services\PayPalCardService
     */
    protected $paypalCardService;

    /**
     * Constructor
     *
     * @param \App\Services\GoogleMapsService $googleMapsService
     * @param \App\Services\PayPalService $paypalService
     * @param \App\Services\PayPalCardService $paypalCardService
     */
    public function __construct(
        GoogleMapsService $googleMapsService,
        PayPalService $paypalService,
        PayPalCardService $paypalCardService
    ) {
        $this->middleware('auth')->except([
            'calculateFare',
            'index',
            'store',
            'guestReview',
            'saveGuestBooking',
            'authCheck',
            'showGuestLogin',
            'processGuestLogin',
            'showGuestRegister',
            'processGuestRegister',
            'showClientDetails',
            'saveClientDetails'
        ]);
        $this->googleMapsService = $googleMapsService;
        $this->paypalService = $paypalService;
        $this->paypalCardService = $paypalCardService;
    }

    /**
     * Display the booking form
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        // Check if guest bookings are allowed
        $allowGuestBookings = SettingsService::allowGuestBookings();

        // If guest bookings are not allowed and user is not authenticated, redirect to login
        if (!$allowGuestBookings && !Auth::check()) {
            return redirect()->route('login')
                ->with('message', 'Please login to create a booking.');
        }

        $vehicles = Vehicle::where('is_active', true)->get();

        // Get booking settings
        $maxBookingDate = SettingsService::getMaxBookingDate()->format('Y-m-d');
        $minHourlyDuration = SettingsService::getMinimumHourlyDuration();

        // Get localization settings
        $currencyCode = SettingsService::getCurrencyCode();
        $currencySymbol = SettingsService::getCurrencySymbol();
        $countryCode = SettingsService::getCountryCode();
        $distanceUnit = SettingsService::getDistanceUnit();

        // Get Google Maps API key and autocomplete settings
        $googleMapsApiKey = SettingsService::getGoogleMapsApiKey();
        $autocompleteSettings = SettingsService::getAutocompleteSettings();

        // Get extra services settings
        $extraServicesSettings = SettingsService::getExtraServicesSettings();

        // Get airports for airport transfer option
        $airports = \App\Models\Airport::orderBy('name')->get();

        // Handle parameters from home form
        $homeFormData = $this->processHomeFormData($request);

        return view('booking.index', compact(
            'vehicles',
            'airports',
            'maxBookingDate',
            'minHourlyDuration',
            'currencyCode',
            'currencySymbol',
            'countryCode',
            'distanceUnit',
            'googleMapsApiKey',
            'autocompleteSettings',
            'extraServicesSettings',
            'homeFormData'
        ));
    }

    /**
     * Process home form data and prepare for booking form
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    private function processHomeFormData(Request $request)
    {
        $data = [
            'has_data' => false,
            'booking_type' => 'one_way',
            'pickup_address' => '',
            'dropoff_address' => '',
            'pickup_date' => '',
            'pickup_time' => '',
            'return_date' => '',
            'return_time' => '',
            'passengers' => 2,
            'airport_direction' => 'to_airport',
            'airport_name' => '',
            'airport_id' => null,
            'vehicle_id' => null,
        ];

        // Check if we have data from home form
        if ($request->has('pickup') || $request->has('dropoff') || $request->has('booking_type') || $request->has('type')) {
            $data['has_data'] = true;

            // Get booking type
            $bookingType = $request->input('booking_type', $request->input('type', 'one_way'));
            $data['booking_type'] = $bookingType;

            // Get addresses
            $data['pickup_address'] = $request->input('pickup', '');
            $data['dropoff_address'] = $request->input('dropoff', '');

            // Get date and time
            $data['pickup_date'] = $request->input('date', '');
            $data['pickup_time'] = $request->input('time', '');
            $data['return_date'] = $request->input('return_date', '');
            $data['return_time'] = $request->input('return_time', '');

            // Get passengers
            $data['passengers'] = $request->input('passengers', 2);

            // Handle airport transfer specific data
            if ($bookingType === 'airport' || $bookingType === 'airport_transfer') {
                $data['booking_type'] = 'airport_transfer';
                $data['airport_direction'] = $request->input('airport_direction', 'to_airport');
                $data['airport_name'] = $request->input('airport_name', '');

                // Try to find airport by name
                if ($data['airport_name']) {
                    $airport = \App\Models\Airport::where('name', 'LIKE', '%' . $data['airport_name'] . '%')
                        ->orWhere('code', 'LIKE', '%' . $data['airport_name'] . '%')
                        ->first();
                    if ($airport) {
                        $data['airport_id'] = $airport->id;
                        $data['airport_name'] = $airport->name;
                    }
                }

                // Set pickup/dropoff based on direction
                if ($data['airport_direction'] === 'to_airport') {
                    $data['pickup_address'] = $request->input('pickup', '');
                    $data['dropoff_address'] = $data['airport_name'];
                } else {
                    $data['pickup_address'] = $data['airport_name'];
                    $data['dropoff_address'] = $request->input('dropoff', '');
                }
            }

            // Handle return trip
            if ($bookingType === 'return' || $bookingType === 'round_trip') {
                $data['booking_type'] = 'return';
            }

            // Get vehicle ID if specified
            $data['vehicle_id'] = $request->input('vehicle_id', null);
        }

        return $data;
    }

    /**
     * Calculate fare based on pickup and dropoff locations
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateFare(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pickup_address' => 'required|string',
            'dropoff_address' => 'required_if:booking_type,one_way,return,airport_transfer',
            'vehicle_id' => 'required|exists:vehicles,id',
            'booking_type' => 'required|in:one_way,return,hourly,airport_transfer',
            'duration_hours' => 'required_if:booking_type,hourly|nullable|integer|min:1',
            'pickup_date' => 'nullable|date',
            'pickup_time' => 'nullable|string',
            'extra_services' => 'nullable|array',
            'passengers' => 'nullable|integer|min:1|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $pickup = $request->input('pickup_address');
        $vehicleId = $request->input('vehicle_id');
        $bookingType = $request->input('booking_type');
        $durationHours = $request->input('duration_hours', 1);

        // Get pickup date and time if provided
        $pickupDate = $request->input('pickup_date');
        $pickupTime = $request->input('pickup_time');
        $pickupDateTime = null;

        // Format pickup date and time for surcharge verification
        if ($pickupDate && $pickupTime) {
            $pickupDateTime = $pickupDate . ' ' . $pickupTime . ':00';
            \Log::info('Pickup date and time for fare calculation', [
                'pickup_date' => $pickupDate,
                'pickup_time' => $pickupTime,
                'pickup_datetime' => $pickupDateTime
            ]);
        }

        // Get vehicle details
        $vehicle = Vehicle::findOrFail($vehicleId);

        // Get extra services data
        $extraServices = $request->input('extra_services', []);
        $passengers = $request->input('passengers', 2);

        // Calculate extra services total
        $extraServicesTotal = $this->calculateExtraServicesTotal($extraServices);

        // Initialize fare
        $fare = 0;
        $fareDetails = [];

        if ($bookingType === 'hourly') {
            // For hourly bookings, use the vehicle-specific pricing
            $vehiclePricing = [
                'base_fare' => $vehicle->base_fare,
                'price_per_km' => $vehicle->price_per_km,
                'price_per_hour' => $vehicle->price_per_hour,
                'booking_fee' => $vehicle->booking_fee,
                'tax_rate' => $vehicle->tax_rate,
                'airport_surcharge' => $vehicle->airport_surcharge,
                'waiting_fee_per_minute' => $vehicle->waiting_fee_per_minute,
                'cancellation_fee' => $vehicle->cancellation_fee,
                'night_surcharge' => $vehicle->night_surcharge,
                'weekend_surcharge' => $vehicle->weekend_surcharge,
                'holiday_surcharge' => $vehicle->holiday_surcharge
            ];

            // Calculate hourly rate
            $hourlyRate = $vehicle->price_per_hour ?? ($vehicle->price_per_km * 15); // Assume 15km per hour if no hourly rate
            $hourlyCharge = $hourlyRate * $durationHours;

            // Calculate subtotal
            $subtotal = ($vehicle->base_fare ?? 0) + $hourlyCharge;

            // Add booking fee
            $bookingFee = $vehicle->booking_fee ?? 2.50;
            $subtotal += $bookingFee;

            // Check if it's a weekend and add weekend surcharge if applicable
            $isWeekend = (date('N') >= 6); // 6 = Saturday, 7 = Sunday
            if ($isWeekend) {
                $subtotal += $vehicle->weekend_surcharge ?? 0;
            }

            // Check if it's night time (10 PM - 6 AM) and add night surcharge if applicable
            $hour = (int)date('H');
            $isNightTime = ($hour >= 22 || $hour < 6);
            if ($isNightTime) {
                $subtotal += $vehicle->night_surcharge ?? 0;
            }

            // Add extra services to subtotal
            $subtotal += $extraServicesTotal;

            // Calculate tax if applicable
            $taxAmount = 0;
            if ($vehicle->tax_rate > 0) {
                $taxAmount = $subtotal * ($vehicle->tax_rate / 100);
            }

            // Calculate total fare with tax
            $totalFare = $subtotal + $taxAmount;

            $fareDetails = [
                'base_fare' => $vehicle->base_fare ?? 0,
                'hourly_rate' => $hourlyRate,
                'hours' => $durationHours,
                'hourly_charge' => $hourlyCharge,
                'booking_fee' => $bookingFee,
                'extra_services_total' => $extraServicesTotal,
                'extra_services' => $this->getExtraServicesDetails($extraServices),
                'subtotal' => round($subtotal, 2),
                'tax_rate' => $vehicle->tax_rate ?? 0,
                'tax_amount' => round($taxAmount, 2),
                'total_fare' => round($totalFare, 2),
                'booking_type' => 'hourly',
                'passengers' => $passengers
            ];

            // Add surcharges to fare details if applicable
            if ($isWeekend) {
                $fareDetails['weekend_surcharge'] = $vehicle->weekend_surcharge ?? 0;
            }

            if ($isNightTime) {
                $fareDetails['night_surcharge'] = $vehicle->night_surcharge ?? 0;
            }

            $fare = $fareDetails['total_fare'];
        } else {
            // For one_way, return, and airport_transfer bookings, calculate based on distance
            $dropoff = $request->input('dropoff_address');

        // Handle airport transfer specific logic
        if ($bookingType === 'airport_transfer') {
            $airportDirection = $request->input('airport_direction');
            $airportId = $request->input('airport_id');

            if ($airportId) {
                $airport = \App\Models\Airport::find($airportId);
                if ($airport) {
                    if ($airportDirection === 'to_airport') {
                        // Going to airport: pickup is user address, dropoff is airport
                        $dropoff = $airport->name . ', ' . $airport->city;
                    } else {
                        // Coming from airport: pickup is airport, dropoff is user address
                        $pickup = $airport->name . ', ' . $airport->city;
                    }
                }
            }
        }

            try {
                // Get distance and duration from Google Maps API
                $distanceMatrix = $this->googleMapsService->getDistanceMatrix($pickup, $dropoff);

                // With our improved fallback, this should never be null, but just in case
                if (!$distanceMatrix) {
                    // Use a default distance of 10 km
                    $distanceMatrix = [
                        'distance' => [
                            'value' => 10000, // 10 km in meters
                            'text' => '10 km'
                        ],
                        'duration' => [
                            'value' => 1200, // 20 minutes in seconds
                            'text' => '20 mins'
                        ]
                    ];

                    \Log::warning('Using default distance matrix after all fallbacks failed', [
                        'pickup' => $pickup,
                        'dropoff' => $dropoff
                    ]);
                }

                // Calculate fare based on distance and vehicle rate
                $distance = $distanceMatrix['distance']['value']; // in meters
                $duration = $distanceMatrix['duration']['value']; // in seconds

                // Convert meters to kilometers
                $distanceKm = $distance / 1000;

                // Get directions for more detailed route information
                $directions = $this->googleMapsService->getDirections($pickup, $dropoff);

                // Use enhanced fare calculation with all vehicle-specific pricing details
                $vehiclePricing = [
                    'base_fare' => $vehicle->base_fare,
                    'price_per_km' => $vehicle->price_per_km,
                    'price_per_hour' => $vehicle->price_per_hour,
                    'booking_fee' => $vehicle->booking_fee,
                    'tax_rate' => $vehicle->tax_rate,
                    'airport_surcharge' => $vehicle->airport_surcharge,
                    'waiting_fee_per_minute' => $vehicle->waiting_fee_per_minute,
                    'cancellation_fee' => $vehicle->cancellation_fee,
                    'night_surcharge' => $vehicle->night_surcharge,
                    'weekend_surcharge' => $vehicle->weekend_surcharge,
                    'holiday_surcharge' => $vehicle->holiday_surcharge,
                    'via_charge' => $vehicle->via_charge
                ];

                // Get via stops from request
                $viaStops = $request->input('via_stops', []);

                $fareDetails = $this->googleMapsService->calculateFare(
                    $distance,
                    5.0,
                    $vehicle->price_per_km,
                    $vehicle->type,
                    $bookingType,
                    $durationHours,
                    $vehiclePricing,
                    $pickupDateTime,
                    $extraServices,
                    $passengers,
                    $viaStops
                );

                $fare = $fareDetails['total_fare'];

                return response()->json([
                    'success' => true,
                    'distance' => $distanceMatrix['distance']['text'],
                    'duration' => $distanceMatrix['duration']['text'],
                    'fare' => $fare,
                    'fare_details' => $fareDetails,
                    'currency' => SettingsService::getCurrencyCode(),
                    'currency_symbol' => SettingsService::getCurrencySymbol(),
                    'route' => $directions ? [
                        'start_address' => $directions['start_address'],
                        'end_address' => $directions['end_address'],
                        'polyline' => $directions['overview_polyline'],
                    ] : null,
                ]);
            } catch (\Exception $e) {
                // If Google Maps API fails, use a simplified calculation
                // This is a fallback for demo purposes
                $estimatedDistance = 10; // Assume 10km
                $fare = $estimatedDistance * $vehicle->price_per_km;

                // For return trips, double the fare
                if ($bookingType === 'return') {
                    $fare *= 2;
                }

                // Use the vehicle-specific pricing
                $vehiclePricing = [
                    'base_fare' => $vehicle->base_fare,
                    'price_per_km' => $vehicle->price_per_km,
                    'price_per_hour' => $vehicle->price_per_hour,
                    'booking_fee' => $vehicle->booking_fee,
                    'tax_rate' => $vehicle->tax_rate,
                    'airport_surcharge' => $vehicle->airport_surcharge,
                    'waiting_fee_per_minute' => $vehicle->waiting_fee_per_minute,
                    'cancellation_fee' => $vehicle->cancellation_fee,
                    'night_surcharge' => $vehicle->night_surcharge,
                    'weekend_surcharge' => $vehicle->weekend_surcharge,
                    'holiday_surcharge' => $vehicle->holiday_surcharge,
                    'via_charge' => $vehicle->via_charge
                ];

                // Calculate distance charge
                $distanceCharge = $estimatedDistance * $vehicle->price_per_km;

                // Calculate subtotal
                $subtotal = ($vehicle->base_fare ?? 0) + $distanceCharge;

                // Add booking fee
                $bookingFee = $vehicle->booking_fee ?? 2.50;
                $subtotal += $bookingFee;

                // Add airport surcharge if applicable
                if ($bookingType === 'airport_transfer') {
                    $airportSurcharge = $vehicle->airport_surcharge ?? 15.00;
                    $subtotal += $airportSurcharge;
                }

                // Check if it's a weekend and add weekend surcharge if applicable
                $isWeekend = false;
                $isNightTime = false;

                if ($pickupDateTime) {
                    try {
                        // Parse the pickup date time
                        $pickupDateObj = new \DateTime($pickupDateTime);

                        // Check if it's a weekend (Saturday or Sunday)
                        $dayOfWeek = (int)$pickupDateObj->format('N');
                        $isWeekend = ($dayOfWeek >= 6); // 6 = Saturday, 7 = Sunday

                        // Check if it's night time (10 PM - 6 AM)
                        $hour = (int)$pickupDateObj->format('H');
                        $isNightTime = ($hour >= 22 || $hour < 6);

                        \Log::info('Fallback surcharge verification', [
                            'pickup_datetime' => $pickupDateTime,
                            'day_of_week' => $dayOfWeek,
                            'hour' => $hour,
                            'is_weekend' => $isWeekend,
                            'is_night_time' => $isNightTime
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Error parsing pickup date time for fallback surcharge verification', [
                            'pickup_datetime' => $pickupDateTime,
                            'error' => $e->getMessage()
                        ]);

                        // Fallback to current date/time if parsing fails
                        $isWeekend = (date('N') >= 6);
                        $hour = (int)date('H');
                        $isNightTime = ($hour >= 22 || $hour < 6);
                    }
                } else {
                    // Fallback to current date/time if no pickup date/time provided
                    $isWeekend = (date('N') >= 6);
                    $hour = (int)date('H');
                    $isNightTime = ($hour >= 22 || $hour < 6);
                }

                // Add weekend surcharge if applicable
                if ($isWeekend && ($vehicle->weekend_surcharge ?? 0) > 0) {
                    $subtotal += $vehicle->weekend_surcharge ?? 0;
                }

                // Add night surcharge if applicable
                if ($isNightTime && ($vehicle->night_surcharge ?? 0) > 0) {
                    $subtotal += $vehicle->night_surcharge ?? 0;
                }

                // Add extra services to subtotal
                $subtotal += $extraServicesTotal;

                // Calculate via charges
                $viaStops = $request->input('via_stops', []);
                $viaCharges = 0;
                $viaCount = 0;
                if (!empty($viaStops) && is_array($viaStops)) {
                    $viaCount = count($viaStops);
                    $viaChargePerStop = $vehicle->via_charge ?? 5.00;
                    $viaCharges = $viaCount * $viaChargePerStop;
                    $subtotal += $viaCharges;
                }

                // Calculate tax if applicable
                $taxAmount = 0;
                if ($vehicle->tax_rate > 0) {
                    $taxAmount = $subtotal * ($vehicle->tax_rate / 100);
                }

                // Calculate total fare with tax
                $totalFare = $subtotal + $taxAmount;

                $fareDetails = [
                    'base_fare' => $vehicle->base_fare ?? 0,
                    'distance_km' => $estimatedDistance,
                    'price_per_km' => $vehicle->price_per_km,
                    'distance_charge' => $distanceCharge,
                    'booking_fee' => $bookingFee,
                    'extra_services_total' => $extraServicesTotal,
                    'extra_services' => $this->getExtraServicesDetails($extraServices),
                    'via_charges' => $viaCharges,
                    'via_count' => $viaCount,
                    'subtotal' => round($subtotal, 2),
                    'tax_rate' => $vehicle->tax_rate ?? 0,
                    'tax_amount' => round($taxAmount, 2),
                    'total_fare' => round($totalFare, 2),
                    'booking_type' => $bookingType,
                    'passengers' => $passengers
                ];

                // Add surcharges to fare details if applicable
                if ($bookingType === 'airport_transfer') {
                    $fareDetails['airport_surcharge'] = $vehicle->airport_surcharge ?? 15.00;
                }

                if ($isWeekend) {
                    $fareDetails['weekend_surcharge'] = $vehicle->weekend_surcharge ?? 0;
                }

                if ($isNightTime) {
                    $fareDetails['night_surcharge'] = $vehicle->night_surcharge ?? 0;
                }

                $fare = $fareDetails['total_fare'];
            }
        }

        return response()->json([
            'success' => true,
            'fare' => $fare,
            'fare_details' => $fareDetails,
            'currency' => SettingsService::getCurrencyCode(),
            'currency_symbol' => SettingsService::getCurrencySymbol()
        ]);
    }

    /**
     * Store a new booking
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Get minimum hourly duration from settings
        $minHourlyDuration = SettingsService::getMinimumHourlyDuration();
        $maxBookingDate = SettingsService::getMaxBookingDate();

        // Create dynamic validation rules based on booking type
        $rules = [
            'pickup_datetime' => 'required|string',
            'return_datetime' => 'required_if:booking_type,return|nullable|string',
            'duration_hours' => "required_if:booking_type,hourly|nullable|integer|min:{$minHourlyDuration}",
            'vehicle_id' => 'required|exists:vehicles,id',
            'booking_type' => 'required|in:one_way,return,hourly,airport_transfer',
            'amount' => 'required|numeric|min:1',
            'distance_value' => 'nullable|numeric',
            'duration_value' => 'nullable|numeric',
            'fare_details' => 'nullable|string',
        ];

        // Handle address validation based on booking type
        $bookingType = $request->input('booking_type');
        if ($bookingType === 'airport_transfer') {
            $airportDirection = $request->input('airport_direction');
            if ($airportDirection === 'to_airport') {
                // Going to airport: pickup address required, dropoff is airport
                $rules['airport_pickup_address'] = 'required|string';
                $rules['airport_id'] = 'required|exists:airports,id';
            } else {
                // Coming from airport: dropoff address required, pickup is airport
                $rules['airport_dropoff_address'] = 'required|string';
                $rules['airport_id'] = 'required|exists:airports,id';
            }

        } elseif ($bookingType === 'hourly') {
            // Hourly: only pickup address required
            $rules['pickup_address'] = 'required|string';
        } elseif ($bookingType === 'return') {
            // Return: both return addresses required
            $rules['return_pickup_address'] = 'required|string';
            $rules['return_dropoff_address'] = 'required|string';
        } else {
            // One-way: both addresses required
            $rules['pickup_address'] = 'required|string';
            $rules['dropoff_address'] = 'required|string';
        }

        // Flight details validation for all booking types (all optional)
        $rules['flight_number'] = 'nullable|string|max:20';
        $rules['airline'] = 'nullable|string|max:100';
        $rules['departure_time'] = 'nullable|date';
        $rules['arrival_time'] = 'nullable|date';
        $rules['terminal'] = 'nullable|string|max:50';
        $rules['flight_status'] = 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived';
        $rules['flight_notes'] = 'nullable|string|max:500';

        // Prefixed flight information fields for different booking types
        $rules['oneway_flight_number'] = 'nullable|string|max:20';
        $rules['oneway_airline'] = 'nullable|string|max:100';
        $rules['oneway_departure_time'] = 'nullable|date';
        $rules['oneway_arrival_time'] = 'nullable|date|after_or_equal:oneway_departure_time';
        $rules['oneway_terminal'] = 'nullable|string|max:50';
        $rules['oneway_flight_status'] = 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived';
        $rules['oneway_flight_notes'] = 'nullable|string|max:500';

        $rules['return_flight_number'] = 'nullable|string|max:20';
        $rules['return_airline'] = 'nullable|string|max:100';
        $rules['return_departure_time'] = 'nullable|date';
        $rules['return_arrival_time'] = 'nullable|date|after_or_equal:return_departure_time';
        $rules['return_terminal'] = 'nullable|string|max:50';
        $rules['return_flight_status'] = 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived';
        $rules['return_flight_notes'] = 'nullable|string|max:500';

        $rules['hourly_flight_number'] = 'nullable|string|max:20';
        $rules['hourly_airline'] = 'nullable|string|max:100';
        $rules['hourly_departure_time'] = 'nullable|date';
        $rules['hourly_arrival_time'] = 'nullable|date|after_or_equal:hourly_departure_time';
        $rules['hourly_terminal'] = 'nullable|string|max:50';
        $rules['hourly_flight_status'] = 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived';
        $rules['hourly_flight_notes'] = 'nullable|string|max:500';

        // Custom error messages
        $messages = [
            'pickup_address.required' => 'The pickup address field is required.',
            'dropoff_address.required' => 'The dropoff address field is required.',
            'return_pickup_address.required' => 'The pickup address field is required.',
            'return_dropoff_address.required' => 'The dropoff address field is required.',
            'airport_pickup_address.required' => 'The pickup address field is required.',
            'airport_dropoff_address.required' => 'The dropoff address field is required.',
            'airport_id.required' => 'Please select an airport.',
            'airport_id.exists' => 'The selected airport is invalid.',
            'pickup_datetime.required' => 'The pickup date and time are required.',
            'vehicle_id.required' => 'Please select a vehicle.',
            'vehicle_id.exists' => 'The selected vehicle is invalid.',
            'amount.required' => 'The fare amount is required.',
            'amount.numeric' => 'The fare amount must be a valid number.',
            'amount.min' => 'The fare amount must be at least :min.',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);

        // Add custom validation for pickup date not being too far in the future
        $validator->after(function ($validator) use ($maxBookingDate) {
            try {
                $pickupDatetimeString = $validator->getData()['pickup_datetime'] ?? '';

                if (empty($pickupDatetimeString)) {
                    $validator->errors()->add(
                        'pickup_datetime',
                        'Pickup date and time are required.'
                    );
                    return;
                }

                // Try to parse the datetime with more flexible parsing
                $pickupDate = null;
                try {
                    $pickupDate = Carbon::parse($pickupDatetimeString);
                } catch (\Exception $e) {
                    // Try alternative parsing if the first attempt fails
                    try {
                        $pickupDate = Carbon::createFromFormat('Y-m-d H:i', $pickupDatetimeString);
                    } catch (\Exception $e2) {
                        $validator->errors()->add(
                            'pickup_datetime',
                            'Invalid date format. Please try again.'
                        );
                        return;
                    }
                }

                // Check if date is too far in the future
                if ($pickupDate->gt($maxBookingDate)) {
                    $validator->errors()->add(
                        'pickup_datetime',
                        'Pickup date cannot be more than ' . $maxBookingDate->diffInDays(now()) . ' days in the future.'
                    );
                }

                // Check if date is in the past (allow 1 hour buffer for current time)
                $minimumTime = now()->subHour();
                if ($pickupDate->lt($minimumTime)) {
                    $validator->errors()->add(
                        'pickup_datetime',
                        'Pickup date cannot be in the past.'
                    );
                }

                // Validate return date if present
                if (!empty($validator->getData()['return_datetime'])) {
                    try {
                        $returnDate = Carbon::parse($validator->getData()['return_datetime']);

                        if ($returnDate->lt($pickupDate)) {
                            $validator->errors()->add(
                                'return_datetime',
                                'Return date must be after pickup date.'
                            );
                        }
                    } catch (\Exception $e) {
                        $validator->errors()->add(
                            'return_datetime',
                            'Invalid return date format. Please try again.'
                        );
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Date validation error: ' . $e->getMessage());
                $validator->errors()->add(
                    'pickup_datetime',
                    'An error occurred while validating the date. Please try again.'
                );
            }
        });

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Convert datetime strings to Carbon instances
        try {
            $pickupDate = \Carbon\Carbon::parse($request->input('pickup_datetime'));
            $returnDate = $request->input('return_datetime') ? \Carbon\Carbon::parse($request->input('return_datetime')) : null;
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['pickup_datetime' => 'Invalid date format. Please try again.'])
                ->withInput();
        }

        // Geocode addresses to get coordinates based on booking type
        $pickupGeocode = null;
        $dropoffGeocode = null;

        $bookingType = $request->input('booking_type');

        if ($bookingType === 'airport_transfer') {
            $airportDirection = $request->input('airport_direction');

            if ($airportDirection === 'to_airport') {
                // Going to airport: geocode pickup address
                $pickupAddress = $request->input('airport_pickup_address');
                if ($pickupAddress) {
                    $pickupGeocode = $this->googleMapsService->geocodeAddress($pickupAddress);
                }

                // Get airport coordinates for dropoff
                $airportId = $request->input('airport_id');
                if ($airportId) {
                    $airport = \App\Models\Airport::find($airportId);
                    if ($airport) {
                        $dropoffGeocode = [
                            'lat' => $airport->latitude,
                            'lng' => $airport->longitude
                        ];
                    }
                }
            } else {
                // Coming from airport: geocode dropoff address
                $dropoffAddress = $request->input('airport_dropoff_address');
                if ($dropoffAddress) {
                    $dropoffGeocode = $this->googleMapsService->geocodeAddress($dropoffAddress);
                }

                // Get airport coordinates for pickup
                $airportId = $request->input('airport_id');
                if ($airportId) {
                    $airport = \App\Models\Airport::find($airportId);
                    if ($airport) {
                        $pickupGeocode = [
                            'lat' => $airport->latitude,
                            'lng' => $airport->longitude
                        ];
                    }
                }
            }
        } elseif ($bookingType === 'return') {
            // Return bookings: use return-specific field names
            $pickupAddress = $request->input('return_pickup_address');
            if ($pickupAddress) {
                $pickupGeocode = $this->googleMapsService->geocodeAddress($pickupAddress);
            }

            $dropoffAddress = $request->input('return_dropoff_address');
            if ($dropoffAddress) {
                $dropoffGeocode = $this->googleMapsService->geocodeAddress($dropoffAddress);
            }
        } else {
            // Regular bookings: geocode pickup address
            $pickupAddress = $request->input('pickup_address');
            if ($pickupAddress) {
                $pickupGeocode = $this->googleMapsService->geocodeAddress($pickupAddress);
            }

            // Geocode dropoff address for non-hourly bookings
            if ($bookingType !== 'hourly') {
                $dropoffAddress = $request->input('dropoff_address');
                if ($dropoffAddress) {
                    $dropoffGeocode = $this->googleMapsService->geocodeAddress($dropoffAddress);
                }
            }
        }

        // Check if user is authenticated
        if (Auth::check()) {
            // Create booking for authenticated user
            $booking = new Booking();
            $booking->user_id = Auth::id();
            $booking->vehicle_id = $request->input('vehicle_id');
            $booking->booking_number = Booking::generateBookingNumber();
            $booking->booking_type = $request->input('booking_type');

            // Set addresses based on booking type
            if ($request->input('booking_type') === 'airport_transfer') {
                $airportDirection = $request->input('airport_direction');
                $airportId = $request->input('airport_id');
                $airport = \App\Models\Airport::find($airportId);

                if ($airportDirection === 'to_airport') {
                    // Going to airport
                    $booking->pickup_address = $request->input('airport_pickup_address');
                    $booking->dropoff_address = $airport ? $airport->name . ', ' . $airport->city : '';
                } else {
                    // Coming from airport
                    $booking->pickup_address = $airport ? $airport->name . ', ' . $airport->city : '';
                    $booking->dropoff_address = $request->input('airport_dropoff_address');
                }
            } elseif ($request->input('booking_type') === 'return') {
                // Return bookings
                $booking->pickup_address = $request->input('return_pickup_address');
                $booking->dropoff_address = $request->input('return_dropoff_address');
            } else {
                // Regular bookings (one-way, hourly)
                $booking->pickup_address = $request->input('pickup_address');
                if ($request->input('booking_type') !== 'hourly') {
                    $booking->dropoff_address = $request->input('dropoff_address');
                }
            }

            $booking->pickup_lat = $pickupGeocode ? $pickupGeocode['lat'] : null;
            $booking->pickup_lng = $pickupGeocode ? $pickupGeocode['lng'] : null;
            $booking->dropoff_lat = $dropoffGeocode ? $dropoffGeocode['lat'] : null;
            $booking->dropoff_lng = $dropoffGeocode ? $dropoffGeocode['lng'] : null;

            $booking->pickup_date = $pickupDate;
            $booking->return_date = $returnDate;
            $booking->duration_hours = $request->input('duration_hours');
            $booking->amount = $request->input('amount');
            $booking->distance_value = $request->input('distance_value');
            $booking->duration_value = $request->input('duration_value');
            $booking->status = 'pending';
            $booking->payment_status = 'pending';
            $booking->notes = $request->input('notes');

            // Handle via stops
            $viaStops = $request->input('via_stops', []);
            if (!empty($viaStops) && is_array($viaStops)) {
                $booking->via_stops = $viaStops;
                $booking->via_count = count($viaStops);

                // Calculate via charges
                $vehicle = \App\Models\Vehicle::find($request->input('vehicle_id'));
                if ($vehicle) {
                    $viaChargePerStop = $vehicle->via_charge ?? 5.00;
                    $booking->via_charges = $booking->via_count * $viaChargePerStop;
                }
            }

            // Handle airport transfer fields
            if ($request->input('booking_type') === 'airport_transfer') {
                $booking->airport_direction = $request->input('airport_direction');
                $booking->airport_surcharge = $request->input('airport_surcharge', 0);

                if ($request->input('airport_direction') === 'to_airport') {
                    $booking->dropoff_airport_id = $request->input('airport_id');
                } else {
                    $booking->pickup_airport_id = $request->input('airport_id');
                }

            }

            // Handle flight information for all booking types
            $bookingType = $request->input('booking_type');
            $flightPrefix = '';

            // Determine the field prefix based on booking type
            if ($bookingType === 'one_way') {
                $flightPrefix = 'oneway_';
            } elseif ($bookingType === 'return') {
                $flightPrefix = 'return_';
            } elseif ($bookingType === 'hourly') {
                $flightPrefix = 'hourly_';
            }

            // Get flight information with appropriate prefix or fallback to base names
            $booking->flight_number = $request->input($flightPrefix . 'flight_number') ?: $request->input('flight_number');
            $booking->airline = $request->input($flightPrefix . 'airline') ?: $request->input('airline');
            $booking->terminal = $request->input($flightPrefix . 'terminal') ?: $request->input('terminal');
            $booking->flight_status = $request->input($flightPrefix . 'flight_status') ?: $request->input('flight_status');
            $booking->flight_notes = $request->input($flightPrefix . 'flight_notes') ?: $request->input('flight_notes');

            // Handle flight times with prefix support
            $departureTime = $request->input($flightPrefix . 'departure_time') ?: $request->input('departure_time');
            if ($departureTime) {
                try {
                    $booking->departure_time = \Carbon\Carbon::parse($departureTime);
                } catch (\Exception $e) {
                    \Log::warning('Invalid departure time format: ' . $departureTime);
                }
            }

            $arrivalTime = $request->input($flightPrefix . 'arrival_time') ?: $request->input('arrival_time');
            if ($arrivalTime) {
                try {
                    $booking->arrival_time = \Carbon\Carbon::parse($arrivalTime);
                } catch (\Exception $e) {
                    \Log::warning('Invalid arrival time format: ' . $arrivalTime);
                }
            }

            // Save fare details if provided
            if ($request->has('fare_details') && !empty($request->input('fare_details'))) {
                try {
                    $fareDetails = json_decode($request->input('fare_details'), true);
                    $booking->fare_details = $fareDetails;
                } catch (\Exception $e) {
                    \Log::error('Error parsing fare details: ' . $e->getMessage());
                }
            }

            // Handle extra services (only save if enabled in settings)
            $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();
            $booking->meet_and_greet = $request->has('meet_and_greet') && $extraServicesSettings['meet_and_greet']['enabled'] ? true : false;
            $booking->child_seat = $request->has('child_seat') && $extraServicesSettings['child_seat']['enabled'] ? true : false;
            $booking->wheelchair_accessible = $request->has('wheelchair_accessible') && $extraServicesSettings['wheelchair_accessible']['enabled'] ? true : false;
            $booking->extra_luggage = $request->has('extra_luggage') && $extraServicesSettings['extra_luggage']['enabled'] ? true : false;

            $booking->save();

            // Add booking history
            $booking->addHistory('booking_created', [
                'booking_type' => $booking->booking_type,
                'amount' => $booking->amount,
                'vehicle_id' => $booking->vehicle_id,
            ]);

            // Notify the client about the booking
            Auth::user()->notify(new BookingStatusChanged($booking));

            // Redirect to payment page
            return redirect()->route('booking.payment', $booking->id);
        } else {
            // For guest users, store booking data in session
            $bookingData = [
                'vehicle_id' => $request->input('vehicle_id'),
                'booking_type' => $request->input('booking_type'),
                'pickup_lat' => $pickupGeocode ? $pickupGeocode['lat'] : null,
                'pickup_lng' => $pickupGeocode ? $pickupGeocode['lng'] : null,
                'dropoff_lat' => $dropoffGeocode ? $dropoffGeocode['lat'] : null,
                'dropoff_lng' => $dropoffGeocode ? $dropoffGeocode['lng'] : null,
                'pickup_date' => $pickupDate->format('Y-m-d H:i:s'),
                'pickup_datetime' => $pickupDate->format('Y-m-d H:i:s'), // Add this for consistency
                'return_date' => $returnDate ? $returnDate->format('Y-m-d H:i:s') : null,
                'return_datetime' => $returnDate ? $returnDate->format('Y-m-d H:i:s') : null, // Add this for consistency
                'duration_hours' => $request->input('duration_hours'),
                'amount' => $request->input('amount'),
                'distance_value' => $request->input('distance_value'),
                'duration_value' => $request->input('duration_value'),
                'notes' => $request->input('notes'),
                'via_stops' => $request->input('via_stops', []),
                'created_at' => now()->format('Y-m-d H:i:s')
            ];

            // Set addresses based on booking type
            if ($request->input('booking_type') === 'airport_transfer') {
                $airportDirection = $request->input('airport_direction');
                $airportId = $request->input('airport_id');
                $airport = \App\Models\Airport::find($airportId);

                if ($airportDirection === 'to_airport') {
                    // Going to airport
                    $bookingData['pickup_address'] = $request->input('airport_pickup_address');
                    $bookingData['dropoff_address'] = $airport ? $airport->name . ', ' . $airport->city : '';
                } else {
                    // Coming from airport
                    $bookingData['pickup_address'] = $airport ? $airport->name . ', ' . $airport->city : '';
                    $bookingData['dropoff_address'] = $request->input('airport_dropoff_address');
                }
            } elseif ($request->input('booking_type') === 'return') {
                // Return bookings
                $bookingData['pickup_address'] = $request->input('return_pickup_address');
                $bookingData['dropoff_address'] = $request->input('return_dropoff_address');
            } else {
                // Regular bookings (one-way, hourly)
                $bookingData['pickup_address'] = $request->input('pickup_address');
                if ($request->input('booking_type') !== 'hourly') {
                    $bookingData['dropoff_address'] = $request->input('dropoff_address');
                } else {
                    $bookingData['dropoff_address'] = null;
                }
            }

            // Handle airport transfer fields for guest bookings
            if ($request->input('booking_type') === 'airport_transfer') {
                $bookingData['airport_direction'] = $request->input('airport_direction');
                $bookingData['airport_surcharge'] = $request->input('airport_surcharge', 0);
                $bookingData['airport_id'] = $request->input('airport_id');

                if ($request->input('airport_direction') === 'to_airport') {
                    $bookingData['dropoff_airport_id'] = $request->input('airport_id');
                } else {
                    $bookingData['pickup_airport_id'] = $request->input('airport_id');
                }

                // Add flight details for guest bookings
                $bookingData['flight_number'] = $request->input('flight_number');
                $bookingData['airline'] = $request->input('airline');
                $bookingData['terminal'] = $request->input('terminal');
                $bookingData['flight_status'] = $request->input('flight_status');
                $bookingData['flight_notes'] = $request->input('flight_notes');

                // Handle flight times for guest bookings
                if ($request->input('departure_time')) {
                    try {
                        $bookingData['departure_time'] = \Carbon\Carbon::parse($request->input('departure_time'))->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        \Log::warning('Invalid departure time format for guest booking: ' . $request->input('departure_time'));
                    }
                }

                if ($request->input('arrival_time')) {
                    try {
                        $bookingData['arrival_time'] = \Carbon\Carbon::parse($request->input('arrival_time'))->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        \Log::warning('Invalid arrival time format for guest booking: ' . $request->input('arrival_time'));
                    }
                }
            }

            // Add fare details if provided
            if ($request->has('fare_details') && !empty($request->input('fare_details'))) {
                try {
                    $fareDetails = json_decode($request->input('fare_details'), true);
                    $bookingData['fare_details'] = $fareDetails;
                } catch (\Exception $e) {
                    \Log::error('Error parsing fare details for guest booking: ' . $e->getMessage());
                }
            }

            // Add extra services to guest booking data
            $bookingData['meet_and_greet'] = $request->has('meet_and_greet') ? 1 : 0;
            $bookingData['child_seat'] = $request->has('child_seat') ? 1 : 0;
            $bookingData['wheelchair_accessible'] = $request->has('wheelchair_accessible') ? 1 : 0;
            $bookingData['extra_luggage'] = $request->has('extra_luggage') ? 1 : 0;

            // Store in session
            session(['guest_booking' => $bookingData]);

            // Redirect to guest booking review page
            return redirect()->route('booking.guest.review');
        }
    }

    /**
     * Show payment page for a booking
     *
     * @param int $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showPayment($id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        // Get currency symbol for display
        $currencySymbol = SettingsService::getCurrencySymbol();

        return view('booking.payment', compact('booking', 'currencySymbol'));
    }

    /**
     * Process payment for a booking
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPayment(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        // Check payment method
        $paymentMethod = $request->input('payment_method', 'paypal');

        if ($paymentMethod === 'paypal') {
            // Create PayPal order
            $returnUrl = route('booking.payment.success', $booking->id);
            $cancelUrl = route('booking.payment.cancel', $booking->id);

            $order = $this->paypalService->createOrder(
                $booking->amount,
                SettingsService::getCurrencyCode(),
                $returnUrl,
                $cancelUrl
            );

            if (!$order || isset($order['error'])) {
                $errorMessage = 'Unable to create PayPal order. Please try again.';

                // Check for specific PayPal errors
                if (isset($order['error']) && is_array($order['error'])) {
                    $errorDetails = json_encode($order['error']);
                    if (strpos($errorDetails, 'PAYEE_ACCOUNT_RESTRICTED') !== false) {
                        $errorMessage = 'PayPal payment is temporarily unavailable. Please try a different payment method or contact support.';
                    }
                }

                return redirect()->back()->with('error', $errorMessage);
            }

            // Save order ID to session
            session(['paypal_order_id' => $order['id']]);

            // Redirect to PayPal checkout
            foreach ($order['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    return redirect()->away($link['href']);
                }
            }

            return redirect()->back()->with('error', 'Unable to redirect to PayPal. Please try again.');
        } elseif ($paymentMethod === 'pay_later') {
            // Process pay later payment
            return $this->processPayLaterPayment($request, $booking);
        } else {
            return redirect()->back()->with('error', 'Invalid payment method. Please try again.');
        }
    }

    /**
     * Process pay later payment for a booking
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Booking $booking
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function processPayLaterPayment(Request $request, Booking $booking)
    {
        try {
            // Update booking status
            $oldStatus = $booking->status;
            $booking->status = 'confirmed';
            $booking->payment_status = 'pending'; // Pay later payment is pending until collected
            $booking->notes = $booking->notes . "\n\nPay Later Notes: " . $request->input('notes', 'No special instructions provided.');
            $booking->save();

            // Create payment record
            $payment = new Payment();
            $payment->booking_id = $booking->id;
            $payment->user_id = Auth::id();
            $payment->transaction_id = 'PAY_LATER-' . time();
            $payment->payment_method = 'pay_later';
            $payment->amount = $booking->amount;
            $payment->status = 'pending'; // Pay later payment is pending until collected
            $payment->payment_details = json_encode([
                'notes' => $request->input('notes'),
                'payment_type' => 'pay_later',
                'created_at' => now()->format('Y-m-d H:i:s')
            ]);
            $payment->save();

            // Add booking history for status change
            $booking->addHistory('status_changed', [
                'old_status' => $oldStatus,
                'new_status' => 'confirmed',
                'reason' => 'Pay later selected'
            ]);

            // Add booking history for payment
            $booking->addHistory('payment_method_selected', [
                'payment_id' => $payment->id,
                'transaction_id' => $payment->transaction_id,
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method
            ]);

            try {
                // Send booking confirmation email
                EmailService::sendBookingConfirmation($booking);

                // Send admin notification for new booking
                EmailService::sendAdminNewBookingNotification($booking);

                // Notify the client about the booking confirmation
                $booking->user->notify(new BookingStatusChanged($booking, $oldStatus));

                // Notify available drivers about the new ride
                $availableDrivers = User::role('driver')->where('is_active', true)->get();
                Notification::send($availableDrivers, new NewRideAvailable($booking));
            } catch (\Exception $notificationException) {
                // Log notification error but continue with the process
                \Log::warning('Error sending notifications: ' . $notificationException->getMessage());
            }

            // Get currency symbol for display
            $currencySymbol = SettingsService::getCurrencySymbol();

            // Use the custom pay later success page
            return response()->view('booking.pay-later-success', [
                'booking' => $booking,
                'payment' => $payment,
                'currencySymbol' => $currencySymbol
            ]);
        } catch (\Exception $e) {
            \Log::error('Error processing pay later payment: ' . $e->getMessage());

            return redirect()->back()->with('error', 'An error occurred while processing your pay later payment. Please try again.');
        }
    }

    /**
     * Create a payment order for PayPal card payment
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPaymentIntent(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        try {
            // Create PayPal order
            $order = $this->paypalCardService->createPaymentOrder(
                $booking->amount,
                SettingsService::getCurrencyCode(),
                [
                    'booking_id' => $booking->id,
                    'booking_number' => $booking->booking_number,
                    'user_id' => Auth::id()
                ]
            );

            if (!$order || isset($order['error'])) {
                $errorMessage = 'Unable to create PayPal order. Please try again.';

                // Check for specific PayPal errors
                if (isset($order['error']) && is_array($order['error'])) {
                    $errorDetails = json_encode($order['error']);
                    if (strpos($errorDetails, 'PAYEE_ACCOUNT_RESTRICTED') !== false) {
                        $errorMessage = 'PayPal payment is temporarily unavailable. Please try a different payment method or contact support.';
                    }
                }

                return response()->json([
                    'success' => false,
                    'error' => $errorMessage
                ]);
            }

            return response()->json([
                'success' => true,
                'order_id' => $order['id'],
                'client_id' => SettingsHelper::getPaypalClientId()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error creating PayPal order: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'An error occurred. Please try again.'
            ]);
        }
    }

    /**
     * Process PayPal card payment for a booking
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processCardPayment(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        // Validate request
        $validated = $request->validate([
            'order_id' => 'required|string',
        ]);

        try {
            // Capture the PayPal order
            $result = $this->paypalCardService->capturePaymentOrder($validated['order_id']);

            if (!$result || isset($result['error'])) {
                return redirect()->back()->with('error', 'Unable to capture payment. Please try again.');
            }

            // Update booking status
            $oldStatus = $booking->status;
            $booking->status = 'confirmed';
            $booking->payment_status = 'completed';
            $booking->save();

            // Create payment record
            $payment = new Payment();
            $payment->booking_id = $booking->id;
            $payment->user_id = Auth::id();
            $payment->transaction_id = $result['id'];
            $payment->payment_method = 'paypal_card';
            $payment->amount = $booking->amount;
            $payment->status = 'completed';
            $payment->payment_details = json_encode($result);
            $payment->save();

            // Add booking history for status change
            $booking->addHistory('status_changed', [
                'old_status' => $oldStatus,
                'new_status' => 'confirmed',
                'reason' => 'Payment completed'
            ]);

            // Add booking history for payment
            $booking->addHistory('payment_completed', [
                'payment_id' => $payment->id,
                'transaction_id' => $payment->transaction_id,
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method
            ]);

            // Send booking confirmation and payment receipt emails
            EmailService::sendBookingConfirmation($booking);
            EmailService::sendPaymentReceipt($payment);

            // Send admin notification for new booking
            EmailService::sendAdminNewBookingNotification($booking);

            // Notify the client about the booking confirmation
            $booking->user->notify(new BookingStatusChanged($booking, $oldStatus));

            // Notify available drivers about the new ride
            $availableDrivers = User::role('driver')->where('is_active', true)->get();
            Notification::send($availableDrivers, new NewRideAvailable($booking));

            // Get currency symbol for display
            $currencySymbol = SettingsService::getCurrencySymbol();

            // Return the payment success view
            return view('booking.payment-success', compact('booking', 'payment', 'currencySymbol'));
        } catch (\Exception $e) {
            \Log::error('Error processing PayPal card payment: ' . $e->getMessage());

            return redirect()->back()->with('error', 'An error occurred while processing your payment. Please try again.');
        }
    }

    /**
     * Handle successful payment
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function paymentSuccess(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);
        $orderId = session('paypal_order_id');

        if (!$orderId) {
            return redirect()->route('booking.payment', $booking->id)
                ->with('error', 'Payment session expired. Please try again.');
        }

        // Capture the order
        $result = $this->paypalService->captureOrder($orderId);

        if (!$result || isset($result['error'])) {
            return redirect()->route('booking.payment', $booking->id)
                ->with('error', 'Unable to capture payment. Please try again.');
        }

        // Update booking status
        $oldStatus = $booking->status;
        $booking->status = 'confirmed';
        $booking->payment_status = 'completed';
        $booking->save();

        // Create payment record
        $payment = new Payment();
        $payment->booking_id = $booking->id;
        $payment->user_id = Auth::id();
        $payment->transaction_id = $result['id'];
        $payment->payment_method = 'paypal';
        $payment->amount = $booking->amount;
        $payment->status = 'completed';
        $payment->payment_details = json_encode($result);
        $payment->save();

        // Add booking history for status change
        $booking->addHistory('status_changed', [
            'old_status' => $oldStatus,
            'new_status' => 'confirmed',
            'reason' => 'Payment completed'
        ]);

        // Add booking history for payment
        $booking->addHistory('payment_completed', [
            'payment_id' => $payment->id,
            'transaction_id' => $payment->transaction_id,
            'amount' => $payment->amount,
            'payment_method' => $payment->payment_method
        ]);

        // Send booking confirmation and payment receipt emails
        EmailService::sendBookingConfirmation($booking);
        EmailService::sendPaymentReceipt($payment);

        // Send admin notification for new booking
        EmailService::sendAdminNewBookingNotification($booking);

        // Notify the client about the booking confirmation
        $booking->user->notify(new BookingStatusChanged($booking, $oldStatus));

        // Notify available drivers about the new ride
        $availableDrivers = User::role('driver')->where('is_active', true)->get();
        Notification::send($availableDrivers, new NewRideAvailable($booking));

        // Clear session
        session()->forget('paypal_order_id');

        // Get currency symbol for display
        $currencySymbol = SettingsService::getCurrencySymbol();

        // Return the payment success view
        return view('booking.payment-success', compact('booking', 'payment', 'currencySymbol'));
    }

    /**
     * Handle cancelled payment
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function paymentCancel(Request $request, $id)
    {
        $booking = Booking::where('user_id', Auth::id())->findOrFail($id);

        // Clear session
        session()->forget('paypal_order_id');

        return redirect()->route('booking.payment', $booking->id)
            ->with('warning', 'Payment was cancelled. Please try again.');
    }

    /**
     * Show booking confirmation
     *
     * @param int $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function confirmation($id)
    {
        $booking = Booking::with(['vehicle', 'payment'])
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // Get currency symbol for the view
        $currencySymbol = SettingsService::getCurrencySymbol();
        $currencyCode = SettingsService::getCurrencyCode();

        return view('booking.confirmation', compact('booking', 'currencySymbol', 'currencyCode'));
    }

    /**
     * Track booking
     *
     * @param int $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function trackBooking($id)
    {
        $booking = Booking::with(['vehicle', 'payment', 'driver', 'history' => function($query) {
                $query->orderBy('created_at', 'desc');
            }])
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        return view('booking.track', compact('booking'));
    }

    /**
     * Show guest booking review page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function guestReview()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            // For testing purposes, create a sample booking data
            if (app()->environment('local') && request()->has('test')) {
                $vehicle = Vehicle::where('is_active', true)->first();
                if (!$vehicle) {
                    return redirect()->route('booking.index')
                        ->with('error', 'No active vehicles found. Please add vehicles first.');
                }

                $bookingData = [
                    'vehicle_id' => $vehicle->id,
                    'booking_type' => 'one_way',
                    'pickup_address' => '123 Main St, London, UK',
                    'pickup_lat' => 51.5074,
                    'pickup_lng' => -0.1278,
                    'dropoff_address' => '456 Oxford St, London, UK',
                    'dropoff_lat' => 51.5152,
                    'dropoff_lng' => -0.1418,
                    'pickup_date' => now()->addDay()->format('Y-m-d H:i:s'),
                    'pickup_datetime' => now()->addDay()->format('Y-m-d H:i:s'),
                    'amount' => 50.00,
                    'distance_value' => 5.2,
                    'duration_value' => 15,
                    'created_at' => now()->format('Y-m-d H:i:s')
                ];

                session(['guest_booking' => $bookingData]);
            } else {
                return redirect()->route('booking.index')
                    ->with('error', 'No booking information found. Please start a new booking.');
            }
        }

        $bookingData = session('guest_booking');
        $vehicle = Vehicle::findOrFail($bookingData['vehicle_id']);

        return view('booking.guest-review', compact('bookingData', 'vehicle'));
    }

    /**
     * Save guest booking and redirect to client details
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveGuestBooking(Request $request)
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            return redirect()->route('booking.index')
                ->with('error', 'No booking information found. Please start a new booking.');
        }

        // Get booking data from session
        $bookingData = session('guest_booking');

        // Validate booking data
        if (!isset($bookingData['vehicle_id']) || !isset($bookingData['booking_type']) || !isset($bookingData['amount'])) {
            return redirect()->route('booking.index')
                ->with('error', 'Invalid booking information. Please start a new booking.');
        }

        // Flash success message
        session()->flash('success', 'Your booking has been saved. Please provide your contact details to continue.');

        // Redirect to client details page
        return redirect()->route('booking.client-details');
    }

    /**
     * Show client details form
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showClientDetails()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            // For testing purposes, create a sample booking data
            if (app()->environment('local') && request()->has('test')) {
                $vehicle = Vehicle::where('is_active', true)->first();
                if (!$vehicle) {
                    return redirect()->route('booking.index')
                        ->with('error', 'No active vehicles found. Please add vehicles first.');
                }

                $bookingData = [
                    'vehicle_id' => $vehicle->id,
                    'booking_type' => 'one_way',
                    'pickup_address' => '123 Main St, London, UK',
                    'pickup_lat' => 51.5074,
                    'pickup_lng' => -0.1278,
                    'dropoff_address' => '456 Oxford St, London, UK',
                    'dropoff_lat' => 51.5152,
                    'dropoff_lng' => -0.1418,
                    'pickup_date' => now()->addDay()->format('Y-m-d H:i:s'),
                    'pickup_datetime' => now()->addDay()->format('Y-m-d H:i:s'),
                    'amount' => 50.00,
                    'distance_value' => 5.2,
                    'duration_value' => 15,
                    'created_at' => now()->format('Y-m-d H:i:s')
                ];

                session(['guest_booking' => $bookingData]);
            } else {
                return redirect()->route('booking.index')
                    ->with('error', 'No booking information found. Please start a new booking.');
            }
        }

        // Get the vehicle for the booking summary
        $bookingData = session('guest_booking');
        $vehicle = Vehicle::findOrFail($bookingData['vehicle_id']);

        return view('booking.client_details', compact('vehicle'));
    }

    /**
     * Save client details and redirect to auth check
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveClientDetails(Request $request)
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            return redirect()->route('booking.index')
                ->with('error', 'No booking information found. Please start a new booking.');
        }

        // Validate client details
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'special_requests' => 'nullable|string|max:500',
            'terms' => 'required|accepted',
        ]);

        // Store client details in session
        session(['client_details' => [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'special_requests' => $validated['special_requests'] ?? null,
        ]]);

        // Check if user with this email already exists
        $userExists = User::where('email', $validated['email'])->exists();

        // Flash success message
        session()->flash('success', 'Your details have been saved. Please continue to complete your booking.');

        // If user exists, redirect to login page
        if ($userExists) {
            return redirect()->route('booking.guest.login')
                ->with('message', 'An account with this email already exists. Please sign in to continue.');
        }

        // Redirect to auth check
        return redirect()->route('booking.auth-check');
    }

    /**
     * Check if user is authenticated and redirect accordingly
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function authCheck()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            return redirect()->route('booking.index')
                ->with('error', 'No booking information found. Please start a new booking.');
        }

        // Check if client details are in session
        if (!session()->has('client_details')) {
            return redirect()->route('booking.client-details')
                ->with('error', 'Please provide your contact details first.');
        }

        // If user is already logged in, create the booking
        if (Auth::check()) {
            return $this->createBookingFromSession();
        }

        // If not logged in, show login/register options
        return redirect()->route('booking.guest.login');
    }

    /**
     * Show guest login page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showGuestLogin()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            // For testing purposes, create a sample booking data
            if (app()->environment('local') && request()->has('test')) {
                $vehicle = Vehicle::where('is_active', true)->first();
                if (!$vehicle) {
                    return redirect()->route('booking.index')
                        ->with('error', 'No active vehicles found. Please add vehicles first.');
                }

                $bookingData = [
                    'vehicle_id' => $vehicle->id,
                    'booking_type' => 'one_way',
                    'pickup_address' => '123 Main St, London, UK',
                    'pickup_lat' => 51.5074,
                    'pickup_lng' => -0.1278,
                    'dropoff_address' => '456 Oxford St, London, UK',
                    'dropoff_lat' => 51.5152,
                    'dropoff_lng' => -0.1418,
                    'pickup_date' => now()->addDay()->format('Y-m-d H:i:s'),
                    'pickup_datetime' => now()->addDay()->format('Y-m-d H:i:s'),
                    'amount' => 50.00,
                    'created_at' => now()->format('Y-m-d H:i:s')
                ];

                session(['guest_booking' => $bookingData]);

                // Also add client details for testing
                if (!session()->has('client_details')) {
                    session(['client_details' => [
                        'name' => 'Test User',
                        'email' => '<EMAIL>',
                        'phone' => '1234567890',
                        'special_requests' => 'Test request'
                    ]]);
                }
            } else {
                return redirect()->route('booking.index')
                    ->with('error', 'No booking information found. Please start a new booking.');
            }
        }

        return view('booking.guest-login');
    }

    /**
     * Process guest login
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processGuestLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            // Authentication passed
            return $this->createBookingFromSession();
        }

        // Authentication failed
        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->withInput($request->except('password'));
    }

    /**
     * Show guest register page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function showGuestRegister()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            // For testing purposes, create a sample booking data
            if (app()->environment('local') && request()->has('test')) {
                $vehicle = Vehicle::where('is_active', true)->first();
                if (!$vehicle) {
                    return redirect()->route('booking.index')
                        ->with('error', 'No active vehicles found. Please add vehicles first.');
                }

                $bookingData = [
                    'vehicle_id' => $vehicle->id,
                    'booking_type' => 'one_way',
                    'pickup_address' => '123 Main St, London, UK',
                    'pickup_lat' => 51.5074,
                    'pickup_lng' => -0.1278,
                    'dropoff_address' => '456 Oxford St, London, UK',
                    'dropoff_lat' => 51.5152,
                    'dropoff_lng' => -0.1418,
                    'pickup_date' => now()->addDay()->format('Y-m-d H:i:s'),
                    'pickup_datetime' => now()->addDay()->format('Y-m-d H:i:s'),
                    'amount' => 50.00,
                    'created_at' => now()->format('Y-m-d H:i:s')
                ];

                session(['guest_booking' => $bookingData]);

                // Also add client details for testing
                if (!session()->has('client_details')) {
                    session(['client_details' => [
                        'name' => 'Test User',
                        'email' => '<EMAIL>',
                        'phone' => '1234567890',
                        'special_requests' => 'Test request'
                    ]]);
                }
            } else {
                return redirect()->route('booking.index')
                    ->with('error', 'No booking information found. Please start a new booking.');
            }
        }

        return view('booking.guest-register');
    }

    /**
     * Process guest registration
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processGuestRegister(Request $request)
    {
        // Get client details from session
        $clientDetails = session('client_details', []);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
        ]);

        // Create new user with client role
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'role' => 'client', // Set role directly
        ]);

        // Send welcome email
        try {
            EmailService::sendClientWelcome($user, true);
        } catch (\Exception $e) {
            \Log::error('Failed to send welcome email: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);
        }

        // Log the user in
        Auth::login($user);

        // Create booking from session
        return $this->createBookingFromSession();
    }

    /**
     * Create booking from session data
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    private function createBookingFromSession()
    {
        // Check if there's a guest booking in session
        if (!session()->has('guest_booking')) {
            return redirect()->route('booking.index')
                ->with('error', 'No booking information found. Please start a new booking.');
        }

        // Check if client details are in session
        if (!session()->has('client_details')) {
            return redirect()->route('booking.client-details')
                ->with('error', 'Please provide your contact details first.');
        }

        $bookingData = session('guest_booking');
        $clientDetails = session('client_details');

        // Create booking
        $booking = new Booking();
        $booking->user_id = Auth::id();
        $booking->vehicle_id = $bookingData['vehicle_id'];
        $booking->booking_number = Booking::generateBookingNumber();
        $booking->booking_type = $bookingData['booking_type'];
        $booking->pickup_address = $bookingData['pickup_address'];
        $booking->pickup_lat = $bookingData['pickup_lat'];
        $booking->pickup_lng = $bookingData['pickup_lng'];

        if ($bookingData['booking_type'] !== 'hourly') {
            $booking->dropoff_address = $bookingData['dropoff_address'];
            $booking->dropoff_lat = $bookingData['dropoff_lat'];
            $booking->dropoff_lng = $bookingData['dropoff_lng'];
        }

        // Handle both pickup_datetime and pickup_date for backward compatibility
        $pickupDateTime = $bookingData['pickup_datetime'] ?? $bookingData['pickup_date'] ?? null;
        if (!$pickupDateTime) {
            return redirect()->route('booking.index')
                ->with('error', 'Invalid booking data. Please start a new booking.');
        }

        try {
            $booking->pickup_date = \Carbon\Carbon::parse($pickupDateTime);

            // Handle both return_datetime and return_date for backward compatibility
            $returnDateTime = $bookingData['return_datetime'] ?? $bookingData['return_date'] ?? null;
            $booking->return_date = $returnDateTime ? \Carbon\Carbon::parse($returnDateTime) : null;
        } catch (\Exception $e) {
            return redirect()->route('booking.index')
                ->with('error', 'Invalid date format. Please start a new booking.');
        }
        $booking->duration_hours = $bookingData['duration_hours'] ?? null;
        $booking->amount = $bookingData['amount'];
        $booking->status = 'pending';
        $booking->payment_status = 'pending';
        $booking->notes = $clientDetails['special_requests'] ?? $bookingData['notes'] ?? null;

        // Handle via stops
        if (isset($bookingData['via_stops']) && !empty($bookingData['via_stops']) && is_array($bookingData['via_stops'])) {
            $booking->via_stops = $bookingData['via_stops'];
            $booking->via_count = count($bookingData['via_stops']);

            // Calculate via charges if not already set
            if (!isset($bookingData['via_charges']) || $bookingData['via_charges'] == 0) {
                $vehicle = \App\Models\Vehicle::find($bookingData['vehicle_id']);
                if ($vehicle) {
                    $viaChargePerStop = $vehicle->via_charge ?? 5.00;
                    $booking->via_charges = $booking->via_count * $viaChargePerStop;
                }
            } else {
                $booking->via_charges = $bookingData['via_charges'];
            }
        }

        // Handle airport transfer fields from session
        if ($bookingData['booking_type'] === 'airport_transfer') {
            $booking->airport_direction = $bookingData['airport_direction'] ?? null;
            $booking->airport_surcharge = $bookingData['airport_surcharge'] ?? 0;
            $booking->pickup_airport_id = $bookingData['pickup_airport_id'] ?? null;
            $booking->dropoff_airport_id = $bookingData['dropoff_airport_id'] ?? null;

            // Handle flight details from session
            $booking->flight_number = $bookingData['flight_number'] ?? null;
            $booking->airline = $bookingData['airline'] ?? null;
            $booking->terminal = $bookingData['terminal'] ?? null;
            $booking->flight_status = $bookingData['flight_status'] ?? null;
            $booking->flight_notes = $bookingData['flight_notes'] ?? null;

            // Handle flight times from session
            if (!empty($bookingData['departure_time'])) {
                try {
                    $booking->departure_time = \Carbon\Carbon::parse($bookingData['departure_time']);
                } catch (\Exception $e) {
                    \Log::warning('Invalid departure time format in session: ' . $bookingData['departure_time']);
                }
            }

            if (!empty($bookingData['arrival_time'])) {
                try {
                    $booking->arrival_time = \Carbon\Carbon::parse($bookingData['arrival_time']);
                } catch (\Exception $e) {
                    \Log::warning('Invalid arrival time format in session: ' . $bookingData['arrival_time']);
                }
            }
        }

        // Handle distance and duration values
        $booking->distance_value = $bookingData['distance_value'] ?? null;
        $booking->duration_value = $bookingData['duration_value'] ?? null;

        // Handle fare details
        if (isset($bookingData['fare_details'])) {
            $booking->fare_details = $bookingData['fare_details'];
        }

        // Handle extra services from session
        $booking->meet_and_greet = isset($bookingData['meet_and_greet']) ? (bool)$bookingData['meet_and_greet'] : false;
        $booking->child_seat = isset($bookingData['child_seat']) ? (bool)$bookingData['child_seat'] : false;
        $booking->wheelchair_accessible = isset($bookingData['wheelchair_accessible']) ? (bool)$bookingData['wheelchair_accessible'] : false;
        $booking->extra_luggage = isset($bookingData['extra_luggage']) ? (bool)$bookingData['extra_luggage'] : false;

        $booking->save();

        // Add booking history
        $booking->addHistory('booking_created', [
            'booking_type' => $booking->booking_type,
            'amount' => $booking->amount,
            'vehicle_id' => $booking->vehicle_id,
        ]);

        // Notify the client about the booking
        Auth::user()->notify(new BookingStatusChanged($booking));

        // Clear session
        session()->forget(['guest_booking', 'client_details']);

        // Redirect to payment page
        return redirect()->route('booking.payment', $booking->id)
            ->with('success', 'Your booking has been created successfully.');
    }

    /**
     * Calculate total cost for extra services
     *
     * @param array $extraServices
     * @return float
     */
    private function calculateExtraServicesTotal($extraServices)
    {
        $total = 0;
        $extraServicesSettings = SettingsService::getExtraServicesSettings();

        foreach ($extraServices as $service => $enabled) {
            if ($enabled && isset($extraServicesSettings[$service]) && $extraServicesSettings[$service]['enabled']) {
                $total += $extraServicesSettings[$service]['fee'];
            }
        }

        return $total;
    }

    /**
     * Get detailed breakdown of extra services with pricing
     *
     * @param array $extraServices
     * @return array
     */
    private function getExtraServicesDetails($extraServices)
    {
        $details = [];
        $extraServicesSettings = SettingsService::getExtraServicesSettings();

        foreach ($extraServices as $service => $enabled) {
            if ($enabled && isset($extraServicesSettings[$service]) && $extraServicesSettings[$service]['enabled']) {
                $details[$service] = $extraServicesSettings[$service]['fee'];
            }
        }

        return $details;
    }
}
