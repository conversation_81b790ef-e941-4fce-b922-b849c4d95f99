<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Document;

class DocumentController extends Controller
{
    /**
     * Display a listing of the driver's documents.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();
        $documents = $user->driverDocuments()->orderBy('created_at', 'desc')->get();

        return view('driver.documents.index', compact('documents'));
    }

    /**
     * Show the form for creating a new document.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function create()
    {
        return view('driver.documents.create');
    }

    /**
     * Store a newly created document in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'document_type' => 'required|string|max:50',
            'document_file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'expiry_date' => 'nullable|date',
            'document_number' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();

        // Store document
        $document = $request->file('document_file');
        $documentName = \App\Services\StorageService::generateUniqueFilename($document);
        $path = \App\Services\StorageService::uploadFile($document, 'driver-documents', 'public', $documentName);

        if ($path) {
            // Create document record
            $user->driverDocuments()->create([
                'type' => $request->document_type,
                'file_path' => $path,
                'expiry_date' => $request->expiry_date,
                'document_number' => $request->document_number,
                'notes' => $request->notes,
                'status' => 'pending',
            ]);
        } else {
            return redirect()->back()
                ->withErrors(['document_file' => 'Failed to upload document. Please try again.']);
        }

        return redirect()->route('driver.documents.index')
            ->with('success', 'Document uploaded successfully and is pending verification.');
    }

    /**
     * Display the specified document.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $user = Auth::user();
        $document = $user->driverDocuments()->findOrFail($id);

        return view('driver.documents.show', compact('document'));
    }

    /**
     * Show the form for editing the specified document.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit($id)
    {
        $user = Auth::user();
        $document = $user->driverDocuments()->findOrFail($id);

        return view('driver.documents.edit', compact('document'));
    }

    /**
     * Update the specified document in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'document_type' => 'required|string|max:50',
            'document_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'expiry_date' => 'nullable|date',
            'document_number' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $document = $user->driverDocuments()->findOrFail($id);

        // Update document file if provided
        if ($request->hasFile('document_file')) {
            // Delete old file
            if (Storage::exists('public/' . $document->file_path)) {
                Storage::delete('public/' . $document->file_path);
            }

            // Store new file
            $document = $request->file('document_file');
            $documentName = time() . '.' . $document->getClientOriginalExtension();
            $path = $document->storeAs('driver-documents', $documentName, 'public');
            $document->file_path = $path;
            $document->status = 'pending'; // Reset status when document is updated
        }

        // Update document details
        $document->type = $request->document_type;
        $document->expiry_date = $request->expiry_date;
        $document->document_number = $request->document_number;
        $document->notes = $request->notes;
        $document->save();

        return redirect()->route('driver.documents.index')
            ->with('success', 'Document updated successfully.');
    }

    /**
     * Remove the specified document from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $document = $user->driverDocuments()->findOrFail($id);

        // Delete file
        if (Storage::exists('public/' . $document->file_path)) {
            Storage::delete('public/' . $document->file_path);
        }

        // Delete record
        $document->delete();

        return redirect()->route('driver.documents.index')
            ->with('success', 'Document deleted successfully.');
    }
}
