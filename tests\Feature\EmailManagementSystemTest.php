<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Booking;
use App\Models\EmailTemplate;
use App\Models\EmailCampaign;
use App\Models\EmailLog;
use App\Models\Setting;
use App\Services\EmailService;
use App\Services\SettingsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use Carbon\Carbon;

class EmailManagementSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $client;
    protected $driver;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->client = User::factory()->create(['role' => 'client']);
        $this->driver = User::factory()->create(['role' => 'driver']);

        // Initialize email settings
        $this->initializeEmailSettings();

        // Mock mail to prevent actual sending during tests
        Mail::fake();
    }

    private function initializeEmailSettings()
    {
        $settings = [
            'mail_driver' => 'smtp',
            'mail_host' => 'smtp.gmail.com',
            'mail_port' => '587',
            'mail_username' => '<EMAIL>',
            'mail_password' => 'test-password',
            'mail_encryption' => 'tls',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'YNR Cars',
            'company_name' => 'YNR Cars',
            'company_email' => '<EMAIL>',
        ];

        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }
    }

    /** @test */
    public function admin_can_access_email_management_dashboard()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.email.index'));

        $response->assertStatus(200);
        $response->assertSee('Email Management');
        $response->assertSee('Email Settings');
        $response->assertSee('Email Statistics');
    }

    /** @test */
    public function admin_can_update_email_settings()
    {
        $emailSettings = [
            'mail_driver' => 'smtp',
            'mail_host' => 'smtp.mailgun.org',
            'mail_port' => '587',
            'mail_username' => '<EMAIL>',
            'mail_encryption' => 'tls',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'YNR Cars Updated',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.email.update-settings'), $emailSettings);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were updated
        $this->assertEquals('smtp.mailgun.org', SettingsService::get('mail_host'));
        $this->assertEquals('YNR Cars Updated', SettingsService::get('mail_from_name'));
    }

    /** @test */
    public function admin_can_test_email_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.email.test'), [
                'test_email' => '<EMAIL>'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify test email was queued
        Mail::assertQueued(\App\Mail\TestMail::class);
    }

    /** @test */
    public function admin_can_send_custom_email()
    {
        $emailData = [
            'to_email' => '<EMAIL>',
            'subject' => 'Test Custom Email',
            'message' => 'This is a test custom email message.',
            'send_copy' => true,
        ];

        $response = $this->actingAs($this->admin)
            ->postJson(route('admin.email.send-custom'), $emailData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify custom email was queued
        Mail::assertQueued(\App\Mail\CustomMail::class);
    }

    /** @test */
    public function email_templates_can_be_created_and_managed()
    {
        // Test creating a template
        $templateData = [
            'name' => 'Test Template',
            'type' => 'notification',
            'category' => 'client',
            'subject' => 'Test Subject - {client_name}',
            'content' => 'Hello {client_name}, this is a test template.',
            'description' => 'A test email template',
            'variables' => ['client_name'],
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.email.templates.store'), $templateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify template was created
        $template = EmailTemplate::where('name', 'Test Template')->first();
        $this->assertNotNull($template);
        $this->assertEquals('test-template', $template->slug);
        $this->assertEquals('notification', $template->type);
    }

    /** @test */
    public function email_templates_can_be_previewed()
    {
        $template = EmailTemplate::factory()->create([
            'name' => 'Preview Test',
            'subject' => 'Hello {client_name}',
            'content' => 'Welcome {client_name} to our service!',
            'variables' => ['client_name'],
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.email.templates.preview', $template));

        $response->assertStatus(200);
        $response->assertSee('Hello John Doe'); // Should show sample data
        $response->assertSee('Welcome John Doe to our service!');
    }

    /** @test */
    public function booking_confirmation_emails_are_sent()
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
            'status' => 'confirmed',
        ]);

        $result = EmailService::sendBookingConfirmation($booking);

        $this->assertTrue($result);
        Mail::assertQueued(\App\Mail\BookingConfirmationMail::class);
    }

    /** @test */
    public function payment_receipt_emails_are_sent()
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
        ]);

        // Create a payment for the booking
        $payment = \App\Models\Payment::factory()->create([
            'booking_id' => $booking->id,
            'user_id' => $this->client->id,
            'amount' => 100.00,
        ]);

        $result = EmailService::sendPaymentReceipt($payment);

        $this->assertTrue($result);
        Mail::assertQueued(\App\Mail\PaymentReceiptMail::class);
    }

    /** @test */
    public function welcome_emails_are_sent_to_new_clients()
    {
        $result = EmailService::sendClientWelcome($this->client, true);

        $this->assertTrue($result);
        Mail::assertQueued(\App\Mail\ClientWelcomeMail::class);
    }

    /** @test */
    public function welcome_emails_are_sent_to_new_drivers()
    {
        $result = EmailService::sendDriverWelcome($this->driver, 'temp-password');

        $this->assertTrue($result);
        Mail::assertQueued(\App\Mail\DriverWelcomeMail::class);
    }

    /** @test */
    public function email_campaigns_can_be_created()
    {
        $template = EmailTemplate::factory()->create();

        $campaignData = [
            'name' => 'Test Campaign',
            'subject' => 'Special Offer for Our Clients',
            'content' => 'Dear {client_name}, we have a special offer for you!',
            'template_id' => $template->id,
            'recipient_type' => 'client',
            'recipient_criteria' => [],
            'scheduled_at' => Carbon::now()->addHour(),
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.email.campaigns.store'), $campaignData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify campaign was created
        $campaign = EmailCampaign::where('name', 'Test Campaign')->first();
        $this->assertNotNull($campaign);
        $this->assertContains($campaign->status, ['draft', 'scheduled']); // Can be either depending on scheduled_at
    }

    /** @test */
    public function email_logs_are_created_for_sent_emails()
    {
        $booking = Booking::factory()->create([
            'user_id' => $this->client->id,
        ]);

        EmailService::sendBookingConfirmation($booking);

        // Check that email log was created
        $emailLog = EmailLog::where('type', 'booking_confirmation')->first();
        $this->assertNotNull($emailLog);
        $this->assertEquals('sent', $emailLog->direction);
        $this->assertEquals($this->client->email, $emailLog->to_email);
    }

    /** @test */
    public function admin_can_view_sent_emails()
    {
        // Create some email logs
        EmailLog::factory()->count(5)->sent()->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.email.sent'));

        $response->assertStatus(200);
        // Just check that the page loads, content may vary
    }

    /** @test */
    public function email_queue_status_can_be_retrieved()
    {
        $queueStatus = EmailService::getEmailQueueStatus();

        $this->assertIsArray($queueStatus);
        $this->assertArrayHasKey('connection', $queueStatus);
        $this->assertArrayHasKey('queue_name', $queueStatus);
    }

    /** @test */
    public function contact_form_emails_are_processed()
    {
        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Contact',
            'message' => 'This is a test contact message.',
        ];

        $result = EmailService::sendContactFormEmails($contactData);

        $this->assertTrue($result);
        Mail::assertQueued(\App\Mail\ContactFormMail::class);
        Mail::assertQueued(\App\Mail\ContactConfirmationMail::class);
    }

    /** @test */
    public function email_settings_validation_works()
    {
        $invalidSettings = [
            'mail_driver' => '', // Required
            'mail_host' => '', // Required
            'mail_port' => 'invalid', // Should be numeric
            'mail_from_address' => 'invalid-email', // Should be valid email
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.email.update-settings'), $invalidSettings);

        // Check if validation errors exist (some fields might not be validated)
        $response->assertSessionHasErrors();
    }

    /** @test */
    public function non_admin_users_cannot_access_email_management()
    {
        $response = $this->actingAs($this->client)
            ->get(route('admin.email.index'));

        $response->assertStatus(403);
    }
}
