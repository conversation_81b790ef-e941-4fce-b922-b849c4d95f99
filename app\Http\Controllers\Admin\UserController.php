<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::query();
        $query->where('role', 'client'); // Always restrict to clients only

        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'nullable|in:admin,client,driver',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            // Driver specific fields
            'license_number' => 'nullable|required_if:role,driver|string|max:50',
            'vehicle_info' => 'nullable|required_if:role,driver|string|max:255',
            'is_available' => 'nullable|boolean',
        ]);

        $user = new User();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->role = $request->role ?? 'client'; // Default to client if not specified
        $user->phone = $request->phone;
        $user->address = $request->address;
        $user->is_active = $request->boolean('is_active');

        // Driver specific fields
        if ($request->role === 'driver') {
            $user->license_number = $request->license_number;
            $user->vehicle_info = $request->vehicle_info;
            $user->is_available = $request->boolean('is_available');
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            $photo = $request->file('profile_photo');
            $filename = \App\Services\StorageService::generateUniqueFilename($photo);
            $path = \App\Services\StorageService::uploadFile($photo, 'profile-photos', 'public', $filename);

            if ($path) {
                $user->profile_photo = $path;
            } else {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['profile_photo' => 'Failed to upload profile photo. Please try again.']);
            }
        }

        $user->save();

        $roleText = $user->role === 'client' ? 'Client' : 'User';
        return redirect()->route('admin.users.index')
            ->with('success', $roleText . ' created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = User::findOrFail($id);

        // Get additional data based on user role
        $data = [];

        if ($user->role === 'client') {
            $data['bookings'] = $user->bookings()->latest()->take(5)->get();
        } elseif ($user->role === 'driver') {
            $data['rides'] = $user->driverRides()->latest()->take(5)->get();
            $data['earnings'] = $user->driverEarnings()->sum('amount');
        }

        return view('admin.users.show', compact('user', 'data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::findOrFail($id);
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
            'role' => 'required|in:admin,client,driver',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            // Driver specific fields
            'license_number' => 'nullable|required_if:role,driver|string|max:50',
            'vehicle_info' => 'nullable|required_if:role,driver|string|max:255',
            'is_available' => 'nullable|boolean',
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->role = $request->role;
        $user->phone = $request->phone;
        $user->address = $request->address;
        $user->is_active = $request->boolean('is_active');

        // Driver specific fields
        if ($request->role === 'driver') {
            $user->license_number = $request->license_number;
            $user->vehicle_info = $request->vehicle_info;
            $user->is_available = $request->boolean('is_available');
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->profile_photo) {
                \App\Services\StorageService::deleteFile($user->profile_photo, 'public');
            }

            $photo = $request->file('profile_photo');
            $filename = \App\Services\StorageService::generateUniqueFilename($photo);
            $path = \App\Services\StorageService::uploadFile($photo, 'profile-photos', 'public', $filename);

            if ($path) {
                $user->profile_photo = $path;
            } else {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['profile_photo' => 'Failed to upload profile photo. Please try again.']);
            }
        }

        // Update password if provided
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8|confirmed',
            ]);

            $user->password = Hash::make($request->password);
        }

        $user->save();

        $roleText = $user->role === 'client' ? 'Client' : 'User';
        return redirect()->route('admin.users.index')
            ->with('success', $roleText . ' updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = User::findOrFail($id);

        // Check if user has related data
        $hasRelatedData = false;

        if ($user->role === 'client' && $user->bookings()->exists()) {
            $hasRelatedData = true;
        } elseif ($user->role === 'driver' && $user->driverRides()->exists()) {
            $hasRelatedData = true;
        }

        if ($hasRelatedData) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete user with related data. Consider deactivating the account instead.');
        }

        // Delete profile photo if exists
        if ($user->profile_photo) {
            Storage::disk('public')->delete($user->profile_photo);
        }

        $user->delete();

        $roleText = $user->role === 'client' ? 'Client' : 'User';
        return redirect()->route('admin.users.index')
            ->with('success', $roleText . ' deleted successfully.');
    }

    /**
     * Toggle user active status.
     */
    public function toggleActive(string $id)
    {
        $user = User::findOrFail($id);
        $user->is_active = !$user->is_active;
        $user->save();

        $status = $user->is_active ? 'activated' : 'deactivated';
        $roleText = $user->role === 'client' ? 'Client' : 'User';

        return redirect()->route('admin.users.index')
            ->with('success', "{$roleText} {$status} successfully.");
    }

    /**
     * Search for clients via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchClients(Request $request)
    {
        $query = $request->input('q');
        $page = $request->input('page', 1);
        $perPage = 10;

        $clientsQuery = User::where('role', 'client')
            ->where('is_active', true)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone', 'like', "%{$query}%");
            });

        $total = $clientsQuery->count();
        $clients = $clientsQuery->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get(['id', 'name', 'email', 'phone', 'address']);

        return response()->json([
            'clients' => $clients,
            'pagination' => [
                'more' => ($page * $perPage) < $total
            ]
        ]);
    }

    /**
     * Get client details via AJAX.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientDetails($id)
    {
        $client = User::where('role', 'client')
            ->with(['bookings' => function($query) {
                $query->latest()->take(5);
            }])
            ->findOrFail($id);

        // Count total bookings
        $bookingsCount = $client->bookings()->count();

        // Add bookings count to client data
        $clientData = $client->toArray();
        $clientData['bookings_count'] = $bookingsCount;

        return response()->json([
            'success' => true,
            'client' => $clientData
        ]);
    }

    /**
     * Quick create client via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function quickCreateClient(Request $request)
    {
        // Validate the request
        $validator = \Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:255',
        ]);

        // Return validation errors if any
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create new client
            $client = new User();
            $client->name = $request->name;
            $client->email = $request->email;
            $client->phone = $request->phone;
            $client->address = $request->address;
            $client->role = 'client';
            $client->password = bcrypt(\Str::random(12)); // Generate random password
            $client->save();

            return response()->json([
                'success' => true,
                'message' => 'Client created successfully',
                'client' => $client
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating client: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if email is available for use.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'user_id' => 'nullable|integer' // For edit forms to exclude current user
        ]);

        $email = $request->input('email');
        $userId = $request->input('user_id');

        // Check if email exists, excluding current user if editing
        $query = User::where('email', $email);

        if ($userId) {
            $query->where('id', '!=', $userId);
        }

        $exists = $query->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'This email is already taken' : 'Email is available'
        ]);
    }

    /**
     * Display a listing of clients.
     */
    public function clients()
    {
        $clients = User::where('role', 'client')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.users.clients', compact('clients'));
    }
}
