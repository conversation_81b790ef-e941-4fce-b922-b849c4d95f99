<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Booking;
use App\Services\SettingsService;

class ClientFeedbackRequestMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $booking;

    /**
     * Create a new message instance.
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '⭐ How was your ride? Share your feedback - Booking #' . $this->booking->booking_number,
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.client-feedback-request',
            with: [
                'booking' => $this->booking,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
