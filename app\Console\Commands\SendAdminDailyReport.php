<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\User;
use App\Services\EmailService;
use Carbon\Carbon;

class SendAdminDailyReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:daily-report {--date= : Specific date for the report (Y-m-d format)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send daily business report to administrators';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $reportDate = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::yesterday();
        $this->info("Generating daily business report for {$reportDate->format('Y-m-d')}...");

        $reportData = $this->generateReportData($reportDate);

        try {
            EmailService::sendAdminDailyReport($reportData, $reportDate->format('Y-m-d'));
            $this->info("✅ Daily business report sent successfully!");

            $this->table(['Metric', 'Value'], [
                ['Report Date', $reportDate->format('F j, Y')],
                ['Total Revenue', '£' . number_format($reportData['today_revenue'], 2)],
                ['Total Bookings', $reportData['today_bookings']],
                ['Completed Bookings', $reportData['completed_bookings']],
                ['New Clients', $reportData['new_clients']],
                ['Active Drivers', $reportData['active_drivers']],
            ]);

        } catch (\Exception $e) {
            $this->error("❌ Failed to send daily report: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Generate report data for the specified date
     */
    private function generateReportData(Carbon $date): array
    {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay = $date->copy()->endOfDay();
        $yesterday = $date->copy()->subDay();

        // Today's bookings
        $todayBookings = Booking::whereBetween('created_at', [$startOfDay, $endOfDay])->get();
        $yesterdayBookings = Booking::whereBetween('created_at', [$yesterday->startOfDay(), $yesterday->endOfDay()])->get();

        // Revenue calculations
        $todayRevenue = $todayBookings->where('status', 'completed')->sum('amount');
        $yesterdayRevenue = $yesterdayBookings->where('status', 'completed')->sum('amount');
        $revenueChange = $yesterdayRevenue > 0 ? (($todayRevenue - $yesterdayRevenue) / $yesterdayRevenue) * 100 : 0;

        // Month-to-date revenue
        $mtdRevenue = Booking::where('status', 'completed')
            ->whereBetween('created_at', [$date->copy()->startOfMonth(), $endOfDay])
            ->sum('amount');

        // Booking statistics
        $completedBookings = $todayBookings->where('status', 'completed')->count();
        $pendingBookings = $todayBookings->where('status', 'pending')->count();
        $cancelledBookings = $todayBookings->where('status', 'cancelled')->count();
        $totalBookings = $todayBookings->count();
        $completionRate = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;

        // Client and driver stats
        $newClients = User::where('role', 'client')
            ->whereBetween('created_at', [$startOfDay, $endOfDay])
            ->count();

        $activeDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->count();

        $availableDrivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->count(); // Simplified - all active drivers considered available

        $driverUtilization = $activeDrivers > 0 ? ($availableDrivers / $activeDrivers) * 100 : 0;

        // Average rating
        $avgRating = $todayBookings->where('status', 'completed')->avg('client_rating') ?? 0;

        // Average booking value
        $avgBookingValue = $completedBookings > 0 ? $todayRevenue / $completedBookings : 0;

        // Top performers
        $topPerformers = User::where('role', 'driver')
            ->whereHas('bookings', function($query) use ($startOfDay, $endOfDay) {
                $query->where('status', 'completed')
                      ->whereBetween('pickup_date', [$startOfDay, $endOfDay]);
            })
            ->withCount(['bookings as rides_count' => function($query) use ($startOfDay, $endOfDay) {
                $query->where('status', 'completed')
                      ->whereBetween('pickup_date', [$startOfDay, $endOfDay]);
            }])
            ->with(['bookings' => function($query) use ($startOfDay, $endOfDay) {
                $query->where('status', 'completed')
                      ->whereBetween('pickup_date', [$startOfDay, $endOfDay]);
            }])
            ->get()
            ->map(function($driver) {
                return [
                    'name' => $driver->name,
                    'rides' => $driver->rides_count,
                    'revenue' => $driver->bookings->sum('amount'),
                ];
            })
            ->sortByDesc('revenue')
            ->take(5)
            ->values()
            ->toArray();

        // Generate alerts
        $alerts = [];
        if ($pendingBookings > 5) {
            $alerts[] = "High number of pending bookings ({$pendingBookings}) - review driver assignments";
        }
        if ($completionRate < 80) {
            $alerts[] = "Low completion rate ({$completionRate}%) - investigate cancellation reasons";
        }
        if ($availableDrivers < 3) {
            $alerts[] = "Low driver availability ({$availableDrivers} available) - contact more drivers";
        }
        if ($revenueChange < -20) {
            $alerts[] = "Significant revenue drop ({$revenueChange}%) compared to yesterday";
        }

        return [
            'today_revenue' => $todayRevenue,
            'yesterday_revenue' => $yesterdayRevenue,
            'revenue_change' => $revenueChange,
            'mtd_revenue' => $mtdRevenue,
            'avg_booking_value' => $avgBookingValue,
            'today_bookings' => $totalBookings,
            'completed_bookings' => $completedBookings,
            'pending_bookings' => $pendingBookings,
            'cancelled_bookings' => $cancelledBookings,
            'completion_rate' => $completionRate,
            'new_clients' => $newClients,
            'active_drivers' => $activeDrivers,
            'available_drivers' => $availableDrivers,
            'driver_utilization' => $driverUtilization,
            'avg_rating' => $avgRating,
            'top_performers' => $topPerformers,
            'alerts' => $alerts,
        ];
    }
}
