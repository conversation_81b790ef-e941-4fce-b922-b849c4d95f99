<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Booking;
use App\Services\EmailService;
use Carbon\Carbon;

class SendClientLoyaltyRewards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:loyalty-rewards {--type=milestone : Type of reward to check (milestone, birthday, referral)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send loyalty rewards and milestone notifications to clients';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $rewardType = $this->option('type');
        $this->info("Processing {$rewardType} rewards for clients...");

        $rewardsSent = 0;

        switch ($rewardType) {
            case 'milestone':
                $rewardsSent = $this->processMilestoneRewards();
                break;
            case 'birthday':
                $rewardsSent = $this->processBirthdayRewards();
                break;
            case 'referral':
                $rewardsSent = $this->processReferralRewards();
                break;
            default:
                $this->error("Invalid reward type: {$rewardType}");
                return Command::FAILURE;
        }

        $this->info("🎁 {$rewardType} rewards sent to {$rewardsSent} clients");
        return Command::SUCCESS;
    }

    /**
     * Process milestone rewards for clients
     */
    private function processMilestoneRewards(): int
    {
        $rewardsSent = 0;
        $milestones = [5, 10, 25, 50, 100, 250, 500]; // Ride count milestones

        $clients = User::where('role', 'client')
            ->where('is_active', true)
            ->get();

        foreach ($clients as $client) {
            $totalRides = $client->bookings()->where('status', 'completed')->count();

            // Check if client has reached a milestone
            if (in_array($totalRides, $milestones)) {
                // Check if we've already sent this milestone reward
                $lastRewardSent = $client->last_milestone_reward ?? 0;

                if ($totalRides > $lastRewardSent) {
                    $rewardData = $this->generateMilestoneReward($client, $totalRides);

                    try {
                        EmailService::sendClientLoyaltyReward($client, 'milestone', $rewardData);

                        // Update last milestone reward sent
                        $client->update(['last_milestone_reward' => $totalRides]);

                        $rewardsSent++;
                        $this->line("✅ Milestone reward sent to {$client->name} - {$totalRides} rides");
                    } catch (\Exception $e) {
                        $this->error("❌ Failed to send milestone reward to {$client->name}: " . $e->getMessage());
                    }
                }
            }
        }

        return $rewardsSent;
    }

    /**
     * Process birthday rewards for clients
     */
    private function processBirthdayRewards(): int
    {
        $rewardsSent = 0;
        $today = Carbon::today();

        $clients = User::where('role', 'client')
            ->where('is_active', true)
            ->whereNotNull('date_of_birth')
            ->get();

        foreach ($clients as $client) {
            $birthday = Carbon::parse($client->date_of_birth);

            // Check if today is their birthday
            if ($birthday->format('m-d') === $today->format('m-d')) {
                // Check if we've already sent birthday reward this year
                $lastBirthdayReward = $client->last_birthday_reward;

                if (!$lastBirthdayReward || Carbon::parse($lastBirthdayReward)->year < $today->year) {
                    $rewardData = $this->generateBirthdayReward($client);

                    try {
                        EmailService::sendClientLoyaltyReward($client, 'birthday', $rewardData);

                        // Update last birthday reward sent
                        $client->update(['last_birthday_reward' => $today]);

                        $rewardsSent++;
                        $this->line("✅ Birthday reward sent to {$client->name}");
                    } catch (\Exception $e) {
                        $this->error("❌ Failed to send birthday reward to {$client->name}: " . $e->getMessage());
                    }
                }
            }
        }

        return $rewardsSent;
    }

    /**
     * Process referral rewards for clients
     */
    private function processReferralRewards(): int
    {
        $rewardsSent = 0;

        // Find clients who have successful referrals in the last 24 hours
        $yesterday = Carbon::yesterday();

        $newClients = User::where('role', 'client')
            ->where('is_active', true)
            ->whereNotNull('referred_by')
            ->whereBetween('created_at', [$yesterday->startOfDay(), $yesterday->endOfDay()])
            ->get();

        foreach ($newClients as $newClient) {
            $referrer = User::find($newClient->referred_by);

            if ($referrer && $referrer->role === 'client') {
                $rewardData = $this->generateReferralReward($referrer, $newClient);

                try {
                    EmailService::sendClientLoyaltyReward($referrer, 'referral_bonus', $rewardData);

                    $rewardsSent++;
                    $this->line("✅ Referral reward sent to {$referrer->name} for referring {$newClient->name}");
                } catch (\Exception $e) {
                    $this->error("❌ Failed to send referral reward to {$referrer->name}: " . $e->getMessage());
                }
            }
        }

        return $rewardsSent;
    }

    /**
     * Generate milestone reward data
     */
    private function generateMilestoneReward($client, $totalRides): array
    {
        $totalSpent = $client->bookings()->where('status', 'completed')->sum('amount');

        // Calculate reward based on milestone
        $discountPercentage = match($totalRides) {
            5 => 10,
            10 => 15,
            25 => 20,
            50 => 25,
            100 => 30,
            250 => 35,
            500 => 40,
            default => 10
        };

        $loyaltyLevel = match($totalRides) {
            5 => 'Bronze Member',
            10 => 'Silver Member',
            25 => 'Gold Member',
            50 => 'Platinum Member',
            100 => 'Diamond Member',
            250 => 'Elite Member',
            500 => 'VIP Member',
            default => 'Valued Member'
        };

        return [
            'milestone_description' => "You've completed {$totalRides} rides with us!",
            'reward_title' => "{$totalRides} Rides Milestone Achievement",
            'discount_percentage' => $discountPercentage,
            'promo_code' => 'MILESTONE' . $totalRides,
            'expiry_date' => Carbon::now()->addDays(30)->format('Y-m-d'),
            'total_rides' => $totalRides,
            'total_spent' => $totalSpent,
            'member_since' => $client->created_at->format('Y-m-d'),
            'loyalty_level' => $loyaltyLevel,
            'how_to_use' => [
                'Book your next ride through our website or app',
                'Enter promo code "MILESTONE' . $totalRides . '" at checkout',
                'Enjoy your ' . $discountPercentage . '% discount on the total fare',
                'Valid for 30 days from today'
            ]
        ];
    }

    /**
     * Generate birthday reward data
     */
    private function generateBirthdayReward($client): array
    {
        return [
            'reward_title' => 'Happy Birthday Special Gift!',
            'discount_percentage' => 25,
            'promo_code' => 'BIRTHDAY' . date('Y'),
            'expiry_date' => Carbon::now()->addDays(7)->format('Y-m-d'),
            'how_to_use' => [
                'Book any ride within the next 7 days',
                'Enter promo code "BIRTHDAY' . date('Y') . '" at checkout',
                'Enjoy 25% off your birthday ride',
                'Valid until ' . Carbon::now()->addDays(7)->format('F j, Y')
            ]
        ];
    }

    /**
     * Generate referral reward data
     */
    private function generateReferralReward($referrer, $newClient): array
    {
        return [
            'reward_title' => 'Thank You for Referring ' . $newClient->name . '!',
            'reward_amount' => 10.00,
            'promo_code' => 'REFERRAL' . $newClient->id,
            'expiry_date' => Carbon::now()->addDays(60)->format('Y-m-d'),
            'how_to_use' => [
                'Your £10 credit has been added to your account',
                'Use it on any future booking automatically',
                'Credit will be applied at checkout',
                'Valid for 60 days from today',
                'Keep referring friends to earn more credits!'
            ]
        ];
    }
}
