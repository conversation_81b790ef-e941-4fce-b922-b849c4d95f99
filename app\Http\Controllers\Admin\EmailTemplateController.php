<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class EmailTemplateController extends Controller
{
    /**
     * Display a listing of email templates
     */
    public function index(Request $request)
    {
        $query = EmailTemplate::query();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $templates = $query->orderBy('name')->paginate(20);

        // Get filter options
        $types = EmailTemplate::select('type')->distinct()->pluck('type')->filter()->sort();
        $categories = EmailTemplate::select('category')->distinct()->pluck('category')->filter()->sort();

        return view('admin.email.templates.index', compact('templates', 'types', 'categories'));
    }

    /**
     * Show the form for creating a new template
     */
    public function create()
    {
        $template = new EmailTemplate();
        return view('admin.email.templates.create', compact('template'));
    }

    /**
     * Store a newly created template
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:booking,payment,notification,marketing,system',
            'category' => 'nullable|string|in:client,driver,admin',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $slug = Str::slug($request->name);
            
            // Ensure unique slug
            $originalSlug = $slug;
            $counter = 1;
            while (EmailTemplate::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            EmailTemplate::create([
                'name' => $request->name,
                'slug' => $slug,
                'type' => $request->type,
                'category' => $request->category,
                'subject' => $request->subject,
                'content' => $request->content,
                'description' => $request->description,
                'variables' => $request->variables ?? [],
                'is_active' => $request->boolean('is_active', true),
                'is_system' => false,
            ]);

            return redirect()->route('admin.email.templates.index')
                ->with('success', 'Email template created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create email template: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified template
     */
    public function show(EmailTemplate $template)
    {
        return view('admin.email.templates.show', compact('template'));
    }

    /**
     * Show the form for editing the specified template
     */
    public function edit(EmailTemplate $template)
    {
        return view('admin.email.templates.edit', compact('template'));
    }

    /**
     * Update the specified template
     */
    public function update(Request $request, EmailTemplate $template)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:booking,payment,notification,marketing,system',
            'category' => 'nullable|string|in:client,driver,admin',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $updateData = [
                'name' => $request->name,
                'type' => $request->type,
                'category' => $request->category,
                'subject' => $request->subject,
                'content' => $request->content,
                'description' => $request->description,
                'variables' => $request->variables ?? [],
                'is_active' => $request->boolean('is_active', true),
            ];

            // Update slug if name changed and it's not a system template
            if (!$template->is_system && $request->name !== $template->name) {
                $slug = Str::slug($request->name);
                
                // Ensure unique slug
                $originalSlug = $slug;
                $counter = 1;
                while (EmailTemplate::where('slug', $slug)->where('id', '!=', $template->id)->exists()) {
                    $slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
                
                $updateData['slug'] = $slug;
            }

            $template->update($updateData);

            return redirect()->route('admin.email.templates.index')
                ->with('success', 'Email template updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update email template: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified template
     */
    public function destroy(EmailTemplate $template)
    {
        try {
            // Prevent deletion of system templates
            if ($template->is_system) {
                return redirect()->back()
                    ->with('error', 'System templates cannot be deleted.');
            }

            $template->delete();

            return redirect()->route('admin.email.templates.index')
                ->with('success', 'Email template deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete email template: ' . $e->getMessage());
        }
    }

    /**
     * Preview template with sample data
     */
    public function preview(EmailTemplate $template)
    {
        $previewData = $template->getPreviewData();
        $renderedContent = $template->renderContent($previewData);
        $renderedSubject = $template->renderSubject($previewData);

        return view('admin.email.templates.preview', compact('template', 'renderedContent', 'renderedSubject', 'previewData'));
    }

    /**
     * Toggle template status
     */
    public function toggleStatus(EmailTemplate $template)
    {
        try {
            $template->update(['is_active' => !$template->is_active]);

            $status = $template->is_active ? 'activated' : 'deactivated';
            
            return response()->json([
                'success' => true,
                'message' => "Template {$status} successfully.",
                'is_active' => $template->is_active,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update template status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Duplicate template
     */
    public function duplicate(EmailTemplate $template)
    {
        try {
            $newTemplate = $template->replicate();
            $newTemplate->name = $template->name . ' (Copy)';
            $newTemplate->slug = Str::slug($newTemplate->name);
            $newTemplate->is_system = false;
            
            // Ensure unique slug
            $originalSlug = $newTemplate->slug;
            $counter = 1;
            while (EmailTemplate::where('slug', $newTemplate->slug)->exists()) {
                $newTemplate->slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            
            $newTemplate->save();

            return redirect()->route('admin.email.templates.edit', $newTemplate)
                ->with('success', 'Template duplicated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to duplicate template: ' . $e->getMessage());
        }
    }
}
