<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\BlogPost;
use App\Helpers\SettingsHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $vehicles = Vehicle::where('is_active', true)->take(6)->get();

        return view('welcome', compact('vehicles'));
    }

    /**
     * Display the about page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function about()
    {
        $aboutContent = SettingsHelper::getAboutUs();
        return view('about', compact('aboutContent'));
    }

    /**
     * Display the services page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function services()
    {
        return view('services');
    }

    /**
     * Display the contact page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Process the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContact(Request $request)
    {
        // Use the enhanced EmailReceivingService for processing
        $result = \App\Services\EmailReceivingService::processContactForm($request->all());

        if ($result['success']) {
            return redirect()->back()->with('success', 'Your message has been sent successfully. We will get back to you soon.');
        } else {
            // If there are validation errors, return with errors
            if (isset($result['errors'])) {
                return redirect()->back()->withErrors($result['errors'])->withInput();
            }

            // For other errors, show generic error message
            return redirect()->back()->with('error', $result['message'] ?? 'There was an error sending your message. Please try again or contact us directly.');
        }
    }

    /**
     * Display the privacy policy page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function privacyPolicy()
    {
        $privacyContent = SettingsHelper::getPrivacyPolicy();
        return view('privacy-policy', compact('privacyContent'));
    }

    /**
     * Display the terms and conditions page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function termsAndConditions()
    {
        $termsContent = SettingsHelper::getTermsAndConditions();
        return view('terms-and-conditions', compact('termsContent'));
    }

    /**
     * Display the fleet page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function fleet()
    {
        $vehicles = Vehicle::where('is_active', true)->get();
        return view('fleet', compact('vehicles'));
    }



    /**
     * Display the FAQ page.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function faq()
    {
        return view('faq');
    }


}
