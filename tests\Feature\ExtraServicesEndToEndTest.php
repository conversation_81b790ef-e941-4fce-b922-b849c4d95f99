<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Setting;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class ExtraServicesEndToEndTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vehicle;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'client',
            'email_verified_at' => now(),
        ]);

        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create([
            'is_active' => true,
        ]);

        // Initialize extra services settings
        $this->initializeExtraServicesSettings();

        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);
        
        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });
    }

    private function initializeExtraServicesSettings()
    {
        $settings = [
            ['key' => 'meet_and_greet_fee', 'value' => '10.00', 'group' => 'extra_services'],
            ['key' => 'child_seat_fee', 'value' => '15.00', 'group' => 'extra_services'],
            ['key' => 'wheelchair_fee', 'value' => '0.00', 'group' => 'extra_services'],
            ['key' => 'extra_luggage_fee', 'value' => '5.00', 'group' => 'extra_services'],
            ['key' => 'meet_and_greet_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'child_seat_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'wheelchair_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'extra_luggage_enabled', 'value' => 'true', 'group' => 'extra_services'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /** @test */
    public function complete_booking_flow_with_extra_services()
    {
        // Step 1: Create booking with extra services
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 80.00, // Base fare + extra services
            'distance_value' => '10.5',
            'duration_value' => 20,
            
            // Extra services
            'meet_and_greet' => '1',
            'child_seat' => '1',
            'extra_luggage' => '1',
            // wheelchair_accessible not selected
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302); // Redirect to payment

        // Verify booking was created with extra services
        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);
        
        // Check extra services are saved correctly
        $this->assertTrue($booking->meet_and_greet);
        $this->assertTrue($booking->child_seat);
        $this->assertFalse($booking->wheelchair_accessible); // Not selected
        $this->assertTrue($booking->extra_luggage);

        // Step 2: Process pay later payment
        $paymentData = [
            'payment_method' => 'pay_later',
            'notes' => 'Test booking with extra services',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.process-payment', $booking->id), $paymentData);

        $response->assertStatus(200); // Should return view response

        // Verify booking status updated
        $booking->refresh();
        $this->assertEquals('confirmed', $booking->status);
        $this->assertEquals('pending', $booking->payment_status);

        // Verify payment record created
        $payment = Payment::where('booking_id', $booking->id)->first();
        $this->assertNotNull($payment);
        $this->assertEquals('pay_later', $payment->payment_method);

        // Step 3: Test client booking view shows extra services
        $response = $this->actingAs($this->user)
            ->get(route('client.bookings.show', $booking->id));

        $response->assertStatus(200);
        $response->assertSee('Extra Services');
        $response->assertSee('Meet');
        $response->assertSee('Child Seat');
        $response->assertSee('Extra Luggage Space');
        $response->assertDontSee('Wheelchair Accessible'); // Not selected

        // Step 4: Test admin booking view shows extra services
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)
            ->get(route('admin.bookings.show', $booking->id));

        $response->assertStatus(200);
        $response->assertSee('Meet and Greet');
        $response->assertSee('Child Seat');
        $response->assertSee('Extra Luggage');

        // Success! All extra services integration tests passed
    }

    /** @test */
    public function booking_without_extra_services_works_correctly()
    {
        // Create booking without any extra services
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00, // Base fare only
            'distance_value' => '10.5',
            'duration_value' => 20,
            // No extra services selected
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);
        
        // Check no extra services are saved
        $this->assertFalse($booking->meet_and_greet);
        $this->assertFalse($booking->child_seat);
        $this->assertFalse($booking->wheelchair_accessible);
        $this->assertFalse($booking->extra_luggage);

        // Test client view doesn't show extra services section when none are selected
        $response = $this->actingAs($this->user)
            ->get(route('client.bookings.show', $booking->id));

        $response->assertStatus(200);
        // Since no extra services are selected, the section should not appear
        $response->assertDontSee('Meet & Greet Service');
        $response->assertDontSee('Child Seat');
        $response->assertDontSee('Extra Luggage Space');
    }

    /** @test */
    public function disabled_extra_services_are_not_processed()
    {
        // Disable meet and greet service
        Setting::where('key', 'meet_and_greet_enabled')->update(['value' => 'false']);

        // Clear any potential cache
        \Illuminate\Support\Facades\Cache::flush();

        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00,
            'distance_value' => '10.5',
            'duration_value' => 20,
            
            // Try to select disabled service
            'meet_and_greet' => '1',
            'child_seat' => '1',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302);

        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);



        // Meet and greet should not be saved (disabled)
        $this->assertFalse($booking->meet_and_greet);
        // Child seat should be saved (enabled)
        $this->assertTrue($booking->child_seat);
    }
}
