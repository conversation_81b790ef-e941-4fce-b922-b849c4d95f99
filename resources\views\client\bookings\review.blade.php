@extends('layouts.client')

@section('title', 'Leave a Review')

@section('styles')
<style>
    .review-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .review-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .review-card .card-header {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        color: white;
        border-bottom: none;
        padding: 20px;
        border-radius: 10px 10px 0 0;
    }

    .review-card .card-body {
        padding: 30px;
    }

    .booking-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .booking-info-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
        color: #343a40;
    }

    .booking-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        align-items: center;
    }

    .booking-info-row:last-child {
        margin-bottom: 0;
    }

    .booking-info-label {
        font-weight: 600;
        color: #495057;
    }

    .booking-info-value {
        color: #6c757d;
        background-color: #fff;
        padding: 8px 12px;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0,0,0,0.05);
    }

    .star-rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: center;
        margin: 30px 0;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        cursor: pointer;
        width: 50px;
        height: 50px;
        margin: 0 5px;
        position: relative;
        font-size: 40px;
        color: #ddd;
        transition: all 0.3s;
    }

    .star-rating label:before {
        content: '\f005';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        top: 0;
        left: 0;
    }

    .star-rating input:checked ~ label,
    .star-rating label:hover,
    .star-rating label:hover ~ label {
        color: #ee393d;
        transform: scale(1.2);
    }

    .star-rating input:checked + label:hover,
    .star-rating input:checked ~ label:hover,
    .star-rating label:hover ~ input:checked ~ label,
    .star-rating input:checked ~ label:hover ~ label {
        color: #ee393d;
        transform: scale(1.2);
    }

    .rating-value {
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
        color: #343a40;
        margin-top: 10px;
    }

    .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(248, 193, 44, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #e0a800;
        border-color: #e0a800;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(248, 193, 44, 0.3);
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .welcome-banner {
        background: linear-gradient(135deg, #343a40 0%, #121416 100%);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        color: white;
        padding: 30px;
        margin-bottom: 30px;
    }

    .vehicle-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .vehicle-image {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 5px;
        margin-right: 15px;
    }

    .vehicle-image-placeholder {
        width: 80px;
        height: 60px;
        background-color: #e9ecef;
        border-radius: 5px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        font-size: 1.5rem;
    }

    .vehicle-details h5 {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .vehicle-details p {
        margin-bottom: 0;
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<div class="welcome-banner" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">Leave a Review</h2>
            <p class="mb-0">Share your experience with this ride and help us improve our service</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-light">
                <i class="fas fa-arrow-left me-2"></i> Back to Booking
            </a>
        </div>
    </div>
</div>

@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<div class="row">
    <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card review-card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> Booking Details</h4>
            </div>
            <div class="card-body">
                <div class="vehicle-info">
                    @if($booking->vehicle->image)
                        <img src="{{ asset('storage/' . $booking->vehicle->image) }}" class="vehicle-image" alt="{{ $booking->vehicle->name }}">
                    @else
                        <div class="vehicle-image-placeholder">
                            <i class="fas fa-car"></i>
                        </div>
                    @endif
                    <div class="vehicle-details">
                        <h5>{{ $booking->vehicle->name }}</h5>
                        <p>{{ $booking->vehicle->type }}</p>
                    </div>
                </div>

                <div class="booking-info">
                    <h5 class="booking-info-title"><i class="fas fa-calendar-check me-2"></i> Booking #{{ $booking->booking_number }}</h5>

                    <div class="booking-info-row">
                        <span class="booking-info-label">Date:</span>
                        <span class="booking-info-value">{{ $booking->pickup_date->format('M d, Y') }}</span>
                    </div>

                    <div class="booking-info-row">
                        <span class="booking-info-label">Time:</span>
                        <span class="booking-info-value">{{ $booking->pickup_date->format('h:i A') }}</span>
                    </div>

                    <div class="booking-info-row">
                        <span class="booking-info-label">Amount:</span>
                        <span class="booking-info-value">@currency(){{ number_format($booking->amount, 2) }}</span>
                    </div>

                    <div class="booking-info-row">
                        <span class="booking-info-label">Status:</span>
                        <span class="booking-info-value">{{ ucfirst($booking->status) }}</span>
                    </div>
                </div>

                <div class="booking-info">
                    <h5 class="booking-info-title"><i class="fas fa-map-marker-alt me-2"></i> Locations</h5>

                    <div class="booking-info-row">
                        <span class="booking-info-label">Pickup:</span>
                        <span class="booking-info-value">{{ Str::limit($booking->pickup_address, 30) }}</span>
                    </div>

                    @if($booking->via_stops && count($booking->via_stops) > 0)
                        @foreach($booking->via_stops as $index => $viaStop)
                            <div class="booking-info-row">
                                <span class="booking-info-label">Via Stop {{ $index + 1 }}:</span>
                                <span class="booking-info-value">{{ Str::limit($viaStop['address'] ?? 'Address not specified', 30) }}</span>
                            </div>
                        @endforeach
                    @endif

                    @if ($booking->booking_type !== 'hourly')
                        <div class="booking-info-row">
                            <span class="booking-info-label">Dropoff:</span>
                            <span class="booking-info-value">{{ Str::limit($booking->dropoff_address, 30) }}</span>
                        </div>
                    @endif
                </div>

                @if ($booking->driver)
                    <div class="booking-info">
                        <h5 class="booking-info-title"><i class="fas fa-user me-2"></i> Driver</h5>

                        <div class="booking-info-row">
                            <span class="booking-info-label">Driver ID:</span>
                            <span class="booking-info-value">Driver #{{ substr($booking->driver->id, 0, 5) }}</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-8" data-aos="fade-up" data-aos-delay="200">
        <div class="card review-card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-star me-2"></i> Your Review</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('client.bookings.store-review', $booking->id) }}" method="POST">
                    @csrf

                    <div class="text-center mb-4">
                        <h5 class="mb-3">How would you rate your experience?</h5>

                        <div class="star-rating">
                            <input type="radio" id="star5" name="rating" value="5" {{ old('rating') == 5 ? 'checked' : '' }} />
                            <label for="star5" title="5 stars"></label>
                            <input type="radio" id="star4" name="rating" value="4" {{ old('rating') == 4 ? 'checked' : '' }} />
                            <label for="star4" title="4 stars"></label>
                            <input type="radio" id="star3" name="rating" value="3" {{ old('rating') == 3 ? 'checked' : '' }} />
                            <label for="star3" title="3 stars"></label>
                            <input type="radio" id="star2" name="rating" value="2" {{ old('rating') == 2 ? 'checked' : '' }} />
                            <label for="star2" title="2 stars"></label>
                            <input type="radio" id="star1" name="rating" value="1" {{ old('rating') == 1 ? 'checked' : '' }} />
                            <label for="star1" title="1 star"></label>
                        </div>

                        <div class="rating-value" id="rating-text">Select a rating</div>
                    </div>

                    <div class="mb-4">
                        <label for="comment" class="form-label">Tell us about your experience</label>
                        <textarea class="form-control" id="comment" name="comment" rows="6" placeholder="Share your thoughts about the ride, driver, vehicle, and overall experience...">{{ old('comment') }}</textarea>
                        <div class="form-text">Your review helps us improve our service and assists other clients in making informed decisions.</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i> Submit Review
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update rating text when a star is selected
        const ratingInputs = document.querySelectorAll('.star-rating input');
        const ratingText = document.getElementById('rating-text');
        const ratingDescriptions = [
            'Select a rating',
            'Poor - 1 star',
            'Fair - 2 stars',
            'Good - 3 stars',
            'Very Good - 4 stars',
            'Excellent - 5 stars'
        ];

        ratingInputs.forEach(input => {
            input.addEventListener('change', function() {
                const value = parseInt(this.value);
                ratingText.textContent = ratingDescriptions[value];
            });
        });

        // Check if a rating is already selected (e.g., from old input)
        ratingInputs.forEach(input => {
            if (input.checked) {
                const value = parseInt(input.value);
                ratingText.textContent = ratingDescriptions[value];
            }
        });
    });
</script>
@endsection
