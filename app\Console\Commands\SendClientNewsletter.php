<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\EmailService;
use Carbon\Carbon;

class SendClientNewsletter extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:newsletter {--type=monthly : Newsletter type (monthly, seasonal, promotional)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send newsletter to all active clients';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $newsletterType = $this->option('type');
        $this->info("Sending {$newsletterType} newsletter to clients...");

        $newsletterData = $this->generateNewsletterContent($newsletterType);
        $newslettersSent = 0;

        // Get all active clients who have opted in for promotional emails
        $clients = User::where('role', 'client')
            ->where('is_active', true)
            ->whereHas('notificationSettings', function($query) {
                $query->where('email_promotions', true);
            })
            ->orWhere(function($query) {
                // Include clients without notification settings (default to true)
                $query->where('role', 'client')
                      ->where('is_active', true)
                      ->whereDoesntHave('notificationSettings');
            })
            ->get();

        foreach ($clients as $client) {
            try {
                EmailService::sendClientNewsletter($client, $newsletterData, $newsletterType);

                $newslettersSent++;
                $this->line("✅ Newsletter sent to {$client->name} ({$client->email})");
            } catch (\Exception $e) {
                $this->error("❌ Failed to send newsletter to {$client->name}: " . $e->getMessage());
            }
        }

        $this->info("📧 {$newsletterType} newsletter sent to {$newslettersSent} clients");
        return Command::SUCCESS;
    }

    /**
     * Generate newsletter content based on type
     */
    private function generateNewsletterContent(string $type): array
    {
        $currentMonth = Carbon::now()->format('F Y');

        switch ($type) {
            case 'monthly':
                return $this->generateMonthlyNewsletter($currentMonth);
            case 'seasonal':
                return $this->generateSeasonalNewsletter();
            case 'promotional':
                return $this->generatePromotionalNewsletter();
            default:
                return $this->generateMonthlyNewsletter($currentMonth);
        }
    }

    /**
     * Generate monthly newsletter content
     */
    private function generateMonthlyNewsletter(string $month): array
    {
        return [
            'title' => "YNR Cars Monthly Update - {$month}",
            'subtitle' => 'Your monthly dose of news, updates, and exclusive offers',
            'intro' => "Welcome to our {$month} newsletter! We're excited to share some important updates, new features, and exclusive offers with our valued clients.",
            'featured_article' => [
                'title' => 'New Fleet Additions - Premium Vehicles Now Available',
                'content' => 'We\'re thrilled to announce the addition of professional vehicles to our fleet. Experience premium comfort with our new Mercedes-Benz and BMW vehicles, available for special occasions and business travel.',
                'link' => url('/vehicles')
            ],
            'company_updates' => [
                [
                    'title' => 'Extended Service Hours',
                    'content' => 'We now offer 24/7 service to better serve your transportation needs, any time of day or night.',
                    'date' => Carbon::now()->subDays(5)->format('F j, Y')
                ],
                [
                    'title' => 'Mobile App Improvements',
                    'content' => 'Our mobile app has been updated with new features including real-time tracking and easier booking process.',
                    'date' => Carbon::now()->subDays(10)->format('F j, Y')
                ],
                [
                    'title' => 'Driver Training Program',
                    'content' => 'All our drivers have completed advanced client service training to ensure the best possible experience.',
                    'date' => Carbon::now()->subDays(15)->format('F j, Y')
                ]
            ],
            'promotions' => [
                [
                    'title' => 'Monthly Subscriber Discount',
                    'description' => 'Get 15% off your next 3 bookings as a newsletter subscriber!',
                    'promo_code' => 'NEWSLETTER15',
                    'expiry' => Carbon::now()->addDays(30)->format('F j, Y')
                ]
            ],
            'service_highlights' => [
                [
                    'icon' => '🚗',
                    'title' => 'Fleet Expansion',
                    'description' => '20+ new vehicles added to serve you better'
                ],
                [
                    'icon' => '⭐',
                    'title' => 'Client Satisfaction',
                    'description' => '98% client satisfaction rating this month'
                ],
                [
                    'icon' => '📱',
                    'title' => 'Mobile Bookings',
                    'description' => 'Book rides 50% faster with our improved app'
                ],
                [
                    'icon' => '🕒',
                    'title' => '24/7 Service',
                    'description' => 'Round-the-clock availability for your convenience'
                ]
            ],
            'client_spotlight' => [
                'testimonial' => 'YNR Cars has been my go-to transportation service for over a year. Professional drivers, clean vehicles, and always on time. Highly recommended!',
                'client_name' => 'Sarah Johnson'
            ],
            'social_media' => [
                'facebook' => 'https://facebook.com/ynrcars',
                'twitter' => 'https://twitter.com/ynrcars',
                'instagram' => 'https://instagram.com/ynrcars'
            ]
        ];
    }

    /**
     * Generate seasonal newsletter content
     */
    private function generateSeasonalNewsletter(): array
    {
        $season = $this->getCurrentSeason();

        return [
            'title' => "YNR Cars {$season} Special Newsletter",
            'subtitle' => "Seasonal updates and special {$season} offers",
            'intro' => "As {$season} arrives, we're excited to share special seasonal offers and important updates for the season ahead.",
            'featured_article' => [
                'title' => "{$season} Travel Tips and Special Services",
                'content' => "Make the most of {$season} with our seasonal travel tips and special services designed for the season.",
                'link' => url('/seasonal-services')
            ],
            'promotions' => [
                [
                    'title' => "{$season} Special Discount",
                    'description' => "Celebrate {$season} with 20% off all bookings!",
                    'promo_code' => strtoupper($season) . '20',
                    'expiry' => Carbon::now()->addDays(45)->format('F j, Y')
                ]
            ],
            'upcoming_events' => [
                [
                    'title' => "{$season} Service Adjustments",
                    'description' => "Special scheduling and services for the {$season} season",
                    'date' => Carbon::now()->addDays(7)->format('F j, Y'),
                    'location' => 'All service areas'
                ]
            ]
        ];
    }

    /**
     * Generate promotional newsletter content
     */
    private function generatePromotionalNewsletter(): array
    {
        return [
            'title' => 'YNR Cars Special Promotions - Limited Time Offers!',
            'subtitle' => 'Exclusive deals and discounts just for you',
            'intro' => 'Don\'t miss out on these amazing limited-time offers! Book now and save on your next rides.',
            'promotions' => [
                [
                    'title' => 'Flash Sale - 30% Off',
                    'description' => 'Limited time flash sale! Get 30% off your next booking.',
                    'promo_code' => 'FLASH30',
                    'expiry' => Carbon::now()->addDays(7)->format('F j, Y')
                ],
                [
                    'title' => 'Refer a Friend Bonus',
                    'description' => 'Refer a friend and both get £10 credit!',
                    'promo_code' => 'REFER10',
                    'expiry' => Carbon::now()->addDays(30)->format('F j, Y')
                ],
                [
                    'title' => 'Weekend Special',
                    'description' => 'Book weekend rides and get 25% off!',
                    'promo_code' => 'WEEKEND25',
                    'expiry' => Carbon::now()->addDays(14)->format('F j, Y')
                ]
            ],
            'featured_article' => [
                'title' => 'How to Maximize Your Savings with YNR Cars',
                'content' => 'Learn about our loyalty program, referral bonuses, and seasonal promotions to get the best value from our services.',
                'link' => url('/savings-guide')
            ]
        ];
    }

    /**
     * Get current season
     */
    private function getCurrentSeason(): string
    {
        $month = Carbon::now()->month;

        return match(true) {
            $month >= 3 && $month <= 5 => 'Spring',
            $month >= 6 && $month <= 8 => 'Summer',
            $month >= 9 && $month <= 11 => 'Autumn',
            default => 'Winter'
        };
    }
}
