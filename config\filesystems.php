<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'public'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL', 'http://localhost').'/storage',
            'visibility' => 'public',
            'throw' => false,
            'permissions' => [
                'file' => [
                    'public' => 0644,
                    'private' => 0600,
                ],
                'dir' => [
                    'public' => 0755,
                    'private' => 0700,
                ],
            ],
        ],

        'private' => [
            'driver' => 'local',
            'root' => storage_path('app/private'),
            'visibility' => 'private',
            'throw' => false,
        ],

        'documents' => [
            'driver' => 'local',
            'root' => storage_path('app/public/documents'),
            'url' => env('APP_URL').'/storage/documents',
            'visibility' => 'public',
            'throw' => false,
        ],

        'uploads' => [
            'driver' => 'local',
            'root' => storage_path('app/public/uploads'),
            'url' => env('APP_URL').'/storage/uploads',
            'visibility' => 'public',
            'throw' => false,
        ],

        'temp' => [
            'driver' => 'local',
            'root' => storage_path('app/public/temp'),
            'url' => env('APP_URL').'/storage/temp',
            'visibility' => 'public',
            'throw' => false,
        ],

        'backups' => [
            'driver' => 'local',
            'root' => storage_path('app/public/backups'),
            'url' => env('APP_URL').'/storage/backups',
            'visibility' => 'public',
            'throw' => false,
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for file uploads including size limits and allowed types.
    |
    */

    'max_file_size' => env('MAX_FILE_SIZE', 10240), // KB (10MB default)
    'max_image_size' => env('MAX_IMAGE_SIZE', 5120), // KB (5MB default for images)

    'allowed_mimes' => [
        'jpeg', 'jpg', 'png', 'gif', 'webp', 'svg',
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'txt', 'csv', 'zip', 'rar'
    ],

    'image_mimes' => ['jpeg', 'jpg', 'png', 'gif', 'webp'],

    'document_mimes' => ['pdf', 'doc', 'docx', 'txt'],

    'upload_validation' => [
        'images' => [
            'max_width' => 2048,
            'max_height' => 2048,
            'min_width' => 100,
            'min_height' => 100,
        ],
        'vehicles' => [
            'max_width' => 1920,
            'max_height' => 1080,
            'recommended_width' => 800,
            'recommended_height' => 600,
        ],
        'profiles' => [
            'max_width' => 500,
            'max_height' => 500,
            'recommended_size' => 300,
        ],
        'logos' => [
            'max_width' => 500,
            'max_height' => 200,
            'recommended_height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Cleanup Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic storage cleanup.
    |
    */

    'cleanup' => [
        'temp_files_hours' => env('CLEANUP_TEMP_HOURS', 24),
        'log_files_days' => env('CLEANUP_LOG_DAYS', 30),
        'enable_auto_cleanup' => env('ENABLE_AUTO_CLEANUP', true),
    ],

];
