<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class RoutePreferencesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $routeSettings = [
            [
                'key' => 'prefer_highways',
                'value' => 'true',
                'label' => 'Prefer Highways',
                'description' => 'Prioritize highways and main roads for long distance routes',
                'type' => 'boolean',
                'group' => 'routing',
                'order' => 1,
            ],
            [
                'key' => 'avoid_tolls',
                'value' => 'false',
                'label' => 'Avoid Tolls',
                'description' => 'Avoid toll roads when calculating routes',
                'type' => 'boolean',
                'group' => 'routing',
                'order' => 2,
            ],
            [
                'key' => 'avoid_ferries',
                'value' => 'true',
                'label' => 'Avoid Ferries',
                'description' => 'Avoid ferry routes for better reliability',
                'type' => 'boolean',
                'group' => 'routing',
                'order' => 3,
            ],
            [
                'key' => 'optimize_waypoints',
                'value' => 'true',
                'label' => 'Optimize Waypoints',
                'description' => 'Optimize the order of waypoints for efficiency',
                'type' => 'boolean',
                'group' => 'routing',
                'order' => 4,
            ],
            [
                'key' => 'route_preference_mode',
                'value' => 'highway_priority',
                'label' => 'Route Preference Mode',
                'description' => 'Primary routing preference for long distance trips',
                'type' => 'select',
                'group' => 'routing',
                'order' => 5,
                'options' => json_encode([
                    'highway_priority' => 'Highway Priority (Fastest)',
                    'balanced' => 'Balanced (Distance + Time)',
                    'shortest' => 'Shortest Distance',
                    'fastest' => 'Fastest Time'
                ]),
            ],
        ];

        foreach ($routeSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Route preference settings have been seeded successfully.');
    }
}
