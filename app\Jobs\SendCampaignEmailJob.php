<?php

namespace App\Jobs;

use App\Models\EmailCampaign;
use App\Models\EmailCampaignRecipient;
use App\Mail\CampaignMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendCampaignEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 60; // 1 minute
    public $tries = 3;

    protected EmailCampaign $campaign;
    protected EmailCampaignRecipient $recipient;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailCampaign $campaign, EmailCampaignRecipient $recipient)
    {
        $this->campaign = $campaign;
        $this->recipient = $recipient;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Check if recipient is still pending
            if ($this->recipient->status !== 'pending') {
                return;
            }

            // Check if user has unsubscribed from marketing emails
            if ($this->recipient->user && $this->campaign->recipient_type === 'marketing') {
                $notificationSettings = $this->recipient->user->notificationSettings;
                if ($notificationSettings && !$notificationSettings->email_promotions) {
                    $this->recipient->markAsUnsubscribed();
                    return;
                }
            }

            // Prepare email data
            $emailData = $this->prepareEmailData();

            // Send the email
            Mail::to($this->recipient->email)
                ->send(new CampaignMail($this->campaign, $emailData));

            // Mark as sent
            $this->recipient->markAsSent();

            // Update campaign statistics
            $this->campaign->increment('sent_count');

            Log::info('Campaign email sent successfully', [
                'campaign_id' => $this->campaign->id,
                'recipient_id' => $this->recipient->id,
                'email' => $this->recipient->email,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send campaign email', [
                'campaign_id' => $this->campaign->id,
                'recipient_id' => $this->recipient->id,
                'email' => $this->recipient->email,
                'error' => $e->getMessage(),
            ]);

            // Mark as failed
            $this->recipient->markAsFailed($e->getMessage());
            $this->campaign->increment('failed_count');

            throw $e;
        }
    }

    /**
     * Prepare email data for the recipient
     */
    private function prepareEmailData(): array
    {
        $data = [
            'recipient_email' => $this->recipient->email,
            'campaign_id' => $this->campaign->id,
            'recipient_id' => $this->recipient->id,
        ];

        // Add user-specific data if available
        if ($this->recipient->user) {
            $user = $this->recipient->user;
            $data = array_merge($data, [
                'user_name' => $user->name,
                'user_id' => $user->id,
                'user_role' => $user->role,
            ]);

            // Add role-specific data
            if ($user->role === 'client') {
                $data = array_merge($data, [
                    'total_bookings' => $user->bookings()->count(),
                    'last_booking_date' => $user->bookings()->latest()->first()?->created_at?->format('Y-m-d'),
                ]);
            } elseif ($user->role === 'driver') {
                $data = array_merge($data, [
                    'total_rides' => $user->driverBookings()->count(),
                    'is_available' => $user->is_available,
                ]);
            }
        }

        // Add company data
        $data = array_merge($data, [
            'company_name' => \App\Services\SettingsService::getCompanyName(),
            'company_email' => \App\Services\SettingsService::getCompanyEmail(),
            'company_phone' => \App\Services\SettingsService::getCompanyPhone(),
            'unsubscribe_url' => route('email.unsubscribe', [
                'campaign' => $this->campaign->id,
                'recipient' => $this->recipient->id,
                'token' => hash('sha256', $this->recipient->email . $this->campaign->id),
            ]),
            'tracking_pixel_url' => route('email.track.open', [
                'campaign' => $this->campaign->id,
                'recipient' => $this->recipient->id,
                'token' => hash('sha256', $this->recipient->email . $this->campaign->id),
            ]),
        ]);

        return $data;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Campaign email job failed', [
            'campaign_id' => $this->campaign->id,
            'recipient_id' => $this->recipient->id,
            'email' => $this->recipient->email,
            'error' => $exception->getMessage(),
        ]);

        $this->recipient->markAsFailed($exception->getMessage());
        $this->campaign->increment('failed_count');
    }
}
