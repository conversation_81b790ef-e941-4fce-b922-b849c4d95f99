@extends('layouts.admin')

@section('title', 'Campaign Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📢 {{ $campaign->name }}</h1>
            <p class="text-muted">Campaign details and performance analytics</p>
        </div>
        <div>
            @if($campaign->canBeEdited())
                <a href="{{ route('admin.email.campaigns.edit', $campaign) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i> Edit
                </a>
            @endif
            <a href="{{ route('admin.email.campaigns.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Campaigns
            </a>
        </div>
    </div>

    <!-- Campaign Status & Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Recipients
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($campaign->total_recipients) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Emails Sent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($campaign->sent_count) }}
                            </div>
                            @if($campaign->total_recipients > 0)
                                <div class="small text-muted">
                                    {{ number_format(($campaign->sent_count / $campaign->total_recipients) * 100, 1) }}% of total
                                </div>
                            @endif
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-paper-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Open Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($campaign->open_rate, 1) }}%
                            </div>
                            <div class="small text-muted">
                                {{ number_format($campaign->opened_count) }} opened
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-eye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Click Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($campaign->click_rate, 1) }}%
                            </div>
                            <div class="small text-muted">
                                {{ number_format($campaign->clicked_count) }} clicked
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Campaign Content -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📧 Campaign Content</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-primary">Subject:</h6>
                        <div class="p-3 bg-light rounded">
                            {{ $campaign->subject }}
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-primary">Content:</h6>
                        <div class="p-3 bg-light rounded" style="white-space: pre-wrap;">{{ $campaign->content }}</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.email.campaigns.preview', $campaign) }}" 
                           class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i> Full Preview
                        </a>
                    </div>
                </div>
            </div>

            <!-- Delivery Progress -->
            @if($campaign->status === 'sending' || $campaign->status === 'sent')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📊 Delivery Progress</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Delivery Progress</span>
                                <span>{{ $campaign->sent_count }} / {{ $campaign->total_recipients }}</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ $campaign->total_recipients > 0 ? ($campaign->sent_count / $campaign->total_recipients) * 100 : 0 }}%">
                                </div>
                            </div>
                        </div>

                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h6 mb-0 text-success">{{ number_format($campaign->delivered_count) }}</div>
                                    <small class="text-muted">Delivered</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h6 mb-0 text-info">{{ number_format($campaign->opened_count) }}</div>
                                    <small class="text-muted">Opened</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h6 mb-0 text-warning">{{ number_format($campaign->clicked_count) }}</div>
                                    <small class="text-muted">Clicked</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="h6 mb-0 text-danger">{{ number_format($campaign->failed_count) }}</div>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Recipients List -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">👥 Recipients</h6>
                    <div>
                        <select class="form-select form-select-sm" id="statusFilter" onchange="filterRecipients()">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="sent">Sent</option>
                            <option value="delivered">Delivered</option>
                            <option value="opened">Opened</option>
                            <option value="clicked">Clicked</option>
                            <option value="failed">Failed</option>
                            <option value="bounced">Bounced</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="recipientsTable">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Sent At</th>
                                    <th>Opened At</th>
                                    <th>Clicked At</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($campaign->recipients()->with('user')->paginate(20) as $recipient)
                                    <tr data-status="{{ $recipient->status }}">
                                        <td>
                                            <div>
                                                <div class="fw-bold">{{ $recipient->user->name ?? 'Unknown' }}</div>
                                                <div class="text-muted small">{{ $recipient->email }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst($recipient->user_type) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $recipient->status_badge_class }}">
                                                {{ ucfirst($recipient->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($recipient->sent_at)
                                                <small>{{ $recipient->sent_at->format('M j, g:i A') }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($recipient->opened_at)
                                                <small>{{ $recipient->opened_at->format('M j, g:i A') }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($recipient->clicked_at)
                                                <small>{{ $recipient->clicked_at->format('M j, g:i A') }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Campaign Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📋 Campaign Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td class="text-muted">Status:</td>
                            <td>
                                <span class="badge {{ $campaign->status_badge_class }}">
                                    {{ ucfirst($campaign->status) }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-muted">Recipient Type:</td>
                            <td>{{ ucfirst($campaign->recipient_type) }}</td>
                        </tr>
                        @if($campaign->template)
                            <tr>
                                <td class="text-muted">Template:</td>
                                <td>
                                    <a href="{{ route('admin.email.templates.show', $campaign->template) }}">
                                        {{ $campaign->template->name }}
                                    </a>
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <td class="text-muted">Created By:</td>
                            <td>{{ $campaign->creator->name ?? 'System' }}</td>
                        </tr>
                        <tr>
                            <td class="text-muted">Created:</td>
                            <td>{{ $campaign->created_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        @if($campaign->scheduled_at)
                            <tr>
                                <td class="text-muted">Scheduled:</td>
                                <td>{{ $campaign->scheduled_at->format('M j, Y g:i A') }}</td>
                            </tr>
                        @endif
                        @if($campaign->sent_at)
                            <tr>
                                <td class="text-muted">Sent:</td>
                                <td>{{ $campaign->sent_at->format('M j, Y g:i A') }}</td>
                            </tr>
                        @endif
                        @if($campaign->completed_at)
                            <tr>
                                <td class="text-muted">Completed:</td>
                                <td>{{ $campaign->completed_at->format('M j, Y g:i A') }}</td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">⚡ Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($campaign->canBeSent())
                            <button type="button" class="btn btn-success btn-sm" onclick="sendCampaign()">
                                <i class="fas fa-paper-plane me-1"></i> Send Now
                            </button>
                        @endif

                        @if($campaign->canBeCancelled())
                            <button type="button" class="btn btn-danger btn-sm" onclick="cancelCampaign()">
                                <i class="fas fa-stop me-1"></i> Cancel Campaign
                            </button>
                        @endif

                        @if($campaign->failed_count > 0)
                            <button type="button" class="btn btn-warning btn-sm" onclick="retryFailed()">
                                <i class="fas fa-redo me-1"></i> Retry Failed ({{ $campaign->failed_count }})
                            </button>
                        @endif

                        <a href="{{ route('admin.email.campaigns.preview', $campaign) }}" 
                           class="btn btn-info btn-sm" target="_blank">
                            <i class="fas fa-eye me-1"></i> Preview Email
                        </a>

                        @if($campaign->canBeEdited())
                            <a href="{{ route('admin.email.campaigns.edit', $campaign) }}" 
                               class="btn btn-warning btn-sm">
                                <i class="fas fa-edit me-1"></i> Edit Campaign
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Performance Summary -->
            @if($campaign->sent_count > 0)
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📈 Performance Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Delivery Rate:</span>
                                <span class="fw-bold">{{ number_format($campaign->delivery_rate, 1) }}%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Open Rate:</span>
                                <span class="fw-bold">{{ number_format($campaign->open_rate, 1) }}%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Click Rate:</span>
                                <span class="fw-bold">{{ number_format($campaign->click_rate, 1) }}%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Bounce Rate:</span>
                                <span class="fw-bold">{{ number_format($campaign->bounce_rate, 1) }}%</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Unsubscribe Rate:</span>
                                <span class="fw-bold">{{ number_format($campaign->unsubscribe_rate, 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function filterRecipients() {
    const filter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('#recipientsTable tbody tr');
    
    rows.forEach(row => {
        if (!filter || row.dataset.status === filter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function sendCampaign() {
    if (confirm('Are you sure you want to send this campaign now?')) {
        $.post('{{ route("admin.email.campaigns.send", $campaign) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                alert('Campaign queued for sending!');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }).fail(function() {
            alert('Error sending campaign');
        });
    }
}

function cancelCampaign() {
    if (confirm('Are you sure you want to cancel this campaign?')) {
        $.post('{{ route("admin.email.campaigns.cancel", $campaign) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                alert('Campaign cancelled!');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }).fail(function() {
            alert('Error cancelling campaign');
        });
    }
}

function retryFailed() {
    if (confirm('Are you sure you want to retry failed emails?')) {
        $.post('{{ route("admin.email.campaigns.retry-failed", $campaign) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                alert('Failed emails queued for retry!');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }).fail(function() {
            alert('Error retrying failed emails');
        });
    }
}

// Auto-refresh if campaign is sending
@if($campaign->status === 'sending')
    setInterval(function() {
        location.reload();
    }, 30000); // Refresh every 30 seconds
@endif
</script>
@endpush
