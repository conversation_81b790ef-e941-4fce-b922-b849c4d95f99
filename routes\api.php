<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Vehicle routes
Route::get('/vehicles', [App\Http\Controllers\Api\VehicleController::class, 'index']);

// Pricing routes
Route::post('/pricing/calculate', [App\Http\Controllers\Api\PricingController::class, 'calculatePrice']);

// CircleLoop Integration Routes (No authentication required for webhooks)
Route::prefix('circleloop')->name('api.circleloop.')->group(function () {
    Route::post('/webhook', [App\Http\Controllers\Api\CircleLoopController::class, 'webhook'])->name('webhook');
    Route::get('/test', [App\Http\Controllers\Api\CircleLoopController::class, 'test'])->name('test');
    Route::get('/current-call', [App\Http\Controllers\Api\CircleLoopController::class, 'getCurrentCall'])->name('current-call');
    Route::get('/recent-calls', [App\Http\Controllers\Api\CircleLoopController::class, 'getRecentCalls'])->name('recent-calls');
});



// Email Webhook Routes (No authentication required for webhooks)
Route::prefix('email')->name('api.email.')->group(function () {
    Route::prefix('webhook')->name('webhook.')->group(function () {
        Route::post('/incoming', [App\Http\Controllers\Api\EmailWebhookController::class, 'handleIncomingEmail'])->name('incoming');
        Route::post('/bounce', [App\Http\Controllers\Api\EmailWebhookController::class, 'handleBounce'])->name('bounce');
        Route::post('/delivery', [App\Http\Controllers\Api\EmailWebhookController::class, 'handleDelivery'])->name('delivery');
        Route::post('/open', [App\Http\Controllers\Api\EmailWebhookController::class, 'handleOpen'])->name('open');
        Route::get('/test', [App\Http\Controllers\Api\EmailWebhookController::class, 'test'])->name('test');
        Route::get('/config', [App\Http\Controllers\Api\EmailWebhookController::class, 'config'])->name('config');
    });
});


