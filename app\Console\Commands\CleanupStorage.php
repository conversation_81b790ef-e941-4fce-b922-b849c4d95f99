<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Services\StorageService;
use Carbon\Carbon;

class CleanupStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:cleanup 
                            {--temp-hours=24 : Hours after which temp files are deleted}
                            {--logs-days=30 : Days after which log files are deleted}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old temporary files, logs, and unused storage files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting storage cleanup...');

        $tempHours = (int) $this->option('temp-hours');
        $logsDays = (int) $this->option('logs-days');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be deleted');
        }

        $totalDeleted = 0;
        $totalSize = 0;

        // Clean up temporary files
        $this->info("Cleaning up temporary files older than {$tempHours} hours...");
        $tempResult = $this->cleanupTempFiles($tempHours, $dryRun);
        $totalDeleted += $tempResult['count'];
        $totalSize += $tempResult['size'];

        // Clean up old log files
        $this->info("Cleaning up log files older than {$logsDays} days...");
        $logResult = $this->cleanupLogFiles($logsDays, $dryRun);
        $totalDeleted += $logResult['count'];
        $totalSize += $logResult['size'];

        // Clean up orphaned files
        $this->info('Cleaning up orphaned files...');
        $orphanResult = $this->cleanupOrphanedFiles($dryRun);
        $totalDeleted += $orphanResult['count'];
        $totalSize += $orphanResult['size'];

        // Clean up empty directories
        $this->info('Cleaning up empty directories...');
        $dirResult = $this->cleanupEmptyDirectories($dryRun);

        // Show summary
        $this->newLine();
        $this->info('Storage cleanup completed!');
        $this->table(
            ['Category', 'Files Deleted', 'Space Freed'],
            [
                ['Temporary Files', $tempResult['count'], StorageService::formatBytes($tempResult['size'])],
                ['Log Files', $logResult['count'], StorageService::formatBytes($logResult['size'])],
                ['Orphaned Files', $orphanResult['count'], StorageService::formatBytes($orphanResult['size'])],
                ['Empty Directories', $dirResult['count'], '-'],
                ['TOTAL', $totalDeleted, StorageService::formatBytes($totalSize)],
            ]
        );

        // Show disk usage
        $this->showDiskUsage();

        return Command::SUCCESS;
    }

    /**
     * Clean up temporary files.
     */
    private function cleanupTempFiles(int $hours, bool $dryRun): array
    {
        $count = 0;
        $size = 0;

        try {
            $tempDisk = Storage::disk('public');
            $cutoffTime = Carbon::now()->subHours($hours);

            $tempDirectories = ['temp', 'uploads/temp'];

            foreach ($tempDirectories as $directory) {
                if (!$tempDisk->exists($directory)) {
                    continue;
                }

                $files = $tempDisk->allFiles($directory);

                foreach ($files as $file) {
                    $lastModified = $tempDisk->lastModified($file);
                    
                    if ($lastModified && $lastModified < $cutoffTime->timestamp) {
                        $fileSize = $tempDisk->size($file);
                        
                        if ($dryRun) {
                            $this->line("Would delete: {$file} (" . StorageService::formatBytes($fileSize) . ")");
                        } else {
                            if ($tempDisk->delete($file)) {
                                $this->line("Deleted: {$file} (" . StorageService::formatBytes($fileSize) . ")");
                            }
                        }
                        
                        $count++;
                        $size += $fileSize;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error('Error cleaning temp files: ' . $e->getMessage());
        }

        return ['count' => $count, 'size' => $size];
    }

    /**
     * Clean up old log files.
     */
    private function cleanupLogFiles(int $days, bool $dryRun): array
    {
        $count = 0;
        $size = 0;

        try {
            $logPath = storage_path('logs');
            $cutoffTime = Carbon::now()->subDays($days);

            $files = glob($logPath . '/*.log');

            foreach ($files as $file) {
                $lastModified = filemtime($file);
                
                if ($lastModified && $lastModified < $cutoffTime->timestamp) {
                    $fileSize = filesize($file);
                    
                    if ($dryRun) {
                        $this->line("Would delete: " . basename($file) . " (" . StorageService::formatBytes($fileSize) . ")");
                    } else {
                        if (unlink($file)) {
                            $this->line("Deleted: " . basename($file) . " (" . StorageService::formatBytes($fileSize) . ")");
                        }
                    }
                    
                    $count++;
                    $size += $fileSize;
                }
            }
        } catch (\Exception $e) {
            $this->error('Error cleaning log files: ' . $e->getMessage());
        }

        return ['count' => $count, 'size' => $size];
    }

    /**
     * Clean up orphaned files (files not referenced in database).
     */
    private function cleanupOrphanedFiles(bool $dryRun): array
    {
        $count = 0;
        $size = 0;

        try {
            // This is a simplified version - you might want to implement
            // more sophisticated orphan detection based on your models
            $publicDisk = Storage::disk('public');
            
            // Check profile photos
            $profilePhotos = $publicDisk->allFiles('profile-photos');
            $usedPhotos = \App\Models\User::whereNotNull('profile_photo')
                ->pluck('profile_photo')
                ->toArray();

            foreach ($profilePhotos as $photo) {
                if (!in_array($photo, $usedPhotos)) {
                    $fileSize = $publicDisk->size($photo);
                    
                    if ($dryRun) {
                        $this->line("Would delete orphaned photo: {$photo} (" . StorageService::formatBytes($fileSize) . ")");
                    } else {
                        if ($publicDisk->delete($photo)) {
                            $this->line("Deleted orphaned photo: {$photo} (" . StorageService::formatBytes($fileSize) . ")");
                        }
                    }
                    
                    $count++;
                    $size += $fileSize;
                }
            }

            // Check vehicle images
            $vehicleImages = $publicDisk->allFiles('vehicles');
            $usedImages = \App\Models\Vehicle::whereNotNull('image')
                ->pluck('image')
                ->toArray();

            foreach ($vehicleImages as $image) {
                if (!in_array($image, $usedImages)) {
                    $fileSize = $publicDisk->size($image);
                    
                    if ($dryRun) {
                        $this->line("Would delete orphaned vehicle image: {$image} (" . StorageService::formatBytes($fileSize) . ")");
                    } else {
                        if ($publicDisk->delete($image)) {
                            $this->line("Deleted orphaned vehicle image: {$image} (" . StorageService::formatBytes($fileSize) . ")");
                        }
                    }
                    
                    $count++;
                    $size += $fileSize;
                }
            }

        } catch (\Exception $e) {
            $this->error('Error cleaning orphaned files: ' . $e->getMessage());
        }

        return ['count' => $count, 'size' => $size];
    }

    /**
     * Clean up empty directories.
     */
    private function cleanupEmptyDirectories(bool $dryRun): array
    {
        $count = 0;

        try {
            $publicDisk = Storage::disk('public');
            $directories = $publicDisk->allDirectories();

            foreach ($directories as $directory) {
                $files = $publicDisk->allFiles($directory);
                $subdirs = $publicDisk->allDirectories($directory);

                if (empty($files) && empty($subdirs)) {
                    if ($dryRun) {
                        $this->line("Would delete empty directory: {$directory}");
                    } else {
                        if ($publicDisk->deleteDirectory($directory)) {
                            $this->line("Deleted empty directory: {$directory}");
                        }
                    }
                    $count++;
                }
            }
        } catch (\Exception $e) {
            $this->error('Error cleaning empty directories: ' . $e->getMessage());
        }

        return ['count' => $count, 'size' => 0];
    }

    /**
     * Show disk usage information.
     */
    private function showDiskUsage(): void
    {
        $this->newLine();
        $this->info('Current Disk Usage:');

        $disks = ['public', 'local'];

        foreach ($disks as $diskName) {
            $usage = StorageService::getDiskUsage($diskName);
            
            $this->table(
                ['Disk', 'Total', 'Used', 'Free', 'Used %'],
                [[
                    $diskName,
                    $usage['total_formatted'],
                    $usage['used_formatted'],
                    $usage['free_formatted'],
                    $usage['used_percentage'] . '%'
                ]]
            );
        }
    }
}
