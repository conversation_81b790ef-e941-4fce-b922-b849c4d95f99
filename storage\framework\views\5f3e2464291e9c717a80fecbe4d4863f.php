<?php $__env->startSection('title', 'Booking Details'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/via-stops.css')); ?>">
<style>
    .content-wrapper {
        padding: 20px;
    }

    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: calc(100vh - 76px);
        padding-top: 20px;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 10px 20px;
        margin-bottom: 5px;
        border-radius: 5px;
    }

    .sidebar .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
        color: #fff;
        background-color: #ee393d;
    }

    .sidebar .nav-link i {
        margin-right: 10px;
    }

    .booking-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .booking-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .booking-card .card-body {
        padding: 30px;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-confirmed {
        background-color: #d4edda;
        color: #155724;
    }

    .status-assigned {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-in-progress {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #c3e6cb;
        color: #155724;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .detail-section {
        margin-bottom: 30px;
    }

    .detail-section h5 {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
    }

    .detail-label {
        width: 150px;
        font-weight: 600;
    }

    .detail-value {
        flex: 1;
    }

    .vehicle-img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .driver-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .driver-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 15px;
    }

    .driver-details {
        flex: 1;
    }

    .driver-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .driver-contact {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .review-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .review-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .star-rating {
        color: #ee393d;
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .review-content {
        border-left: 3px solid #ee393d;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .review-date {
        font-style: italic;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
        margin-top: 20px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #dee2e6;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }

    .timeline-item:last-child {
        padding-bottom: 0;
    }

    .timeline-icon {
        position: absolute;
        left: -30px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #dee2e6;
        text-align: center;
        line-height: 18px;
        font-size: 0.7rem;
        color: #6c757d;
    }

    .timeline-icon.active {
        background-color: #ee393d;
        border-color: #ee393d;
        color: #fff;
    }

    .timeline-content {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
    }

    .timeline-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .timeline-date {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .timeline-description {
        font-size: 0.9rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Booking Details</h2>
                <a href="<?php echo e(route('client.bookings.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Bookings
                </a>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card booking-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Booking #<?php echo e($booking->booking_number); ?></h4>
                    <span class="status-badge status-<?php echo e(strtolower(str_replace('_', '-', $booking->status))); ?>">
                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-section">
                                <h5>Booking Information</h5>
                                <div class="detail-row">
                                    <div class="detail-label">Booking Type:</div>
                                    <div class="detail-value"><?php echo e(ucfirst($booking->booking_type)); ?></div>
                                </div>

                                
                                <?php if($booking->hasFlightDetails()): ?>
                                    <div class="detail-section mt-3 pt-3" style="border-top: 1px solid #dee2e6;">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-plane me-2"></i>Flight Information
                                        </h6>
                                        <?php if($booking->flight_number): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Flight Number:</div>
                                                <div class="detail-value"><?php echo e($booking->flight_number); ?></div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->airline): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Airline:</div>
                                                <div class="detail-value"><?php echo e($booking->airline); ?></div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->departure_time): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Departure:</div>
                                                <div class="detail-value"><?php echo e($booking->departure_time->format('M d, Y h:i A')); ?></div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->arrival_time): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Arrival:</div>
                                                <div class="detail-value"><?php echo e($booking->arrival_time->format('M d, Y h:i A')); ?></div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->terminal): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Terminal:</div>
                                                <div class="detail-value"><?php echo e($booking->terminal); ?></div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->flight_status): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Flight Status:</div>
                                                <div class="detail-value">
                                                    <span class="badge <?php echo e($booking->flight_status_badge_class); ?>">
                                                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->flight_status))); ?>

                                                    </span>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($booking->flight_notes): ?>
                                            <div class="detail-row">
                                                <div class="detail-label">Flight Notes:</div>
                                                <div class="detail-value"><?php echo e($booking->flight_notes); ?></div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="detail-row">
                                    <div class="detail-label">Pickup Address:</div>
                                    <div class="detail-value"><?php echo e($booking->pickup_address); ?></div>
                                </div>

                                <?php if($booking->via_stops && count($booking->via_stops) > 0): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Via Stops:</div>
                                        <div class="detail-value">
                                            <div class="via-stops-list">
                                                <?php $__currentLoopData = $booking->via_stops; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $viaStop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="via-stop-item d-flex align-items-center mb-2">
                                                        <span class="badge bg-primary me-2"><?php echo e($index + 1); ?></span>
                                                        <span><?php echo e($viaStop['address'] ?? 'Address not specified'); ?></span>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                            <?php if($booking->via_charges && $booking->via_charges > 0): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        Additional charges: <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->via_charges, 2)); ?>

                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if($booking->booking_type !== 'hourly'): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Dropoff Address:</div>
                                        <div class="detail-value"><?php echo e($booking->dropoff_address); ?></div>
                                    </div>
                                <?php endif; ?>
                                <div class="detail-row">
                                    <div class="detail-label">Pickup Date:</div>
                                    <div class="detail-value"><?php echo e($booking->pickup_date->format('M d, Y h:i A')); ?></div>
                                </div>
                                <?php if($booking->booking_type === 'return' && $booking->return_date): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Return Date:</div>
                                        <div class="detail-value"><?php echo e($booking->return_date->format('M d, Y h:i A')); ?></div>
                                    </div>
                                <?php endif; ?>
                                <?php if($booking->booking_type === 'hourly' && $booking->duration_hours): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Duration:</div>
                                        <div class="detail-value"><?php echo e($booking->duration_hours); ?> <?php echo e($booking->duration_hours > 1 ? 'hours' : 'hour'); ?></div>
                                    </div>
                                <?php endif; ?>
                                <div class="detail-row">
                                    <div class="detail-label">Created On:</div>
                                    <div class="detail-value"><?php echo e($booking->created_at->format('M d, Y h:i A')); ?></div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5>Payment Information</h5>
                                <?php if($booking->payment): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Payment Method:</div>
                                        <div class="detail-value"><?php echo e(ucfirst($booking->payment->payment_method)); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Transaction ID:</div>
                                        <div class="detail-value"><?php echo e($booking->payment->transaction_id); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Amount:</div>
                                        <div class="detail-value"><?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($booking->payment->amount, 2)); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Status:</div>
                                        <div class="detail-value"><?php echo e(ucfirst($booking->payment->status)); ?></div>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No payment information available.</p>
                                <?php endif; ?>
                            </div>

                            <!-- Extra Services Section -->
                            <?php
                                $hasExtraServices = $booking->meet_and_greet || $booking->child_seat || $booking->wheelchair_accessible || $booking->extra_luggage;
                                $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();
                            ?>

                            <?php if($hasExtraServices): ?>
                                <div class="detail-section">
                                    <h5><i class="fas fa-plus-circle me-2 text-primary"></i>Extra Services</h5>

                                    <?php if($booking->meet_and_greet): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">
                                                <i class="fas fa-handshake me-1 text-info"></i>Meet & Greet Service
                                            </div>
                                            <div class="detail-value">
                                                <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($extraServicesSettings['meet_and_greet']['fee'], 2)); ?>

                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($booking->child_seat): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">
                                                <i class="fas fa-baby me-1 text-warning"></i>Child Seat
                                            </div>
                                            <div class="detail-value">
                                                <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($extraServicesSettings['child_seat']['fee'], 2)); ?>

                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($booking->wheelchair_accessible): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">
                                                <i class="fas fa-wheelchair me-1 text-success"></i>Wheelchair Accessible
                                            </div>
                                            <div class="detail-value">
                                                <?php if($extraServicesSettings['wheelchair_accessible']['fee'] > 0): ?>
                                                    <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2)); ?>

                                                <?php else: ?>
                                                    <span class="badge bg-success">Free</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($booking->extra_luggage): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">
                                                <i class="fas fa-suitcase me-1 text-secondary"></i>Extra Luggage Space
                                            </div>
                                            <div class="detail-value">
                                                <?php echo \App\Services\SettingsService::getCurrencySymbol(); ?><?php echo e(number_format($extraServicesSettings['extra_luggage']['fee'], 2)); ?>

                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <?php if($booking->booking_type !== 'hourly'): ?>
                                <div class="detail-section">
                                    <h5>Route Map</h5>
                                    <div id="map" style="height: 300px; width: 100%; border-radius: 8px; margin-bottom: 20px;"></div>
                                </div>
                            <?php endif; ?>

                            <div class="detail-section">
                                <h5>Vehicle Information</h5>
                                <?php if($booking->vehicle->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $booking->vehicle->image)); ?>" class="vehicle-img" alt="<?php echo e($booking->vehicle->name); ?>">
                                <?php else: ?>
                                    <img src="https://via.placeholder.com/400x200?text=Vehicle" class="vehicle-img" alt="<?php echo e($booking->vehicle->name); ?>">
                                <?php endif; ?>
                                <div class="detail-row">
                                    <div class="detail-label">Vehicle:</div>
                                    <div class="detail-value"><?php echo e($booking->vehicle->name); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Type:</div>
                                    <div class="detail-value"><?php echo e($booking->vehicle->type); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Model:</div>
                                    <div class="detail-value"><?php echo e($booking->vehicle->model); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Seats:</div>
                                    <div class="detail-value"><?php echo e($booking->vehicle->seats); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Luggage Capacity:</div>
                                    <div class="detail-value"><?php echo e($booking->vehicle->luggage_capacity); ?></div>
                                </div>
                            </div>

                            <?php if($booking->driver): ?>
                                <div class="detail-section">
                                    <h5>Driver Information</h5>
                                    <div class="driver-info">
                                        <?php if($booking->driver->profile_photo): ?>
                                            <img src="<?php echo e(asset('storage/' . $booking->driver->profile_photo)); ?>" class="driver-img" alt="Driver">
                                        <?php else: ?>
                                            <img src="https://via.placeholder.com/60x60?text=Driver" class="driver-img" alt="Driver">
                                        <?php endif; ?>
                                        <div class="driver-details">
                                            <div class="driver-name">Driver #<?php echo e(substr($booking->driver->id, 0, 5)); ?></div>
                                            <div class="driver-contact">
                                                <span class="badge bg-info">Assigned to your ride</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($booking->rating): ?>
                                <div class="review-section">
                                    <h5><i class="fas fa-star me-2 text-warning"></i> Your Review</h5>
                                    <div class="star-rating">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo e($i <= $booking->rating ? '' : '-o'); ?>"></i>
                                        <?php endfor; ?>
                                        <span class="ms-2">(<?php echo e($booking->rating); ?>/5)</span>
                                    </div>
                                    <div class="review-date text-muted mb-2">
                                        <small>Reviewed on <?php echo e($booking->reviewed_at ? $booking->reviewed_at->format('M d, Y') : 'N/A'); ?></small>
                                    </div>
                                    <div class="review-content p-3 bg-white rounded">
                                        <p class="mb-0"><?php echo e($booking->review); ?></p>
                                    </div>
                                </div>
                            <?php elseif($booking->status === 'completed'): ?>
                                <div class="review-section text-center">
                                    <h5><i class="fas fa-star me-2 text-warning"></i> Leave a Review</h5>
                                    <p>Share your experience with this ride to help us improve our service.</p>
                                    <a href="<?php echo e(route('client.bookings.review', $booking->id)); ?>" class="btn btn-warning">
                                        <i class="fas fa-star me-1"></i> Write a Review
                                    </a>
                                </div>
                            <?php endif; ?>

                            <div class="detail-section">
                                <h5>Booking History</h5>
                                <div class="timeline">
                                    <?php $__empty_1 = true; $__currentLoopData = $booking->history; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $history): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <div class="timeline-item">
                                            <div class="timeline-icon active">
                                                <?php if($history->action === 'booking_created'): ?>
                                                    <i class="fas fa-plus"></i>
                                                <?php elseif($history->action === 'status_changed'): ?>
                                                    <i class="fas fa-sync-alt"></i>
                                                <?php elseif($history->action === 'payment_completed'): ?>
                                                    <i class="fas fa-credit-card"></i>
                                                <?php elseif($history->action === 'driver_assigned'): ?>
                                                    <i class="fas fa-user"></i>
                                                <?php elseif($history->action === 'cancelled'): ?>
                                                    <i class="fas fa-times"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-circle"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="timeline-content">
                                                <div class="timeline-title">
                                                    <?php if($history->action === 'booking_created'): ?>
                                                        Booking Created
                                                    <?php elseif($history->action === 'status_changed'): ?>
                                                        Status Changed to <?php echo e(ucfirst($history->status_after)); ?>

                                                    <?php elseif($history->action === 'payment_completed'): ?>
                                                        Payment Completed
                                                    <?php elseif($history->action === 'driver_assigned'): ?>
                                                        Driver Assigned
                                                    <?php elseif($history->action === 'cancelled'): ?>
                                                        Booking Cancelled
                                                    <?php else: ?>
                                                        <?php echo e(ucfirst(str_replace('_', ' ', $history->action))); ?>

                                                    <?php endif; ?>
                                                </div>
                                                <div class="timeline-date"><?php echo e($history->created_at->format('M d, Y h:i A')); ?></div>
                                                <div class="timeline-description">
                                                    <?php if($history->action === 'booking_created'): ?>
                                                        Your booking was successfully created.
                                                    <?php elseif($history->action === 'status_changed'): ?>
                                                        Booking status was changed from <?php echo e(ucfirst($history->status_before)); ?> to <?php echo e(ucfirst($history->status_after)); ?>.
                                                    <?php elseif($history->action === 'payment_completed'): ?>
                                                        Payment of <?php echo e(isset($history->details['amount']) ? \App\Services\SettingsService::getCurrencySymbol().number_format($history->details['amount'], 2) : 'N/A'); ?> was completed successfully.
                                                    <?php elseif($history->action === 'driver_assigned'): ?>
                                                        A driver has been assigned to your booking.
                                                    <?php elseif($history->action === 'cancelled'): ?>
                                                        Booking was cancelled. <?php echo e(isset($history->details['reason']) ? 'Reason: ' . $history->details['reason'] : ''); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <p class="text-muted">No history available for this booking.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <?php if(in_array($booking->status, ['pending', 'confirmed'])): ?>
                            <form action="<?php echo e(route('client.bookings.cancel', $booking->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to cancel this booking?');">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-danger me-2">Cancel Booking</button>
                            </form>
                        <?php endif; ?>

                        <?php if($booking->status === 'completed' && !$booking->review): ?>
                            <a href="<?php echo e(route('client.bookings.review', $booking->id)); ?>" class="btn btn-primary">Leave Review</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php if($booking->booking_type !== 'hourly'): ?>
<!-- Google Maps JavaScript API -->
<?php
    $googleMapsApiKey = \App\Services\SettingsService::getGoogleMapsApiKey();
?>
<?php if($googleMapsApiKey): ?>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&callback=initMap" async defer></script>
<?php else: ?>
<script>
    console.error('Google Maps API key is not configured. Please set it in the admin settings.');
</script>
<?php endif; ?>
<script>
    function initMap() {
        <?php if($booking->pickup_lat && $booking->pickup_lng && $booking->dropoff_lat && $booking->dropoff_lng): ?>
            // Create map
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> }
            });

            // Create markers for pickup and dropoff
            const pickupMarker = new google.maps.Marker({
                position: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> },
                map: map,
                title: 'Pickup Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
                }
            });

            const dropoffMarker = new google.maps.Marker({
                position: { lat: <?php echo e($booking->dropoff_lat); ?>, lng: <?php echo e($booking->dropoff_lng); ?> },
                map: map,
                title: 'Dropoff Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
                }
            });

            // Create route
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            // Prepare waypoints for via stops
            const waypoints = [];
            <?php if($booking->via_stops && count($booking->via_stops) > 0): ?>
                <?php $__currentLoopData = $booking->via_stops; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $viaStop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($viaStop['lat']) && isset($viaStop['lng']) && $viaStop['lat'] && $viaStop['lng']): ?>
                        waypoints.push({
                            location: { lat: <?php echo e($viaStop['lat']); ?>, lng: <?php echo e($viaStop['lng']); ?> },
                            stopover: true
                        });
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Get directions
            directionsService.route({
                origin: { lat: <?php echo e($booking->pickup_lat); ?>, lng: <?php echo e($booking->pickup_lng); ?> },
                destination: { lat: <?php echo e($booking->dropoff_lat); ?>, lng: <?php echo e($booking->dropoff_lng); ?> },
                waypoints: waypoints,
                optimizeWaypoints: false,
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);

                    // Add info windows
                    const pickupInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Pickup:</strong> <?php echo e($booking->pickup_address); ?></div>'
                    });

                    const dropoffInfoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Dropoff:</strong> <?php echo e($booking->dropoff_address); ?></div>'
                    });

                    pickupMarker.addListener('click', function() {
                        pickupInfoWindow.open(map, pickupMarker);
                    });

                    dropoffMarker.addListener('click', function() {
                        dropoffInfoWindow.open(map, dropoffMarker);
                    });

                    // Open pickup info window by default
                    pickupInfoWindow.open(map, pickupMarker);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        <?php else: ?>
            // If we don't have coordinates, use the addresses
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: 51.5074, lng: -0.1278 } // Default to London
            });

            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                map: map,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });

            directionsService.route({
                origin: '<?php echo e($booking->pickup_address); ?>',
                destination: '<?php echo e($booking->dropoff_address); ?>',
                travelMode: google.maps.TravelMode.DRIVING
            }, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                } else {
                    console.error('Directions request failed due to ' + status);
                }
            });
        <?php endif; ?>
    }
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.client', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/client/bookings/show.blade.php ENDPATH**/ ?>