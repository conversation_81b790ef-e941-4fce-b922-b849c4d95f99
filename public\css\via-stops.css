/* Via Stops Styling */
.via-stops-card {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.via-stop-item {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
    transition: all 0.3s ease;
}

.via-stop-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.via-stop-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
    border-radius: 4px;
    padding: 4px;
    margin: -4px;
}

.via-stop-number .badge {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.via-stop-details {
    flex-grow: 1;
}

.via-stops-list {
    max-height: 300px;
    overflow-y: auto;
}

/* Via stops in booking history */
.booking-location .via-stop-badge {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

/* Via stops in admin tables */
.admin-via-stops-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 8px;
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
    border: none;
}

/* Via stops in driver views */
.driver-via-stops .badge {
    margin-right: 5px;
    margin-bottom: 2px;
}

/* Via stops in email templates */
.email-via-stops {
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 10px;
    margin: 10px 0;
}

.email-via-stops .via-stop-number {
    display: inline-block;
    background: #007bff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    margin-right: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    .via-stop-item {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .via-stop-number {
        margin-bottom: 5px;
    }
    
    .via-stops-list {
        max-height: 200px;
    }
}

/* Animation for via stops */
@keyframes fadeInViaStop {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.via-stop-item {
    animation: fadeInViaStop 0.3s ease-in-out;
}

/* Print styles */
@media print {
    .via-stops-card {
        border-left: 2px solid #000;
        background: none !important;
    }
    
    .via-stop-number .badge {
        background: #000 !important;
        color: white !important;
    }
}
