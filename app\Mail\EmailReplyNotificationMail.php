<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Services\SettingsService;

class EmailReplyNotificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $emailData;
    public $relatedSubmission;
    public $companyName;

    /**
     * Create a new message instance.
     */
    public function __construct(array $emailData, $relatedSubmission = null)
    {
        $this->emailData = $emailData;
        $this->relatedSubmission = $relatedSubmission;
        $this->companyName = SettingsService::getCompanyName();
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $fromEmail = $this->emailData['from'] ?? 'Unknown';

        return new Envelope(
            subject: '📧 Email Reply Received from ' . $fromEmail,
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', '<EMAIL>'),
                $this->companyName
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.email-reply-notification',
            with: [
                'emailData' => $this->emailData,
                'relatedSubmission' => $this->relatedSubmission,
                'companyName' => $this->companyName,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
