<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 60;

    protected $mailable;
    protected $to;
    protected $emailType;

    /**
     * Create a new job instance.
     */
    public function __construct($mailable, $to, $emailType = 'general')
    {
        $this->mailable = $mailable;
        $this->to = $to;
        $this->emailType = $emailType;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Mail::to($this->to)->send($this->mailable);
            
            Log::info('Email sent successfully via queue', [
                'to' => $this->to,
                'type' => $this->emailType,
                'mailable' => get_class($this->mailable)
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send email via queue', [
                'to' => $this->to,
                'type' => $this->emailType,
                'mailable' => get_class($this->mailable),
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Email job failed after all retries', [
            'to' => $this->to,
            'type' => $this->emailType,
            'mailable' => get_class($this->mailable),
            'error' => $exception->getMessage()
        ]);
    }
}
