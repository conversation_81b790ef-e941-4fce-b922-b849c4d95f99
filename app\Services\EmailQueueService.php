<?php

namespace App\Services;

use App\Models\EmailCampaign;
use App\Models\EmailCampaignRecipient;
use App\Models\EmailLog;
use App\Jobs\SendCampaignEmailJob;
use App\Jobs\ProcessEmailCampaignJob;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class EmailQueueService
{
    /**
     * Queue a campaign for sending
     *
     * @param EmailCampaign $campaign
     * @return bool
     */
    public static function queueCampaign(EmailCampaign $campaign): bool
    {
        try {
            // Update campaign status
            $campaign->update(['status' => 'sending']);

            // Get target recipients
            $recipients = $campaign->getTargetRecipients();

            // Create recipient records
            foreach ($recipients as $recipient) {
                EmailCampaignRecipient::create([
                    'campaign_id' => $campaign->id,
                    'user_id' => $recipient->id,
                    'email' => $recipient->email,
                    'status' => 'pending',
                ]);
            }

            // Update total recipients count
            $campaign->update(['total_recipients' => $recipients->count()]);

            // Queue the campaign processing job
            ProcessEmailCampaignJob::dispatch($campaign);

            Log::info('Email campaign queued successfully', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
                'recipient_count' => $recipients->count(),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to queue email campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);

            $campaign->update(['status' => 'failed']);
            return false;
        }
    }

    /**
     * Process campaign recipients in batches
     *
     * @param EmailCampaign $campaign
     * @param int $batchSize
     * @return void
     */
    public static function processCampaignBatch(EmailCampaign $campaign, int $batchSize = 50): void
    {
        $pendingRecipients = $campaign->recipients()
            ->where('status', 'pending')
            ->limit($batchSize)
            ->get();

        foreach ($pendingRecipients as $recipient) {
            SendCampaignEmailJob::dispatch($campaign, $recipient);
        }

        // Check if campaign is complete
        $remainingCount = $campaign->recipients()
            ->where('status', 'pending')
            ->count();

        if ($remainingCount === 0) {
            $campaign->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            Log::info('Email campaign completed', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
            ]);
        }
    }

    /**
     * Get queue statistics
     *
     * @return array
     */
    public static function getQueueStatistics(): array
    {
        try {
            $stats = [
                'pending_campaigns' => EmailCampaign::where('status', 'sending')->count(),
                'pending_emails' => EmailCampaignRecipient::where('status', 'pending')->count(),
                'failed_emails_today' => EmailLog::failed()
                    ->whereDate('created_at', today())
                    ->count(),
                'sent_emails_today' => EmailLog::successful()
                    ->whereDate('created_at', today())
                    ->count(),
                'queue_size' => self::getQueueSize(),
                'failed_jobs' => self::getFailedJobsCount(),
            ];

            // Calculate processing rate (emails per hour)
            $recentEmails = EmailLog::where('created_at', '>=', now()->subHour())->count();
            $stats['processing_rate'] = $recentEmails;

            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get queue statistics', ['error' => $e->getMessage()]);
            return [
                'pending_campaigns' => 0,
                'pending_emails' => 0,
                'failed_emails_today' => 0,
                'sent_emails_today' => 0,
                'queue_size' => 0,
                'failed_jobs' => 0,
                'processing_rate' => 0,
            ];
        }
    }

    /**
     * Get current queue size
     *
     * @return int
     */
    private static function getQueueSize(): int
    {
        try {
            $connection = config('queue.default');
            
            if ($connection === 'database') {
                return DB::table('jobs')->count();
            } elseif ($connection === 'redis') {
                // For Redis, you'd need to implement Redis-specific logic
                return 0;
            }

            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get failed jobs count
     *
     * @return int
     */
    private static function getFailedJobsCount(): int
    {
        try {
            return DB::table('failed_jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Retry failed campaign emails
     *
     * @param EmailCampaign $campaign
     * @return int
     */
    public static function retryFailedEmails(EmailCampaign $campaign): int
    {
        $failedRecipients = $campaign->recipients()
            ->whereIn('status', ['failed', 'bounced'])
            ->get();

        $retryCount = 0;
        foreach ($failedRecipients as $recipient) {
            $recipient->update(['status' => 'pending']);
            SendCampaignEmailJob::dispatch($campaign, $recipient);
            $retryCount++;
        }

        if ($retryCount > 0) {
            $campaign->update(['status' => 'sending']);
        }

        Log::info('Retrying failed campaign emails', [
            'campaign_id' => $campaign->id,
            'retry_count' => $retryCount,
        ]);

        return $retryCount;
    }

    /**
     * Cancel a campaign
     *
     * @param EmailCampaign $campaign
     * @return bool
     */
    public static function cancelCampaign(EmailCampaign $campaign): bool
    {
        try {
            // Update campaign status
            $campaign->update(['status' => 'cancelled']);

            // Update pending recipients
            $campaign->recipients()
                ->where('status', 'pending')
                ->update(['status' => 'cancelled']);

            Log::info('Email campaign cancelled', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to cancel email campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Clean up old email logs
     *
     * @param int $daysToKeep
     * @return int
     */
    public static function cleanupOldLogs(int $daysToKeep = 90): int
    {
        try {
            $cutoffDate = now()->subDays($daysToKeep);
            
            $deletedCount = EmailLog::where('created_at', '<', $cutoffDate)->delete();

            Log::info('Cleaned up old email logs', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateString(),
            ]);

            return $deletedCount;
        } catch (\Exception $e) {
            Log::error('Failed to cleanup old email logs', ['error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * Get email delivery analytics
     *
     * @param int $days
     * @return array
     */
    public static function getDeliveryAnalytics(int $days = 30): array
    {
        try {
            $startDate = now()->subDays($days);

            $analytics = [
                'total_sent' => EmailLog::sent()
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'total_delivered' => EmailLog::where('status', 'delivered')
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'total_bounced' => EmailLog::where('status', 'bounced')
                    ->where('created_at', '>=', $startDate)
                    ->count(),
                'total_failed' => EmailLog::where('status', 'failed')
                    ->where('created_at', '>=', $startDate)
                    ->count(),
            ];

            // Calculate rates
            if ($analytics['total_sent'] > 0) {
                $analytics['delivery_rate'] = round(($analytics['total_delivered'] / $analytics['total_sent']) * 100, 2);
                $analytics['bounce_rate'] = round(($analytics['total_bounced'] / $analytics['total_sent']) * 100, 2);
                $analytics['failure_rate'] = round(($analytics['total_failed'] / $analytics['total_sent']) * 100, 2);
            } else {
                $analytics['delivery_rate'] = 0;
                $analytics['bounce_rate'] = 0;
                $analytics['failure_rate'] = 0;
            }

            // Get daily breakdown
            $analytics['daily_breakdown'] = EmailLog::sent()
                ->where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count, status')
                ->groupBy('date', 'status')
                ->orderBy('date')
                ->get()
                ->groupBy('date');

            return $analytics;
        } catch (\Exception $e) {
            Log::error('Failed to get delivery analytics', ['error' => $e->getMessage()]);
            return [
                'total_sent' => 0,
                'total_delivered' => 0,
                'total_bounced' => 0,
                'total_failed' => 0,
                'delivery_rate' => 0,
                'bounce_rate' => 0,
                'failure_rate' => 0,
                'daily_breakdown' => [],
            ];
        }
    }
}
