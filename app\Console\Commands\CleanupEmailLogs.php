<?php

namespace App\Console\Commands;

use App\Services\EmailQueueService;
use Illuminate\Console\Command;

class CleanupEmailLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:cleanup-logs {--days=90 : Number of days to keep logs} {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old email logs to free up database space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $daysToKeep = (int) $this->option('days');
        $isDryRun = $this->option('dry-run');

        $this->info("Cleaning up email logs older than {$daysToKeep} days...");

        if ($isDryRun) {
            $this->comment('[DRY RUN MODE] - No data will be deleted');
        }

        try {
            if ($isDryRun) {
                // Count what would be deleted
                $cutoffDate = now()->subDays($daysToKeep);
                $count = \App\Models\EmailLog::where('created_at', '<', $cutoffDate)->count();
                
                $this->info("Would delete {$count} email log records older than {$cutoffDate->toDateString()}");
            } else {
                // Actually delete the logs
                $deletedCount = EmailQueueService::cleanupOldLogs($daysToKeep);
                
                $this->info("Successfully deleted {$deletedCount} old email log records.");
            }
        } catch (\Exception $e) {
            $this->error("Error cleaning up email logs: {$e->getMessage()}");
            return 1;
        }

        return 0;
    }
}
