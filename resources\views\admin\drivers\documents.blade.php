@extends('layouts.admin')

@section('title', 'Driver Documents')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Documents for {{ $driver->name }}</h1>
        <div>
            <a href="{{ route('admin.drivers.show', $driver->id) }}" class="btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Driver
            </a>
            <a href="{{ route('admin.drivers.index') }}" class="btn btn-sm btn-secondary shadow-sm">
                <i class="fas fa-list fa-sm text-white-50"></i> All Drivers
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Driver Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @if($driver->profile_photo)
                            <img src="{{ asset('storage/' . $driver->profile_photo) }}" alt="{{ $driver->name }}" class="img-profile rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        @else
                            <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; color: white; font-size: 4rem;">
                                {{ strtoupper(substr($driver->name, 0, 1)) }}
                            </div>
                        @endif
                        <h4 class="mt-3">{{ $driver->name }}</h4>
                        <div class="mb-2">
                            <span class="badge badge-{{ $driver->is_active ? 'success' : 'danger' }} p-2">
                                {{ $driver->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <span class="badge badge-{{ $driver->is_available ? 'info' : 'warning' }} p-2">
                                {{ $driver->is_available ? 'Available' : 'Unavailable' }}
                            </span>
                        </div>
                    </div>

                    <div class="driver-info">
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <span>{{ $driver->email }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span>{{ $driver->phone }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-id-card"></i>
                            <span>License: {{ $driver->license_number }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-car"></i>
                            <span>{{ $driver->vehicle_info }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Upload New Document</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.drivers.upload-document', $driver->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group">
                            <label for="document_type">Document Type</label>
                            <select class="form-control" id="document_type" name="document_type" required>
                                <option value="Driver License">Driver License</option>
                                <option value="Driver PHD License">Driver PHD License</option>
                                <option value="Vehicle PHD License">Vehicle PHD License</option>
                                <option value="Insurance">Insurance</option>
                                <option value="MOT Certificate">MOT Certificate</option>
                                <option value="V5C Logbook">V5C Logbook</option>
                                <option value="Vehicle Photos">Vehicle Photos</option>
                                <option value="Vehicle Registration">Vehicle Registration</option>
                                <option value="Background Check">Background Check</option>
                                <option value="Medical Certificate">Medical Certificate</option>
                                <option value="Training Certificate">Training Certificate</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="document">Document File</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="document" name="document" required>
                                <label class="custom-file-label" for="document">Choose file</label>
                            </div>
                            <small class="form-text text-muted">Accepted formats: PDF, JPG, PNG. Max size: 5MB.</small>
                        </div>

                        <div class="form-group">
                            <label for="expiry_date">Expiry Date</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                            <small class="form-text text-muted">Leave blank if the document doesn't expire.</small>
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Upload Document</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">All Documents</h6>
                    <span class="badge badge-primary">{{ $documents->count() }} Documents</span>
                </div>
                <div class="card-body">
                    @if($documents->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="mb-0">No documents uploaded yet.</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Preview</th>
                                        <th>Uploaded</th>
                                        <th>Expiry</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($documents as $document)
                                        <tr>
                                            <td>{{ $document->document_type }}</td>
                                            <td class="text-center">
                                                @php
                                                    $extension = pathinfo($document->file_path, PATHINFO_EXTENSION);
                                                @endphp

                                                @if(in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif']))
                                                    <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank">
                                                        <img src="{{ asset('storage/' . $document->file_path) }}" alt="{{ $document->document_type }}" class="img-thumbnail" style="max-height: 50px;">
                                                    </a>
                                                @else
                                                    <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-file-pdf"></i> View
                                                    </a>
                                                @endif
                                            </td>
                                            <td>
                                                {{ $document->created_at->format('M d, Y') }}
                                                <div class="small text-muted">{{ $document->created_at->diffForHumans() }}</div>
                                            </td>
                                            <td>
                                                @if($document->expiry_date)
                                                    {{ $document->expiry_date->format('M d, Y') }}
                                                    @if($document->isExpired())
                                                        <div class="badge badge-danger">Expired</div>
                                                    @elseif($document->isExpiringSoon())
                                                        <div class="badge badge-warning">Expiring Soon</div>
                                                    @endif
                                                @else
                                                    <span class="text-muted">No Expiry</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $document->is_verified ? 'success' : 'warning' }} p-2">
                                                    {{ $document->is_verified ? 'Verified' : 'Pending Verification' }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ asset('storage/' . $document->file_path) }}" target="_blank" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <form action="{{ route('admin.drivers.verify-document', $document->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-{{ $document->is_verified ? 'secondary' : 'success' }}" title="{{ $document->is_verified ? 'Mark as Unverified' : 'Verify Document' }}">
                                                            <i class="fas fa-{{ $document->is_verified ? 'times' : 'check' }}"></i>
                                                        </button>
                                                    </form>
                                                    <form action="{{ route('admin.drivers.delete-document', $document->id) }}" method="POST" class="d-inline delete-document-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete Document">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>

                                                @if($document->notes)
                                                    <div class="mt-2 small">
                                                        <strong>Notes:</strong> {{ $document->notes }}
                                                    </div>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    .driver-info .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .driver-info .info-item i {
        width: 25px;
        color: #4e73df;
    }
</style>
@endsection

@section('scripts')
<script>
    // Show file name when selected
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').addClass("selected").html(fileName);
    });

    // Confirm document delete
    $('.delete-document-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);

        Swal.fire({
            title: 'Are you sure?',
            text: "This will delete the document. This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                form.off('submit').submit();
            }
        });
    });
</script>
@endsection
