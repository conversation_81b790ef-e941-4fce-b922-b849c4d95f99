<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Services\SettingsService;

class DriverEarningsSummaryMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $driver;
    public $earningsData;
    public $period;

    /**
     * Create a new message instance.
     */
    public function __construct(User $driver, array $earningsData, string $period = 'weekly')
    {
        $this->driver = $driver;
        $this->earningsData = $earningsData;
        $this->period = $period;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $periodTitle = ucfirst($this->period);
        return new Envelope(
            subject: $periodTitle . ' Earnings Summary - ' . SettingsService::getCompanyName(),
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.driver-earnings-summary',
            with: [
                'driver' => $this->driver,
                'earningsData' => $this->earningsData,
                'period' => $this->period,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
