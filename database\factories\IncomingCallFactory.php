<?php

namespace Database\Factories;

use App\Models\IncomingCall;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IncomingCall>
 */
class IncomingCallFactory extends Factory
{
    protected $model = IncomingCall::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $phoneNumbers = [
            '07123456789',
            '07987654321',
            '07555123456',
            '07444987654',
            '07333555777',
            '07222444666',
            '07111333555',
            '07999888777',
            '07888777666',
            '07777666555',
        ];

        $eventTypes = ['call_ringing', 'call_missed', 'call_answered'];
        $statuses = ['ringing', 'answered', 'missed', 'handled'];

        return [
            'call_id' => 'CL-' . $this->faker->unique()->randomNumber(8),
            'user_id' => 'user' . $this->faker->randomNumber(3),
            'event_type' => $this->faker->randomElement($eventTypes),
            'caller_number' => $this->faker->randomElement($phoneNumbers),
            'caller_number_e164' => '+44' . substr($this->faker->randomElement($phoneNumbers), 1),
            'dialled_number' => '02012345678',
            'client_id' => $this->faker->optional(0.7)->randomElement([
                User::factory(),
                null
            ]),
            'client_name' => $this->faker->optional()->name(),
            'client_email' => $this->faker->optional()->safeEmail(),
            'status' => $this->faker->randomElement($statuses),
            'notes' => $this->faker->optional()->sentence(),
            'handled_by' => $this->faker->optional(0.3)->randomElement([
                User::factory(),
                null
            ]),
            'handled_at' => $this->faker->optional(0.3)->dateTimeBetween('-1 week', 'now'),
            'follow_up_required' => $this->faker->boolean(20), // 20% chance
            'follow_up_at' => $this->faker->optional(0.2)->dateTimeBetween('now', '+1 week'),
            'metadata' => [
                'duration' => $this->faker->optional()->numberBetween(10, 300),
                'quality' => $this->faker->optional()->randomElement(['excellent', 'good', 'fair', 'poor']),
                'recording_url' => $this->faker->optional()->url(),
                'ip_address' => $this->faker->ipv4(),
                'user_agent' => $this->faker->userAgent(),
            ],
        ];
    }

    /**
     * Indicate that the call is ringing.
     */
    public function ringing(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'call_ringing',
            'status' => 'ringing',
            'handled_by' => null,
            'handled_at' => null,
        ]);
    }

    /**
     * Indicate that the call was answered.
     */
    public function answered(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'call_answered',
            'status' => 'answered',
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'duration' => $this->faker->numberBetween(30, 600),
                'quality' => $this->faker->randomElement(['excellent', 'good', 'fair']),
            ]),
        ]);
    }

    /**
     * Indicate that the call was missed.
     */
    public function missed(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'call_missed',
            'status' => 'missed',
            'handled_by' => null,
            'handled_at' => null,
            'follow_up_required' => true,
            'follow_up_at' => $this->faker->dateTimeBetween('now', '+1 day'),
        ]);
    }

    /**
     * Indicate that the call has been handled.
     */
    public function handled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'handled',
            'handled_by' => User::factory(),
            'handled_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the call requires follow-up.
     */
    public function requiresFollowUp(): static
    {
        return $this->state(fn (array $attributes) => [
            'follow_up_required' => true,
            'follow_up_at' => $this->faker->dateTimeBetween('now', '+3 days'),
        ]);
    }

    /**
     * Indicate that the caller is identified (has client_id).
     */
    public function identified(): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => User::factory(),
            'client_name' => $this->faker->name(),
            'client_email' => $this->faker->safeEmail(),
        ]);
    }

    /**
     * Indicate that the caller is unknown (no client_id).
     */
    public function unknown(): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => null,
            'client_name' => null,
            'client_email' => null,
        ]);
    }

    /**
     * Create a call from today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('today', 'now'),
        ]);
    }

    /**
     * Create a call from this week.
     */
    public function thisWeek(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('monday this week', 'now'),
        ]);
    }

    /**
     * Create a recent call (within last 24 hours).
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-24 hours', 'now'),
        ]);
    }

    /**
     * Create a call with specific phone number.
     */
    public function withPhone(string $phoneNumber): static
    {
        return $this->state(fn (array $attributes) => [
            'caller_number' => $phoneNumber,
            'caller_number_e164' => '+44' . substr($phoneNumber, 1),
        ]);
    }

    /**
     * Create a call with specific client.
     */
    public function forClient(User $client): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => $client->id,
            'client_name' => $client->name,
            'client_email' => $client->email,
            'caller_number' => $client->phone ?? $this->faker->phoneNumber(),
        ]);
    }

    /**
     * Create a call with recording.
     */
    public function withRecording(): static
    {
        return $this->state(fn (array $attributes) => [
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'recording_url' => 'https://recordings.example.com/' . $this->faker->uuid() . '.mp3',
                'recording_duration' => $this->faker->numberBetween(30, 600),
                'recording_size' => $this->faker->numberBetween(500000, 5000000), // bytes
            ]),
        ]);
    }

    /**
     * Create a high priority call.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'follow_up_required' => true,
            'follow_up_at' => now()->addHours(2),
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'priority' => 'high',
                'urgency' => 'immediate',
            ]),
        ]);
    }
}
