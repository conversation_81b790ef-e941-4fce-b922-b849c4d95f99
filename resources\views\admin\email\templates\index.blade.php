@extends('layouts.admin')

@section('title', 'Email Templates')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📄 Email Templates</h1>
            <p class="text-muted">Create and manage email templates for automated communications</p>
        </div>
        <div>
            <a href="{{ route('admin.email.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Email
            </a>
            <a href="{{ route('admin.email.templates.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> New Template
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.email.templates.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="type" class="form-label">Type</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">All Types</option>
                        @foreach($types as $type)
                            <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                {{ ucfirst($type) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                {{ ucfirst($category) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Search templates...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Templates List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Email Templates ({{ $templates->total() }})</h6>
        </div>
        <div class="card-body">
            @if($templates->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Category</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>System</th>
                                <th>Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($templates as $template)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <div class="fw-bold">{{ $template->name }}</div>
                                                @if($template->description)
                                                    <div class="text-muted small">{{ Str::limit($template->description, 50) }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge {{ $template->type_badge_class }}">
                                            {{ ucfirst($template->type) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($template->category)
                                            <span class="badge bg-secondary">{{ ucfirst($template->category) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ $template->subject }}">
                                            {{ $template->subject }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge {{ $template->status_badge_class }}">
                                            {{ $template->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($template->is_system)
                                            <i class="fas fa-lock text-warning" title="System Template"></i>
                                        @else
                                            <i class="fas fa-user text-info" title="Custom Template"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $template->updated_at->format('M j, Y') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.email.templates.show', $template) }}" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.email.templates.preview', $template) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Preview" target="_blank">
                                                <i class="fas fa-search"></i>
                                            </a>
                                            <a href="{{ route('admin.email.templates.edit', $template) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                    onclick="toggleTemplateStatus({{ $template->id }})" title="Toggle Status">
                                                <i class="fas fa-power-off"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="duplicateTemplate({{ $template->id }})" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            @if(!$template->is_system)
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteTemplate({{ $template->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing {{ $templates->firstItem() }} to {{ $templates->lastItem() }} of {{ $templates->total() }} results
                    </div>
                    {{ $templates->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Email Templates Found</h5>
                    <p class="text-muted">Create your first email template to get started.</p>
                    <a href="{{ route('admin.email.templates.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create Template
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this email template? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Template</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let templateToDelete = null;

function toggleTemplateStatus(templateId) {
    $.ajax({
        url: `/admin/email/templates/${templateId}/toggle-status`,
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('Error toggling template status');
        }
    });
}

function duplicateTemplate(templateId) {
    if (confirm('Are you sure you want to duplicate this template?')) {
        $.ajax({
            url: `/admin/email/templates/${templateId}/duplicate`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('Error duplicating template');
            }
        });
    }
}

function deleteTemplate(templateId) {
    templateToDelete = templateId;
    $('#deleteModal').modal('show');
}

$('#confirmDelete').click(function() {
    if (templateToDelete) {
        const form = $('<form>', {
            method: 'POST',
            action: `/admin/email/templates/${templateToDelete}`
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_method',
            value: 'DELETE'
        }));
        
        $('body').append(form);
        form.submit();
    }
});
</script>
@endpush
