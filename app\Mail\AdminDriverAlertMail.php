<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Services\SettingsService;

class AdminDriverAlertMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $alertType;
    public $driverData;
    public $alertData;

    /**
     * Create a new message instance.
     */
    public function __construct(string $alertType, array $driverData, array $alertData = [])
    {
        $this->alertType = $alertType;
        $this->driverData = $driverData;
        $this->alertData = $alertData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $alertTitles = [
            'new_registration' => '👋 New Driver Registration',
            'document_expiry' => '⚠️ Driver Document Expiry Alert',
            'performance_issue' => '📊 Driver Performance Alert',
            'compliance_violation' => '🚨 Driver Compliance Violation',
            'account_suspended' => '🔒 Driver Account Suspended',
            'high_earnings' => '🌟 Driver High Performance',
        ];

        $title = $alertTitles[$this->alertType] ?? '📋 Driver Management Alert';

        return new Envelope(
            subject: $title . ' - ' . SettingsService::getCompanyName(),
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.admin-driver-alert',
            with: [
                'alertType' => $this->alertType,
                'driverData' => $this->driverData,
                'alertData' => $this->alertData,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
