@extends('layouts.admin')

@section('title', 'Edit Email Template')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📄 Edit Email Template</h1>
            <p class="text-muted">Edit "{{ $template->name }}" template</p>
        </div>
        <div>
            <a href="{{ route('admin.email.templates.show', $template) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i> View
            </a>
            <a href="{{ route('admin.email.templates.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Templates
            </a>
        </div>
    </div>

    @if($template->is_system)
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>System Template:</strong> This is a system template. Changes should be made carefully as they affect automated emails.
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <!-- Template Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Template Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.email.templates.update', $template) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Template Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $template->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select Type</option>
                                        <option value="booking" {{ old('type', $template->type) == 'booking' ? 'selected' : '' }}>Booking</option>
                                        <option value="payment" {{ old('type', $template->type) == 'payment' ? 'selected' : '' }}>Payment</option>
                                        <option value="notification" {{ old('type', $template->type) == 'notification' ? 'selected' : '' }}>Notification</option>
                                        <option value="marketing" {{ old('type', $template->type) == 'marketing' ? 'selected' : '' }}>Marketing</option>
                                        <option value="system" {{ old('type', $template->type) == 'system' ? 'selected' : '' }}>System</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="client" {{ old('category', $template->category) == 'client' ? 'selected' : '' }}>Client</option>
                                        <option value="driver" {{ old('category', $template->category) == 'driver' ? 'selected' : '' }}>Driver</option>
                                        <option value="admin" {{ old('category', $template->category) == 'admin' ? 'selected' : '' }}>Admin</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                   id="subject" name="subject" value="{{ old('subject', $template->subject) }}" required
                                   placeholder="e.g., Booking Confirmation - #{booking_number}">
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="2" 
                                      placeholder="Brief description of when this template is used">{{ old('description', $template->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Email Content <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="12" required 
                                      placeholder="Enter your email content here. Use variables like {client_name}, {booking_number}, etc.">{{ old('content', $template->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="variables" class="form-label">Available Variables</label>
                            <div id="variablesContainer">
                                @if(old('variables', $template->variables))
                                    @foreach(old('variables', $template->variables) as $variable)
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="variables[]" value="{{ $variable }}">
                                            <button type="button" class="btn btn-outline-danger" onclick="removeVariable(this)">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                @endif
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" name="variables[]" placeholder="Variable name (e.g., client_name)">
                                    <button type="button" class="btn btn-outline-secondary" onclick="addVariable()">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                Add variables that can be used in the template content. Use format: {variable_name}
                            </small>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ old('is_active', $template->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active Template
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Template
                            </button>
                            <div>
                                <button type="button" class="btn btn-outline-info me-2" onclick="previewTemplate()">
                                    <i class="fas fa-eye me-1"></i> Preview
                                </button>
                                <a href="{{ route('admin.email.templates.preview', $template) }}" 
                                   class="btn btn-outline-success" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i> Full Preview
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Template Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📊 Template Info</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h5 mb-0">{{ $template->is_system ? 'System' : 'Custom' }}</div>
                                <small class="text-muted">Type</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0">
                                <span class="badge {{ $template->status_badge_class }}">
                                    {{ $template->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                    <hr>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Created:</span>
                            <span>{{ $template->created_at->format('M j, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Updated:</span>
                            <span>{{ $template->updated_at->format('M j, Y') }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Slug:</span>
                            <span class="text-muted">{{ $template->slug }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Variable Helper -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">💡 Variable Helper</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Current Variables:</h6>
                    @if($template->variables && count($template->variables) > 0)
                        <div class="d-flex flex-wrap gap-1 mb-3">
                            @foreach($template->variables as $variable)
                                <span class="badge bg-primary variable-badge" onclick="insertVariable('{{ $variable }}')">{{{ $variable }}}</span>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted small">No variables defined yet.</p>
                    @endif

                    <h6 class="text-primary">Common Variables:</h6>
                    <div class="mb-3">
                        <h6 class="small text-muted">User Variables:</h6>
                        <div class="d-flex flex-wrap gap-1 mb-2">
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('client_name')">{client_name}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('driver_name')">{driver_name}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('user_email')">{user_email}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="small text-muted">Booking Variables:</h6>
                        <div class="d-flex flex-wrap gap-1 mb-2">
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('booking_number')">{booking_number}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('pickup_date')">{pickup_date}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('pickup_address')">{pickup_address}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('dropoff_address')">{dropoff_address}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('amount')">{amount}</span>
                        </div>
                    </div>

                    <small class="text-muted">Click on any variable to insert it into the content.</small>
                </div>
            </div>

            <!-- Template Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">⚡ Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="duplicateTemplate()">
                            <i class="fas fa-copy me-1"></i> Duplicate Template
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleStatus()">
                            <i class="fas fa-power-off me-1"></i> Toggle Status
                        </button>
                        @if(!$template->is_system)
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteTemplate()">
                                <i class="fas fa-trash me-1"></i> Delete Template
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addVariable() {
    const container = document.getElementById('variablesContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="variables[]" placeholder="Variable name">
        <button type="button" class="btn btn-outline-danger" onclick="removeVariable(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeVariable(button) {
    button.closest('.input-group').remove();
}

function insertVariable(variable) {
    const content = document.getElementById('content');
    const cursorPos = content.selectionStart;
    const textBefore = content.value.substring(0, cursorPos);
    const textAfter = content.value.substring(cursorPos);
    
    content.value = textBefore + '{' + variable + '}' + textAfter;
    content.focus();
    content.setSelectionRange(cursorPos + variable.length + 2, cursorPos + variable.length + 2);
}

function previewTemplate() {
    // Same preview function as in create template
    const formData = {
        name: $('#name').val(),
        subject: $('#subject').val(),
        content: $('#content').val(),
        type: $('#type').val(),
        category: $('#category').val()
    };

    if (!formData.subject || !formData.content) {
        alert('Please fill in the subject and content fields to preview.');
        return;
    }

    // Create preview with sample data (same as create template)
    let previewSubject = formData.subject;
    let previewContent = formData.content;
    
    const sampleData = {
        client_name: 'John Doe',
        driver_name: 'Mike Johnson',
        booking_number: 'YNR20240101001',
        pickup_date: '2024-01-15 10:00 AM',
        pickup_address: '123 Main Street, London',
        dropoff_address: 'Heathrow Airport',
        amount: '£45.00',
        company_name: 'YNR Cars',
        company_email: '<EMAIL>',
        company_phone: '+44 ************'
    };

    Object.keys(sampleData).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        previewSubject = previewSubject.replace(regex, sampleData[key]);
        previewContent = previewContent.replace(regex, sampleData[key]);
    });

    previewContent = previewContent.replace(/\n/g, '<br>');

    const previewHtml = `
        <div class="border rounded p-3" style="background-color: #f8f9fa;">
            <h6 class="text-primary">Subject:</h6>
            <p class="fw-bold">${previewSubject}</p>
            
            <h6 class="text-primary">Content:</h6>
            <div class="border rounded p-3 bg-white">
                ${previewContent}
            </div>
            
            <small class="text-muted mt-2 d-block">
                <strong>Note:</strong> This preview uses sample data. Actual emails will use real data.
            </small>
        </div>
    `;

    $('#previewContent').html(previewHtml);
    $('#previewModal').modal('show');
}

function duplicateTemplate() {
    if (confirm('Are you sure you want to duplicate this template?')) {
        $.post('{{ route("admin.email.templates.duplicate", $template) }}', {
            _token: '{{ csrf_token() }}'
        }).done(function() {
            window.location.href = '{{ route("admin.email.templates.index") }}';
        }).fail(function() {
            alert('Error duplicating template');
        });
    }
}

function toggleStatus() {
    $.post('{{ route("admin.email.templates.toggle-status", $template) }}', {
        _token: '{{ csrf_token() }}'
    }).done(function() {
        location.reload();
    }).fail(function() {
        alert('Error toggling template status');
    });
}

function deleteTemplate() {
    if (confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
        const form = $('<form>', {
            method: 'POST',
            action: '{{ route("admin.email.templates.destroy", $template) }}'
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: '_method',
            value: 'DELETE'
        }));
        
        $('body').append(form);
        form.submit();
    }
}

// Add styles for variable badges
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = '.variable-badge { cursor: pointer; } .variable-badge:hover { opacity: 0.8; }';
    document.head.appendChild(style);
});
</script>
@endpush
