<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\DriverSchedule;
use App\Models\DriverDocument;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class DriverController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display a listing of the drivers.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'driver')->with('driverDocuments');

        // Filter by status
        if ($request->has('status') && in_array($request->status, ['active', 'inactive'])) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by availability
        if ($request->has('availability') && in_array($request->availability, ['available', 'unavailable'])) {
            $query->where('is_available', $request->availability === 'available');
        }

        // Filter by document status
        if ($request->has('document_status')) {
            if ($request->document_status === 'expired') {
                $query->where(function($q) {
                    $q->whereDate('insurance_expiry', '<', now())
                      ->orWhereDate('mot_expiry', '<', now());
                });
            } elseif ($request->document_status === 'expiring_soon') {
                $query->where(function($q) {
                    $q->whereDate('insurance_expiry', '>=', now())
                      ->whereDate('insurance_expiry', '<=', now()->addDays(30))
                      ->orWhere(function($q2) {
                          $q2->whereDate('mot_expiry', '>=', now())
                             ->whereDate('mot_expiry', '<=', now()->addDays(30));
                      });
                });
            }
        }

        // Search by name, email, or license number
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('license_number', 'like', "%{$search}%")
                  ->orWhere('vehicle_make', 'like', "%{$search}%")
                  ->orWhere('vehicle_model', 'like', "%{$search}%")
                  ->orWhere('vehicle_reg_number', 'like', "%{$search}%");
            });
        }

        $drivers = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('admin.drivers.index', compact('drivers'));
    }

    /**
     * Show the form for creating a new driver.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function create()
    {
        $vehicles = Vehicle::where('is_active', true)->get();
        return view('admin.drivers.create', compact('vehicles'));
    }

    /**
     * Store a newly created driver in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'license_number' => 'required|string|max:50|unique:users',
            'vehicle_info' => 'nullable|string|max:255',
            'vehicle_make' => 'required|string|max:50',
            'vehicle_model' => 'required|string|max:50',
            'vehicle_color' => 'required|string|max:50',
            'vehicle_reg_number' => 'required|string|max:50',
            'insurance_expiry' => 'required|date',
            'mot_expiry' => 'required|date',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
            'is_available' => 'boolean',
        ]);

        $user = new User();
        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->role = 'driver';
        $user->phone = $request->phone;
        $user->address = $request->address;
        $user->license_number = $request->license_number;
        $user->vehicle_info = $request->vehicle_info;

        // New vehicle details
        $user->vehicle_make = $request->vehicle_make;
        $user->vehicle_model = $request->vehicle_model;
        $user->vehicle_color = $request->vehicle_color;
        $user->vehicle_reg_number = $request->vehicle_reg_number;
        $user->insurance_expiry = $request->insurance_expiry;
        $user->mot_expiry = $request->mot_expiry;

        $user->is_active = $request->boolean('is_active');
        $user->is_available = $request->boolean('is_available');

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            $photo = $request->file('profile_photo');
            $filename = \App\Services\StorageService::generateUniqueFilename($photo);
            $path = \App\Services\StorageService::uploadFile($photo, 'profile-photos', 'public', $filename);

            if ($path) {
                $user->profile_photo = $path;
            } else {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['profile_photo' => 'Failed to upload profile photo. Please try again.']);
            }
        }

        $user->save();

        // Handle document uploads if any
        if ($request->hasFile('documents')) {
            foreach ($request->file('documents') as $document) {
                $filename = \App\Services\StorageService::generateUniqueFilename($document);
                $path = \App\Services\StorageService::uploadFile($document, 'driver-documents', 'public', $filename);

                if ($path) {
                    DriverDocument::create([
                        'driver_id' => $user->id,
                        'document_type' => $request->input('document_type', 'Other'),
                        'file_path' => $path,
                        'expiry_date' => $request->input('expiry_date'),
                        'notes' => $request->input('document_notes'),
                    ]);
                }
            }
        }

        // Send welcome email to the new driver
        try {
            EmailService::sendDriverWelcome($user, $request->password);
        } catch (\Exception $e) {
            // Log the error but don't fail the driver creation
            \Log::error('Failed to send driver welcome email: ' . $e->getMessage(), [
                'driver_id' => $user->id,
                'driver_email' => $user->email,
            ]);
        }

        // Send admin alert for new driver registration
        try {
            $driverData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'license_number' => $user->license_number,
                'vehicle_make' => $user->vehicle_make,
                'vehicle_model' => $user->vehicle_model,
                'vehicle_color' => $user->vehicle_color,
                'vehicle_reg_number' => $user->vehicle_reg_number,
                'insurance_expiry' => $user->insurance_expiry,
                'is_active' => $user->is_active,
            ];

            EmailService::sendAdminDriverAlert('new_registration', $driverData, [
                'recommended_actions' => [
                    'Review driver documents and verify authenticity',
                    'Complete background check if required',
                    'Schedule driver orientation and training',
                    'Activate driver account once verification is complete',
                    'Add driver to dispatch system and communication channels'
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to send admin driver alert: ' . $e->getMessage(), [
                'driver_id' => $user->id,
            ]);
        }

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver created successfully and welcome email sent.');
    }

    /**
     * Display the specified driver.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function show($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);

        // Get driver's rides
        $rides = Booking::where('driver_id', $driver->id)
            ->with('vehicle', 'user')
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get driver's earnings
        $totalEarnings = Booking::where('driver_id', $driver->id)
            ->where('status', 'completed')
            ->sum('amount');

        // Get driver's documents
        $documents = DriverDocument::where('driver_id', $driver->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get driver's schedule
        $schedules = DriverSchedule::where('driver_id', $driver->id)
            ->orderBy('date', 'asc')
            ->get();

        return view('admin.drivers.show', compact('driver', 'rides', 'totalEarnings', 'documents', 'schedules'));
    }

    /**
     * Show the form for editing the specified driver.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function edit($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);
        $vehicles = Vehicle::where('is_active', true)->get();

        return view('admin.drivers.edit', compact('driver', 'vehicles'));
    }

    /**
     * Update the specified driver in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($driver->id),
            ],
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'license_number' => [
                'required',
                'string',
                'max:50',
                Rule::unique('users')->ignore($driver->id),
            ],
            'vehicle_info' => 'nullable|string|max:255',
            'vehicle_make' => 'required|string|max:50',
            'vehicle_model' => 'required|string|max:50',
            'vehicle_color' => 'required|string|max:50',
            'vehicle_reg_number' => 'required|string|max:50',
            'insurance_expiry' => 'required|date',
            'mot_expiry' => 'required|date',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
            'is_available' => 'boolean',
        ]);

        $driver->name = $request->name;
        $driver->email = $request->email;
        $driver->phone = $request->phone;
        $driver->address = $request->address;
        $driver->license_number = $request->license_number;
        $driver->vehicle_info = $request->vehicle_info;

        // New vehicle details
        $driver->vehicle_make = $request->vehicle_make;
        $driver->vehicle_model = $request->vehicle_model;
        $driver->vehicle_color = $request->vehicle_color;
        $driver->vehicle_reg_number = $request->vehicle_reg_number;
        $driver->insurance_expiry = $request->insurance_expiry;
        $driver->mot_expiry = $request->mot_expiry;

        $driver->is_active = $request->boolean('is_active');
        $driver->is_available = $request->boolean('is_available');

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($driver->profile_photo) {
                \App\Services\StorageService::deleteFile($driver->profile_photo, 'public');
            }

            $photo = $request->file('profile_photo');
            $filename = \App\Services\StorageService::generateUniqueFilename($photo);
            $path = \App\Services\StorageService::uploadFile($photo, 'profile-photos', 'public', $filename);

            if ($path) {
                $driver->profile_photo = $path;
            } else {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['profile_photo' => 'Failed to upload profile photo. Please try again.']);
            }
        }

        // Update password if provided
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8|confirmed',
            ]);

            $driver->password = Hash::make($request->password);
        }

        $driver->save();

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver updated successfully.');
    }

    /**
     * Remove the specified driver from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);

        // Unassign driver from all bookings
        \App\Models\Booking::where('driver_id', $driver->id)->update(['driver_id' => null]);

        // Delete profile photo if exists
        if ($driver->profile_photo) {
            Storage::disk('public')->delete($driver->profile_photo);
        }

        // Delete driver documents
        $documents = DriverDocument::where('driver_id', $driver->id)->get();
        foreach ($documents as $document) {
            Storage::disk('public')->delete($document->file_path);
            $document->delete();
        }

        // Delete driver schedules
        DriverSchedule::where('driver_id', $driver->id)->delete();

        $driver->delete();

        return redirect()->route('admin.drivers.index')
            ->with('success', 'Driver deleted successfully.');
    }

    /**
     * Toggle the active status of the specified driver.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleActive($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);
        $driver->is_active = !$driver->is_active;
        $driver->save();

        $status = $driver->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
            ->with('success', "Driver {$status} successfully.");
    }

    /**
     * Toggle the availability status of the specified driver.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleAvailability($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);
        $driver->is_available = !$driver->is_available;
        $driver->save();

        $status = $driver->is_available ? 'available' : 'unavailable';

        return redirect()->back()
            ->with('success', "Driver marked as {$status} successfully.");
    }

    /**
     * Export drivers data to CSV.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export()
    {
        $drivers = User::where('role', 'driver')->get();

        $filename = 'drivers_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($drivers) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Phone', 'License Number',
                'Address', 'Vehicle Make', 'Vehicle Model', 'Vehicle Color',
                'Vehicle Reg Number', 'Insurance Expiry', 'MOT Expiry',
                'Additional Vehicle Info', 'Status', 'Availability',
                'Created At'
            ]);

            // Add driver data
            foreach ($drivers as $driver) {
                fputcsv($file, [
                    $driver->id,
                    $driver->name,
                    $driver->email,
                    $driver->phone,
                    $driver->license_number,
                    $driver->address,
                    $driver->vehicle_make ?? 'N/A',
                    $driver->vehicle_model ?? 'N/A',
                    $driver->vehicle_color ?? 'N/A',
                    $driver->vehicle_reg_number ?? 'N/A',
                    $driver->insurance_expiry ? $driver->insurance_expiry->format('Y-m-d') : 'N/A',
                    $driver->mot_expiry ? $driver->mot_expiry->format('Y-m-d') : 'N/A',
                    $driver->vehicle_info,
                    $driver->is_active ? 'Active' : 'Inactive',
                    $driver->is_available ? 'Available' : 'Unavailable',
                    $driver->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show driver documents.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function documents($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);
        $documents = DriverDocument::where('driver_id', $driver->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.drivers.documents', compact('driver', 'documents'));
    }

    /**
     * Upload a document for a driver.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function uploadDocument(Request $request, $id)
    {
        $request->validate([
            'document' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'document_type' => 'required|string|max:50',
            'expiry_date' => 'nullable|date',
            'notes' => 'nullable|string|max:255',
        ]);

        $driver = User::where('role', 'driver')->findOrFail($id);

        if ($request->hasFile('document')) {
            $document = $request->file('document');
            $filename = \App\Services\StorageService::generateUniqueFilename($document);
            $path = \App\Services\StorageService::uploadFile($document, 'driver-documents', 'public', $filename);

            if ($path) {
                DriverDocument::create([
                    'driver_id' => $driver->id,
                    'document_type' => $request->document_type,
                    'file_path' => $path,
                    'expiry_date' => $request->expiry_date,
                    'notes' => $request->notes,
                    'is_verified' => false,
                ]);
            } else {
                return redirect()->back()
                    ->withErrors(['document' => 'Failed to upload document. Please try again.']);
            }
        }

        return redirect()->back()
            ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Delete a driver document.
     *
     * @param  int  $document_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteDocument($document_id)
    {
        $document = DriverDocument::findOrFail($document_id);

        // Delete file from storage
        if (Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        $document->delete();

        return redirect()->back()
            ->with('success', 'Document deleted successfully.');
    }

    /**
     * Verify a driver document.
     *
     * @param  int  $document_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verifyDocument($document_id)
    {
        $document = DriverDocument::findOrFail($document_id);
        $document->is_verified = !$document->is_verified;
        $document->save();

        $status = $document->is_verified ? 'verified' : 'unverified';

        return redirect()->back()
            ->with('success', "Document marked as {$status} successfully.");
    }

    /**
     * Show driver schedule management page.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function schedule($id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);
        $schedules = DriverSchedule::where('driver_id', $driver->id)
            ->orderBy('date', 'asc')
            ->get();

        // Group schedules by week
        $currentDate = Carbon::now();
        $startOfWeek = $currentDate->copy()->startOfWeek();
        $endOfWeek = $currentDate->copy()->endOfWeek();

        $weeklySchedules = [];
        for ($i = 0; $i < 4; $i++) {
            $weekStart = $startOfWeek->copy()->addWeeks($i);
            $weekEnd = $endOfWeek->copy()->addWeeks($i);

            $weeklySchedules[] = [
                'start_date' => $weekStart->format('Y-m-d'),
                'end_date' => $weekEnd->format('Y-m-d'),
                'label' => 'Week ' . ($i + 1) . ': ' . $weekStart->format('M d') . ' - ' . $weekEnd->format('M d'),
                'days' => []
            ];

            // Initialize days for this week
            for ($day = 0; $day < 7; $day++) {
                $date = $weekStart->copy()->addDays($day);
                $weeklySchedules[$i]['days'][$day] = [
                    'date' => $date->format('Y-m-d'),
                    'day_name' => $date->format('l'),
                    'schedules' => []
                ];
            }
        }

        // Populate schedules into the weekly structure
        foreach ($schedules as $schedule) {
            $scheduleDate = Carbon::parse($schedule->date);

            for ($i = 0; $i < 4; $i++) {
                $weekStart = $startOfWeek->copy()->addWeeks($i);
                $weekEnd = $endOfWeek->copy()->addWeeks($i);

                if ($scheduleDate->between($weekStart, $weekEnd)) {
                    $dayOfWeek = $scheduleDate->dayOfWeek;
                    $weeklySchedules[$i]['days'][$dayOfWeek]['schedules'][] = $schedule;
                    break;
                }
            }
        }

        return view('admin.drivers.schedule', compact('driver', 'schedules', 'weeklySchedules'));
    }

    /**
     * Update driver schedule.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSchedule(Request $request, $id)
    {
        $driver = User::where('role', 'driver')->findOrFail($id);

        $request->validate([
            'date' => 'required|array',
            'date.*' => 'required|date',
            'start_time' => 'required|array',
            'start_time.*' => 'required|string',
            'end_time' => 'required|array',
            'end_time.*' => 'required|string',
            'is_available' => 'required|array',
            'is_available.*' => 'boolean',
            'is_time_off' => 'required|array',
            'is_time_off.*' => 'boolean',
            'notes' => 'nullable|array',
            'notes.*' => 'nullable|string|max:255',
        ]);

        $count = count($request->date);

        for ($i = 0; $i < $count; $i++) {
            $date = $request->date[$i];
            $startTime = $request->start_time[$i];
            $endTime = $request->end_time[$i];
            $isAvailable = $request->is_available[$i] ?? false;
            $isTimeOff = $request->is_time_off[$i] ?? false;
            $notes = $request->notes[$i] ?? null;

            // Check if schedule already exists for this date
            $schedule = DriverSchedule::where('driver_id', $driver->id)
                ->where('date', $date)
                ->first();

            if ($schedule) {
                // Update existing schedule
                $schedule->start_time = $startTime;
                $schedule->end_time = $endTime;
                $schedule->is_available = $isAvailable;
                $schedule->is_time_off = $isTimeOff;
                $schedule->notes = $notes;
                $schedule->save();
            } else {
                // Create new schedule
                DriverSchedule::create([
                    'driver_id' => $driver->id,
                    'date' => $date,
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'is_available' => $isAvailable,
                    'is_time_off' => $isTimeOff,
                    'notes' => $notes,
                ]);
            }
        }

        return redirect()->route('admin.drivers.schedule', $driver->id)
            ->with('success', 'Driver schedule updated successfully.');
    }
}
