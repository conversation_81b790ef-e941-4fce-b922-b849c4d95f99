<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class StorageService
{
    /**
     * Upload a file to the specified disk and directory.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string $disk
     * @param string|null $filename
     * @param array $options
     * @return string|false
     */
    public static function uploadFile(UploadedFile $file, string $directory, string $disk = 'public', ?string $filename = null, array $options = []): string|false
    {
        try {
            // Validate file
            if (!self::validateFile($file)) {
                Log::error('File validation failed', [
                    'filename' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime' => $file->getMimeType(),
                ]);
                return false;
            }

            // Ensure directory exists
            if (!self::ensureDirectoryExists($directory, $disk)) {
                Log::error('Failed to create directory', ['directory' => $directory, 'disk' => $disk]);
                return false;
            }

            // Generate filename if not provided
            if (!$filename) {
                $filename = self::generateUniqueFilename($file);
            }

            // Handle image optimization if it's an image
            if (self::isImage($file) && isset($options['optimize']) && $options['optimize']) {
                return self::uploadOptimizedImage($file, $directory, $disk, $filename, $options);
            }

            // Store the file
            $path = $file->storeAs($directory, $filename, $disk);

            if ($path) {
                Log::info('File uploaded successfully', [
                    'path' => $path,
                    'disk' => $disk,
                    'original_filename' => $file->getClientOriginalName(),
                ]);
                return $path;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('File upload failed: ' . $e->getMessage(), [
                'directory' => $directory,
                'disk' => $disk,
                'original_filename' => $file->getClientOriginalName(),
                'exception' => $e,
            ]);
            return false;
        }
    }

    /**
     * Delete a file from storage.
     *
     * @param string $path
     * @param string $disk
     * @return bool
     */
    public static function deleteFile(string $path, string $disk = 'public'): bool
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->delete($path);
            }
            return true; // File doesn't exist, consider it deleted
        } catch (\Exception $e) {
            \Log::error('File deletion failed: ' . $e->getMessage(), [
                'path' => $path,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Move a file from one location to another.
     *
     * @param string $from
     * @param string $to
     * @param string $disk
     * @return bool
     */
    public static function moveFile(string $from, string $to, string $disk = 'public'): bool
    {
        try {
            if (Storage::disk($disk)->exists($from)) {
                return Storage::disk($disk)->move($from, $to);
            }
            return false;
        } catch (\Exception $e) {
            \Log::error('File move failed: ' . $e->getMessage(), [
                'from' => $from,
                'to' => $to,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Copy a file from one location to another.
     *
     * @param string $from
     * @param string $to
     * @param string $disk
     * @return bool
     */
    public static function copyFile(string $from, string $to, string $disk = 'public'): bool
    {
        try {
            if (Storage::disk($disk)->exists($from)) {
                return Storage::disk($disk)->copy($from, $to);
            }
            return false;
        } catch (\Exception $e) {
            \Log::error('File copy failed: ' . $e->getMessage(), [
                'from' => $from,
                'to' => $to,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Get file URL.
     *
     * @param string $path
     * @param string $disk
     * @return string|null
     */
    public static function getFileUrl(string $path, string $disk = 'public'): ?string
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->url($path);
            }
            return null;
        } catch (\Exception $e) {
            \Log::error('Get file URL failed: ' . $e->getMessage(), [
                'path' => $path,
                'disk' => $disk,
            ]);
            return null;
        }
    }

    /**
     * Check if file exists.
     *
     * @param string $path
     * @param string $disk
     * @return bool
     */
    public static function fileExists(string $path, string $disk = 'public'): bool
    {
        try {
            return Storage::disk($disk)->exists($path);
        } catch (\Exception $e) {
            \Log::error('File exists check failed: ' . $e->getMessage(), [
                'path' => $path,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Get file size in bytes.
     *
     * @param string $path
     * @param string $disk
     * @return int|false
     */
    public static function getFileSize(string $path, string $disk = 'public'): int|false
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->size($path);
            }
            return false;
        } catch (\Exception $e) {
            \Log::error('Get file size failed: ' . $e->getMessage(), [
                'path' => $path,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Ensure directory exists.
     *
     * @param string $directory
     * @param string $disk
     * @return bool
     */
    public static function ensureDirectoryExists(string $directory, string $disk = 'public'): bool
    {
        try {
            $fullPath = Storage::disk($disk)->path($directory);
            if (!File::isDirectory($fullPath)) {
                return File::makeDirectory($fullPath, 0755, true);
            }
            return true;
        } catch (\Exception $e) {
            \Log::error('Directory creation failed: ' . $e->getMessage(), [
                'directory' => $directory,
                'disk' => $disk,
            ]);
            return false;
        }
    }

    /**
     * Generate unique filename.
     *
     * @param UploadedFile $file
     * @return string
     */
    public static function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $basename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $basename = Str::slug($basename);

        return $basename . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Validate uploaded file.
     *
     * @param UploadedFile $file
     * @return bool
     */
    public static function validateFile(UploadedFile $file): bool
    {
        // Check if file is valid
        if (!$file->isValid()) {
            return false;
        }

        // Check file size
        $maxSize = config('filesystems.max_file_size', 10240) * 1024; // Convert KB to bytes
        if ($file->getSize() > $maxSize) {
            return false;
        }

        // Check MIME type
        $allowedMimes = config('filesystems.allowed_mimes', [
            'jpeg', 'jpg', 'png', 'gif', 'webp', 'svg',
            'pdf', 'doc', 'docx', 'txt'
        ]);

        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedMimes)) {
            return false;
        }

        return true;
    }

    /**
     * Check if file is an image.
     *
     * @param UploadedFile $file
     * @return bool
     */
    public static function isImage(UploadedFile $file): bool
    {
        $imageMimes = config('filesystems.image_mimes', ['jpeg', 'jpg', 'png', 'gif', 'webp']);
        $extension = strtolower($file->getClientOriginalExtension());

        return in_array($extension, $imageMimes);
    }

    /**
     * Upload optimized image.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string $disk
     * @param string $filename
     * @param array $options
     * @return string|false
     */
    public static function uploadOptimizedImage(UploadedFile $file, string $directory, string $disk, string $filename, array $options = []): string|false
    {
        try {
            $maxWidth = $options['max_width'] ?? 1200;
            $maxHeight = $options['max_height'] ?? 800;
            $quality = $options['quality'] ?? 85;

            // Get the full path for the file
            $fullPath = Storage::disk($disk)->path($directory . '/' . $filename);

            // Create optimized image
            $image = Image::make($file->getRealPath());

            // Resize if needed
            $image->resize($maxWidth, $maxHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });

            // Save with quality
            $image->save($fullPath, $quality);

            return $directory . '/' . $filename;
        } catch (\Exception $e) {
            Log::error('Image optimization failed: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'exception' => $e,
            ]);

            // Fallback to regular upload
            return $file->storeAs($directory, $filename, $disk) ?: false;
        }
    }

    /**
     * Clean up temporary files older than specified hours.
     *
     * @param int $hours
     * @return int Number of files deleted
     */
    public static function cleanupTempFiles(int $hours = 24): int
    {
        try {
            $tempDisk = Storage::disk('temp');
            $files = $tempDisk->allFiles();
            $deletedCount = 0;
            $cutoffTime = now()->subHours($hours);

            foreach ($files as $file) {
                $lastModified = $tempDisk->lastModified($file);
                if ($lastModified && $lastModified < $cutoffTime->timestamp) {
                    if ($tempDisk->delete($file)) {
                        $deletedCount++;
                    }
                }
            }

            return $deletedCount;
        } catch (\Exception $e) {
            \Log::error('Temp file cleanup failed: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get storage disk usage information.
     *
     * @param string $disk
     * @return array
     */
    public static function getDiskUsage(string $disk = 'public'): array
    {
        try {
            $diskPath = Storage::disk($disk)->path('');
            $freeBytes = disk_free_space($diskPath);
            $totalBytes = disk_total_space($diskPath);
            $usedBytes = $totalBytes - $freeBytes;
            $usedPercentage = ($usedBytes / $totalBytes) * 100;

            return [
                'total' => $totalBytes,
                'used' => $usedBytes,
                'free' => $freeBytes,
                'used_percentage' => round($usedPercentage, 2),
                'total_formatted' => self::formatBytes($totalBytes),
                'used_formatted' => self::formatBytes($usedBytes),
                'free_formatted' => self::formatBytes($freeBytes),
            ];
        } catch (\Exception $e) {
            \Log::error('Get disk usage failed: ' . $e->getMessage(), ['disk' => $disk]);
            return [
                'total' => 0,
                'used' => 0,
                'free' => 0,
                'used_percentage' => 0,
                'total_formatted' => '0 B',
                'used_formatted' => '0 B',
                'free_formatted' => '0 B',
            ];
        }
    }

    /**
     * Format bytes to human readable format.
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    public static function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get file URL with fallback.
     *
     * @param string|null $path
     * @param string $disk
     * @param string|null $fallback
     * @return string
     */
    public static function getFileUrlWithFallback(?string $path, string $disk = 'public', ?string $fallback = null): string
    {
        if (!$path) {
            return $fallback ?? asset('images/placeholder.png');
        }

        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->url($path);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get file URL: ' . $e->getMessage(), [
                'path' => $path,
                'disk' => $disk,
            ]);
        }

        return $fallback ?? asset('images/placeholder.png');
    }

    /**
     * Create thumbnail for image.
     *
     * @param string $imagePath
     * @param string $disk
     * @param int $width
     * @param int $height
     * @return string|false
     */
    public static function createThumbnail(string $imagePath, string $disk = 'public', int $width = 300, int $height = 200): string|false
    {
        try {
            if (!Storage::disk($disk)->exists($imagePath)) {
                return false;
            }

            $pathInfo = pathinfo($imagePath);
            $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
            $fullImagePath = Storage::disk($disk)->path($imagePath);
            $fullThumbnailPath = Storage::disk($disk)->path($thumbnailPath);

            // Create thumbnail using Intervention Image
            $image = Image::make($fullImagePath);
            $image->fit($width, $height);
            $image->save($fullThumbnailPath, 85);

            return $thumbnailPath;
        } catch (\Exception $e) {
            Log::error('Thumbnail creation failed: ' . $e->getMessage(), [
                'image_path' => $imagePath,
                'exception' => $e,
            ]);
            return false;
        }
    }

    /**
     * Ensure storage link exists.
     *
     * @return bool
     */
    public static function ensureStorageLink(): bool
    {
        try {
            $linkPath = public_path('storage');
            $targetPath = storage_path('app/public');

            // Check if link already exists and is valid
            if (is_link($linkPath) && readlink($linkPath) === $targetPath) {
                return true;
            }

            // Remove existing link/directory if it exists
            if (file_exists($linkPath)) {
                if (is_link($linkPath)) {
                    unlink($linkPath);
                } else {
                    File::deleteDirectory($linkPath);
                }
            }

            // Create the symbolic link
            if (function_exists('symlink')) {
                return symlink($targetPath, $linkPath);
            }

            // Fallback for Windows or systems without symlink support
            return File::copyDirectory($targetPath, $linkPath);
        } catch (\Exception $e) {
            Log::error('Failed to create storage link: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return false;
        }
    }

    /**
     * Get storage status information.
     *
     * @return array
     */
    public static function getStorageStatus(): array
    {
        $status = [
            'storage_link_exists' => false,
            'storage_link_valid' => false,
            'public_disk_accessible' => false,
            'directories_exist' => [],
            'permissions_ok' => false,
        ];

        try {
            // Check storage link
            $linkPath = public_path('storage');
            $targetPath = storage_path('app/public');

            $status['storage_link_exists'] = file_exists($linkPath);
            $status['storage_link_valid'] = is_link($linkPath) && readlink($linkPath) === $targetPath;

            // Check public disk accessibility
            $status['public_disk_accessible'] = Storage::disk('public')->exists('');

            // Check required directories
            $requiredDirs = ['vehicles', 'profile-photos', 'driver-documents', 'logos', 'favicons'];
            foreach ($requiredDirs as $dir) {
                $status['directories_exist'][$dir] = Storage::disk('public')->exists($dir);
            }

            // Check permissions
            $status['permissions_ok'] = is_writable(storage_path('app/public'));

        } catch (\Exception $e) {
            Log::error('Storage status check failed: ' . $e->getMessage());
        }

        return $status;
    }
}
