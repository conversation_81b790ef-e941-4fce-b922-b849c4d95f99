<?php

namespace Database\Factories;

use App\Models\EmailLog;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailLog>
 */
class EmailLogFactory extends Factory
{
    protected $model = EmailLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement([
                'booking_confirmation',
                'payment_receipt',
                'booking_reminder',
                'welcome_client',
                'welcome_driver',
                'contact_form',
                'test_email',
            ]),
            'direction' => $this->faker->randomElement(['sent', 'received']),
            'from_email' => $this->faker->safeEmail(),
            'to_email' => $this->faker->safeEmail(),
            'subject' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(2, true),
            'status' => $this->faker->randomElement(['pending', 'sent', 'delivered', 'bounced', 'failed', 'processed']),
            'related_type' => $this->faker->randomElement(['Booking', 'User', null]),
            'related_id' => $this->faker->optional()->numberBetween(1, 100),
            'metadata' => [
                'timestamp' => now()->toISOString(),
                'server' => 'localhost',
                'ip_address' => $this->faker->ipv4(),
            ],
            'error_message' => $this->faker->optional()->sentence(),
            'sent_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'bounced_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'processed_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the email was sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'direction' => 'sent',
            'status' => 'sent',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the email was received.
     */
    public function received(): static
    {
        return $this->state(fn (array $attributes) => [
            'direction' => 'received',
            'status' => 'processed',
            'processed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the email failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_message' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the email bounced.
     */
    public function bounced(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'bounced',
            'bounced_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'error_message' => 'Email bounced: ' . $this->faker->sentence(),
        ]);
    }

    /**
     * Create a booking confirmation email log.
     */
    public function bookingConfirmation(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'booking_confirmation',
            'direction' => 'sent',
            'status' => 'sent',
            'related_type' => 'Booking',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Create a payment receipt email log.
     */
    public function paymentReceipt(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'payment_receipt',
            'direction' => 'sent',
            'status' => 'sent',
            'related_type' => 'Payment',
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Create a test email log.
     */
    public function testEmail(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'test_email',
            'direction' => 'sent',
            'status' => 'sent',
            'subject' => 'Email Configuration Test',
            'sent_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }
}
