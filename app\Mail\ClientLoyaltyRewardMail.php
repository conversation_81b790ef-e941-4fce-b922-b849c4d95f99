<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Services\SettingsService;

class ClientLoyaltyRewardMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $client;
    public $rewardType;
    public $rewardData;

    /**
     * Create a new message instance.
     */
    public function __construct(User $client, string $rewardType, array $rewardData)
    {
        $this->client = $client;
        $this->rewardType = $rewardType;
        $this->rewardData = $rewardData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $rewardTitles = [
            'milestone' => '🎉 Congratulations! You\'ve reached a milestone',
            'discount' => '💰 Special Discount Just for You',
            'birthday' => '🎂 Happy Birthday! Here\'s a special gift',
            'loyalty_points' => '⭐ You\'ve earned loyalty points',
            'vip_upgrade' => '👑 Welcome to VIP Status',
            'referral_bonus' => '🤝 Thank you for referring friends',
        ];

        $title = $rewardTitles[$this->rewardType] ?? '🎁 Special Reward for You';

        return new Envelope(
            subject: $title . ' - ' . SettingsService::getCompanyName(),
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.client-loyalty-reward',
            with: [
                'client' => $this->client,
                'rewardType' => $this->rewardType,
                'rewardData' => $this->rewardData,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
