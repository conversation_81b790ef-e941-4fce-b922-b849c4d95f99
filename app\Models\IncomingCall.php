<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomingCall extends Model
{
    use HasFactory;

    protected $fillable = [
        'call_id',
        'user_id',
        'event_type',
        'caller_number',
        'caller_number_e164',
        'dialled_number',
        'client_id',
        'client_name',
        'client_email',
        'status',
        'notes',
        'handled_by',
        'handled_at',
        'follow_up_required',
        'follow_up_at',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'handled_at' => 'datetime',
        'follow_up_at' => 'datetime',
        'follow_up_required' => 'boolean',
    ];

    /**
     * Get the client associated with this call
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * Get the user who handled this call
     */
    public function handler()
    {
        return $this->belongsTo(User::class, 'handled_by');
    }

    /**
     * Get the status badge class for display
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'ringing' => 'bg-warning',
            'answered' => 'bg-success',
            'missed' => 'bg-danger',
            'handled' => 'bg-primary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get the event type badge class for display
     */
    public function getEventTypeBadgeClassAttribute(): string
    {
        return match($this->event_type) {
            'call_ringing' => 'bg-info',
            'call_missed' => 'bg-warning',
            'call_answered' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    /**
     * Scope for missed calls
     */
    public function scopeMissed($query)
    {
        return $query->where('status', 'missed');
    }

    /**
     * Scope for answered calls
     */
    public function scopeAnswered($query)
    {
        return $query->where('status', 'answered');
    }

    /**
     * Scope for calls requiring follow-up
     */
    public function scopeRequiresFollowUp($query)
    {
        return $query->where('follow_up_required', true);
    }

    /**
     * Scope for today's calls
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope for recent calls
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Mark call as handled
     */
    public function markAsHandled($userId = null, $notes = null)
    {
        $this->update([
            'status' => 'handled',
            'handled_by' => $userId ?? auth()->id(),
            'handled_at' => now(),
            'notes' => $notes,
        ]);
    }

    /**
     * Set follow-up requirement
     */
    public function setFollowUp($required = true, $followUpAt = null)
    {
        $this->update([
            'follow_up_required' => $required,
            'follow_up_at' => $followUpAt,
        ]);
    }
}
