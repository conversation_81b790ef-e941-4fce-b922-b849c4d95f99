<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Send booking reminders daily at 9 AM
        $schedule->command('bookings:send-reminders')
                 ->dailyAt('09:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send driver document expiry reminders weekly on Mondays at 8 AM
        $schedule->command('drivers:document-expiry-reminders')
                 ->weeklyOn(1, '08:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send weekly earnings summary on Mondays at 10 AM
        $schedule->command('drivers:earnings-summary --period=weekly')
                 ->weeklyOn(1, '10:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send monthly earnings summary on the 1st of each month at 10 AM
        $schedule->command('drivers:earnings-summary --period=monthly')
                 ->monthlyOn(1, '10:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send daily business report to admin every morning at 7 AM
        $schedule->command('admin:daily-report')
                 ->dailyAt('07:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send weekly revenue report to admin on Mondays at 9 AM
        $schedule->command('admin:revenue-report --period=weekly')
                 ->weeklyOn(1, '09:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send monthly revenue report to admin on the 1st of each month at 9 AM
        $schedule->command('admin:revenue-report --period=monthly')
                 ->monthlyOn(1, '09:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Monitor system health every 30 minutes
        $schedule->command('admin:monitor-system-health')
                 ->everyThirtyMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send feedback requests 24 hours after ride completion
        $schedule->command('clients:feedback-requests --hours=24')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Check for milestone rewards daily at 10 AM
        $schedule->command('clients:loyalty-rewards --type=milestone')
                 ->dailyAt('10:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Check for birthday rewards daily at 9 AM
        $schedule->command('clients:loyalty-rewards --type=birthday')
                 ->dailyAt('09:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Check for referral rewards daily at 11 AM
        $schedule->command('clients:loyalty-rewards --type=referral')
                 ->dailyAt('11:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Send monthly newsletter on the 1st of each month at 12 PM
        $schedule->command('clients:newsletter --type=monthly')
                 ->monthlyOn(1, '12:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up storage files daily at 2 AM
        $schedule->command('storage:cleanup')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // Process scheduled email campaigns every 5 minutes
        $schedule->command('email:process-scheduled')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Clean up old email logs weekly on Sundays at 3 AM
        $schedule->command('email:cleanup-logs --days=90')
                 ->weeklyOn(0, '03:00')
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
