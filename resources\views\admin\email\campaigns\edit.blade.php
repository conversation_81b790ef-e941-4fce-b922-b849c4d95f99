@extends('layouts.admin')

@section('title', 'Edit Email Campaign')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📢 Edit Email Campaign</h1>
            <p class="text-muted">Edit "{{ $campaign->name }}" campaign</p>
        </div>
        <div>
            <a href="{{ route('admin.email.campaigns.show', $campaign) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i> View
            </a>
            <a href="{{ route('admin.email.campaigns.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Campaigns
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.email.campaigns.update', $campaign) }}" id="campaignForm">
        @csrf
        @method('PUT')
        <div class="row">
            <div class="col-lg-8">
                <!-- Campaign Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📝 Campaign Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Campaign Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $campaign->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="template_id" class="form-label">Use Template</label>
                                    <select class="form-select @error('template_id') is-invalid @enderror" 
                                            id="template_id" name="template_id" onchange="loadTemplate()">
                                        <option value="">Select Template (Optional)</option>
                                        @foreach($templates as $template)
                                            <option value="{{ $template->id }}" 
                                                    data-subject="{{ $template->subject }}"
                                                    data-content="{{ $template->content }}"
                                                    {{ old('template_id', $campaign->template_id) == $template->id ? 'selected' : '' }}>
                                                {{ $template->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('template_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                   id="subject" name="subject" value="{{ old('subject', $campaign->subject) }}" required>
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Email Content <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="12" required>{{ old('content', $campaign->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Recipients -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">👥 Recipients</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="recipient_type" class="form-label">Recipient Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('recipient_type') is-invalid @enderror" 
                                            id="recipient_type" name="recipient_type" required onchange="updateRecipientCount()">
                                        <option value="">Select Recipients</option>
                                        @foreach($recipientTypes as $type)
                                            <option value="{{ $type }}" {{ old('recipient_type', $campaign->recipient_type) == $type ? 'selected' : '' }}>
                                                {{ ucfirst($type) }} {{ $type === 'all' ? 'Users' : ucfirst($type) . 's' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('recipient_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Estimated Recipients</label>
                                    <div class="form-control-plaintext">
                                        <span id="recipientCount" class="badge bg-info fs-6">{{ $campaign->total_recipients }} recipients</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Filters -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                    data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                <i class="fas fa-filter me-1"></i> Advanced Filters
                            </button>
                        </div>

                        <div class="collapse" id="advancedFilters">
                            <div class="border rounded p-3 bg-light">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="active_only" 
                                                   name="recipient_criteria[active_only]" value="1"
                                                   {{ old('recipient_criteria.active_only', $campaign->recipient_criteria['active_only'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="active_only">
                                                Active users only
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="has_bookings" 
                                                   name="recipient_criteria[has_bookings]" value="1"
                                                   {{ old('recipient_criteria.has_bookings', $campaign->recipient_criteria['has_bookings'] ?? false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="has_bookings">
                                                Users with bookings only
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="exclude_unsubscribed" 
                                                   name="recipient_criteria[exclude_unsubscribed]" value="1" 
                                                   {{ old('recipient_criteria.exclude_unsubscribed', $campaign->recipient_criteria['exclude_unsubscribed'] ?? true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="exclude_unsubscribed">
                                                Exclude unsubscribed users
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <label for="registration_date_from" class="form-label small">Registered from:</label>
                                            <input type="date" class="form-control form-control-sm" 
                                                   id="registration_date_from" name="recipient_criteria[registration_date_from]"
                                                   value="{{ old('recipient_criteria.registration_date_from', $campaign->recipient_criteria['registration_date_from'] ?? '') }}">
                                        </div>
                                        <div class="mb-2">
                                            <label for="registration_date_to" class="form-label small">Registered to:</label>
                                            <input type="date" class="form-control form-control-sm" 
                                                   id="registration_date_to" name="recipient_criteria[registration_date_to]"
                                                   value="{{ old('recipient_criteria.registration_date_to', $campaign->recipient_criteria['registration_date_to'] ?? '') }}">
                                        </div>
                                        <div class="mb-2">
                                            <label for="last_booking_from" class="form-label small">Last booking from:</label>
                                            <input type="date" class="form-control form-control-sm" 
                                                   id="last_booking_from" name="recipient_criteria[last_booking_from]"
                                                   value="{{ old('recipient_criteria.last_booking_from', $campaign->recipient_criteria['last_booking_from'] ?? '') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scheduling -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">⏰ Scheduling</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="send_option" 
                                           id="send_now" value="now" {{ !$campaign->scheduled_at ? 'checked' : '' }} onchange="toggleScheduling()">
                                    <label class="form-check-label" for="send_now">
                                        <strong>Send Now</strong>
                                        <div class="small text-muted">Campaign will be queued for immediate sending</div>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="send_option" 
                                           id="send_later" value="later" {{ $campaign->scheduled_at ? 'checked' : '' }} onchange="toggleScheduling()">
                                    <label class="form-check-label" for="send_later">
                                        <strong>Schedule for Later</strong>
                                        <div class="small text-muted">Set a specific date and time to send</div>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="send_option" 
                                           id="save_draft" value="draft" onchange="toggleScheduling()">
                                    <label class="form-check-label" for="save_draft">
                                        <strong>Save as Draft</strong>
                                        <div class="small text-muted">Save without sending</div>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="schedulingOptions" style="{{ $campaign->scheduled_at ? 'display: block;' : 'display: none;' }}">
                                    <label for="scheduled_at" class="form-label">Schedule Date & Time</label>
                                    <input type="datetime-local" class="form-control @error('scheduled_at') is-invalid @enderror" 
                                           id="scheduled_at" name="scheduled_at" 
                                           value="{{ old('scheduled_at', $campaign->scheduled_at?->format('Y-m-d\TH:i')) }}"
                                           min="{{ now()->format('Y-m-d\TH:i') }}">
                                    @error('scheduled_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Campaign will be sent automatically at this time</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Campaign Info -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📊 Campaign Info</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="text-muted">Status:</td>
                                <td>
                                    <span class="badge {{ $campaign->status_badge_class }}">
                                        {{ ucfirst($campaign->status) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Created:</td>
                                <td>{{ $campaign->created_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            @if($campaign->scheduled_at)
                                <tr>
                                    <td class="text-muted">Scheduled:</td>
                                    <td>{{ $campaign->scheduled_at->format('M j, Y g:i A') }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>

                <!-- Variable Helper -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">💡 Variables</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="small text-muted">User Variables:</h6>
                            <div class="d-flex flex-wrap gap-1 mb-2">
                                <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('user_name')">{user_name}</span>
                                <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('user_email')">{user_email}</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6 class="small text-muted">Company Variables:</h6>
                            <div class="d-flex flex-wrap gap-1 mb-2">
                                <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_name')">{company_name}</span>
                                <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_email')">{company_email}</span>
                                <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_phone')">{company_phone}</span>
                            </div>
                        </div>

                        <small class="text-muted">Click variables to insert into content</small>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">💾 Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Campaign
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewCampaign()">
                                <i class="fas fa-eye me-1"></i> Preview
                            </button>
                            <a href="{{ route('admin.email.campaigns.show', $campaign) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Campaign Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalPreviewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Same JavaScript functions as create campaign
function loadTemplate() {
    const select = document.getElementById('template_id');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        document.getElementById('subject').value = selectedOption.dataset.subject || '';
        document.getElementById('content').value = selectedOption.dataset.content || '';
    }
}

function toggleScheduling() {
    const sendLater = document.getElementById('send_later').checked;
    const schedulingOptions = document.getElementById('schedulingOptions');
    
    if (sendLater) {
        schedulingOptions.style.display = 'block';
        document.getElementById('scheduled_at').required = true;
    } else {
        schedulingOptions.style.display = 'none';
        document.getElementById('scheduled_at').required = false;
        if (!document.getElementById('save_draft').checked) {
            document.getElementById('scheduled_at').value = '';
        }
    }
}

function updateRecipientCount() {
    const recipientType = document.getElementById('recipient_type').value;
    const countElement = document.getElementById('recipientCount');
    
    if (!recipientType) {
        countElement.textContent = 'Select type to see count';
        countElement.className = 'badge bg-info fs-6';
        return;
    }
    
    countElement.textContent = 'Calculating...';
    countElement.className = 'badge bg-warning fs-6';
    
    $.ajax({
        url: '{{ route("admin.email.campaigns.recipient-count") }}',
        method: 'POST',
        data: {
            recipient_type: recipientType,
            recipient_criteria: getRecipientCriteria(),
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            countElement.textContent = response.count + ' recipients';
            countElement.className = 'badge bg-success fs-6';
        },
        error: function() {
            countElement.textContent = 'Error calculating';
            countElement.className = 'badge bg-danger fs-6';
        }
    });
}

function getRecipientCriteria() {
    return {
        active_only: document.getElementById('active_only').checked ? 1 : 0,
        has_bookings: document.getElementById('has_bookings').checked ? 1 : 0,
        exclude_unsubscribed: document.getElementById('exclude_unsubscribed').checked ? 1 : 0,
        registration_date_from: document.getElementById('registration_date_from').value,
        registration_date_to: document.getElementById('registration_date_to').value,
        last_booking_from: document.getElementById('last_booking_from').value
    };
}

function insertVariable(variable) {
    const content = document.getElementById('content');
    const cursorPos = content.selectionStart;
    const textBefore = content.value.substring(0, cursorPos);
    const textAfter = content.value.substring(cursorPos);
    
    content.value = textBefore + '{' + variable + '}' + textAfter;
    content.focus();
    content.setSelectionRange(cursorPos + variable.length + 2, cursorPos + variable.length + 2);
}

function previewCampaign() {
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('content').value;
    
    if (!subject || !content) {
        alert('Please fill in the subject and content fields to preview.');
        return;
    }
    
    const sampleData = {
        user_name: 'John Doe',
        user_email: '<EMAIL>',
        company_name: 'YNR Cars',
        company_email: '<EMAIL>',
        company_phone: '+44 ************'
    };
    
    let previewSubject = subject;
    let previewContent = content;
    
    Object.keys(sampleData).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        previewSubject = previewSubject.replace(regex, sampleData[key]);
        previewContent = previewContent.replace(regex, sampleData[key]);
    });
    
    previewContent = previewContent.replace(/\n/g, '<br>');
    
    const previewHtml = `
        <div class="border rounded p-3" style="background-color: #f8f9fa;">
            <h6 class="text-primary">Subject:</h6>
            <p class="fw-bold">${previewSubject}</p>
            
            <h6 class="text-primary">Content:</h6>
            <div class="border rounded p-3 bg-white">
                ${previewContent}
            </div>
            
            <small class="text-muted mt-2 d-block">
                <strong>Note:</strong> This preview uses sample data. Actual emails will use real user data.
            </small>
        </div>
    `;
    
    document.getElementById('modalPreviewContent').innerHTML = previewHtml;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Add styles for variable badges
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = '.variable-badge { cursor: pointer; } .variable-badge:hover { background-color: #e9ecef !important; }';
    document.head.appendChild(style);
});
</script>
@endpush
