@extends('layouts.admin')

@section('title', 'Outgoing Emails')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">📤 Outgoing Emails</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.email.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Email Management
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.email.outgoing') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label for="type" class="form-label">Email Type</label>
                                    <select name="type" id="type" class="form-select">
                                        <option value="">All Types</option>
                                        @foreach($emailTypes as $type)
                                            <option value="{{ $type }}" {{ request('type') === $type ? 'selected' : '' }}>
                                                {{ ucfirst(str_replace('_', ' ', $type)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">All Statuses</option>
                                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Sent</option>
                                        <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                                        <option value="bounced" {{ request('status') === 'bounced' ? 'selected' : '' }}>Bounced</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>

                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>

                                <div class="col-md-2">
                                    <label for="search" class="form-label">Search</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control" placeholder="Email, subject..." value="{{ request('search') }}">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $outgoingEmails->total() }}</h4>
                                            <p class="mb-0">Total Outgoing</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-paper-plane fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $outgoingEmails->where('status', 'delivered')->count() }}</h4>
                                            <p class="mb-0">Delivered</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $outgoingEmails->where('status', 'pending')->count() }}</h4>
                                            <p class="mb-0">Pending</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $outgoingEmails->whereIn('status', ['failed', 'bounced'])->count() }}</h4>
                                            <p class="mb-0">Failed/Bounced</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Emails Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Type</th>
                                    <th>To</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Sent At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($outgoingEmails as $email)
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ ucfirst(str_replace('_', ' ', $email->type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <strong>{{ $email->to_email }}</strong>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 300px;" title="{{ $email->subject }}">
                                                {{ $email->subject ?: 'No Subject' }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge {{ $email->status_badge_class }}">
                                                {{ ucfirst($email->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                {{ $email->created_at->format('d M Y H:i') }}
                                                <br>
                                                <span class="text-muted">{{ $email->created_at->diffForHumans() }}</span>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewEmailDetails({{ $email->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($email->status === 'failed')
                                                    <button type="button" class="btn btn-outline-warning" onclick="retryEmail({{ $email->id }})">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p>No outgoing emails found</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($outgoingEmails->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $outgoingEmails->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Details Modal -->
<div class="modal fade" id="emailDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📧 Email Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="emailDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function viewEmailDetails(emailId) {
    $('#emailDetailsModal').modal('show');
    $('#emailDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    
    // In a real implementation, you would fetch email details via AJAX
    setTimeout(() => {
        $('#emailDetailsContent').html(`
            <div class="row">
                <div class="col-md-6">
                    <strong>Email ID:</strong> ${emailId}<br>
                    <strong>Type:</strong> Booking Confirmation<br>
                    <strong>Status:</strong> <span class="badge bg-success">Delivered</span><br>
                    <strong>Sent At:</strong> ${new Date().toLocaleString()}
                </div>
                <div class="col-md-6">
                    <strong>To:</strong> <EMAIL><br>
                    <strong>Subject:</strong> Booking Confirmation<br>
                    <strong>Delivered At:</strong> ${new Date().toLocaleString()}
                </div>
            </div>
            <hr>
            <div>
                <strong>Content Preview:</strong>
                <div class="border p-3 mt-2" style="max-height: 300px; overflow-y: auto;">
                    <p>Dear Client,</p>
                    <p>Your booking has been confirmed...</p>
                </div>
            </div>
        `);
    }, 500);
}

function retryEmail(emailId) {
    if (confirm('Are you sure you want to retry sending this email?')) {
        // In a real implementation, you would make an AJAX call to retry the email
        alert('Email retry functionality would be implemented here');
    }
}
</script>
@endsection
