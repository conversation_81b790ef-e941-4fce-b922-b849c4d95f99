<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Booking;
use App\Services\SettingsService;

class ClientBookingStatusMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $booking;
    public $statusType;
    public $previousStatus;

    /**
     * Create a new message instance.
     */
    public function __construct(Booking $booking, string $statusType, string $previousStatus = null)
    {
        $this->booking = $booking;
        $this->statusType = $statusType;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusTitles = [
            'confirmed' => '✅ Booking Confirmed',
            'assigned' => '🚗 Driver Assigned',
            'in_progress' => '🚙 Your Ride is On the Way',
            'completed' => '🎉 Ride Completed',
            'cancelled' => '❌ Booking Cancelled',
            'modified' => '📝 Booking Modified',
        ];

        $title = $statusTitles[$this->statusType] ?? 'Booking Update';

        return new Envelope(
            subject: $title . ' - Booking #' . $this->booking->booking_number,
            from: new \Illuminate\Mail\Mailables\Address(
                SettingsService::get('mail_from_address', config('mail.from.address')),
                SettingsService::get('mail_from_name', config('mail.from.name'))
            ),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.client-booking-status',
            with: [
                'booking' => $this->booking,
                'statusType' => $this->statusType,
                'previousStatus' => $this->previousStatus,
                'companyName' => SettingsService::getCompanyName(),
                'companyEmail' => SettingsService::getCompanyEmail(),
                'companyPhone' => SettingsService::getCompanyPhone(),
                'companyAddress' => SettingsService::getCompanyAddress(),
                'currencySymbol' => SettingsService::getCurrencySymbol(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
