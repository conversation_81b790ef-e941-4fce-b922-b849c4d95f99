<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\User;
use App\Services\EmailQueueService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmailCampaignController extends Controller
{
    /**
     * Display a listing of email campaigns
     */
    public function index(Request $request)
    {
        $query = EmailCampaign::with(['template', 'creator']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('recipient_type')) {
            $query->where('recipient_type', $request->recipient_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $campaigns = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $statuses = ['draft', 'scheduled', 'sending', 'sent', 'completed', 'failed', 'cancelled'];
        $recipientTypes = ['all', 'client', 'driver', 'admin'];

        return view('admin.email.campaigns.index', compact('campaigns', 'statuses', 'recipientTypes'));
    }

    /**
     * Show the form for creating a new campaign
     */
    public function create()
    {
        $templates = EmailTemplate::active()->orderBy('name')->get();
        $recipientTypes = ['all', 'client', 'driver', 'admin'];
        
        return view('admin.email.campaigns.create', compact('templates', 'recipientTypes'));
    }

    /**
     * Store a newly created campaign
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'template_id' => 'nullable|exists:email_templates,id',
            'recipient_type' => 'required|string|in:all,client,driver,admin',
            'recipient_criteria' => 'nullable|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $campaign = EmailCampaign::create([
                'name' => $request->name,
                'subject' => $request->subject,
                'content' => $request->content,
                'template_id' => $request->template_id,
                'recipient_type' => $request->recipient_type,
                'recipient_criteria' => $request->recipient_criteria ?? [],
                'scheduled_at' => $request->scheduled_at,
                'status' => $request->scheduled_at ? 'scheduled' : 'draft',
                'created_by' => Auth::id(),
            ]);

            return redirect()->route('admin.email.campaigns.show', $campaign)
                ->with('success', 'Email campaign created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create email campaign: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified campaign
     */
    public function show(EmailCampaign $campaign)
    {
        $campaign->load(['template', 'creator', 'recipients']);
        
        // Get recipient statistics
        $recipientStats = [
            'total' => $campaign->recipients()->count(),
            'pending' => $campaign->recipients()->where('status', 'pending')->count(),
            'sent' => $campaign->recipients()->where('status', 'sent')->count(),
            'delivered' => $campaign->recipients()->where('status', 'delivered')->count(),
            'opened' => $campaign->recipients()->whereNotNull('opened_at')->count(),
            'clicked' => $campaign->recipients()->whereNotNull('clicked_at')->count(),
            'failed' => $campaign->recipients()->whereIn('status', ['failed', 'bounced'])->count(),
        ];

        return view('admin.email.campaigns.show', compact('campaign', 'recipientStats'));
    }

    /**
     * Show the form for editing the specified campaign
     */
    public function edit(EmailCampaign $campaign)
    {
        if (!$campaign->canBeEdited()) {
            return redirect()->route('admin.email.campaigns.show', $campaign)
                ->with('error', 'This campaign cannot be edited in its current status.');
        }

        $templates = EmailTemplate::active()->orderBy('name')->get();
        $recipientTypes = ['all', 'client', 'driver', 'admin'];
        
        return view('admin.email.campaigns.edit', compact('campaign', 'templates', 'recipientTypes'));
    }

    /**
     * Update the specified campaign
     */
    public function update(Request $request, EmailCampaign $campaign)
    {
        if (!$campaign->canBeEdited()) {
            return redirect()->route('admin.email.campaigns.show', $campaign)
                ->with('error', 'This campaign cannot be edited in its current status.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'template_id' => 'nullable|exists:email_templates,id',
            'recipient_type' => 'required|string|in:all,client,driver,admin',
            'recipient_criteria' => 'nullable|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $campaign->update([
                'name' => $request->name,
                'subject' => $request->subject,
                'content' => $request->content,
                'template_id' => $request->template_id,
                'recipient_type' => $request->recipient_type,
                'recipient_criteria' => $request->recipient_criteria ?? [],
                'scheduled_at' => $request->scheduled_at,
                'status' => $request->scheduled_at ? 'scheduled' : 'draft',
            ]);

            return redirect()->route('admin.email.campaigns.show', $campaign)
                ->with('success', 'Email campaign updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update email campaign: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified campaign
     */
    public function destroy(EmailCampaign $campaign)
    {
        try {
            if (!$campaign->canBeEdited()) {
                return redirect()->back()
                    ->with('error', 'This campaign cannot be deleted in its current status.');
            }

            $campaign->delete();

            return redirect()->route('admin.email.campaigns.index')
                ->with('success', 'Email campaign deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete email campaign: ' . $e->getMessage());
        }
    }

    /**
     * Send campaign immediately
     */
    public function send(EmailCampaign $campaign)
    {
        try {
            if (!$campaign->canBeSent()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This campaign cannot be sent in its current status.',
                ], 400);
            }

            $success = EmailQueueService::queueCampaign($campaign);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Campaign has been queued for sending.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to queue campaign for sending.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending campaign: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel campaign
     */
    public function cancel(EmailCampaign $campaign)
    {
        try {
            if (!$campaign->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This campaign cannot be cancelled in its current status.',
                ], 400);
            }

            $success = EmailQueueService::cancelCampaign($campaign);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Campaign has been cancelled.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to cancel campaign.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling campaign: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Retry failed emails in campaign
     */
    public function retryFailed(EmailCampaign $campaign)
    {
        try {
            $retryCount = EmailQueueService::retryFailedEmails($campaign);

            return response()->json([
                'success' => true,
                'message' => "Retrying {$retryCount} failed emails.",
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrying failed emails: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Preview campaign with target recipients
     */
    public function preview(EmailCampaign $campaign)
    {
        $targetRecipients = $campaign->getTargetRecipients();
        $recipientCount = $targetRecipients->count();
        
        // Sample recipient for preview
        $sampleRecipient = $targetRecipients->first();
        
        return view('admin.email.campaigns.preview', compact('campaign', 'recipientCount', 'sampleRecipient'));
    }

    /**
     * Get recipient count for campaign creation
     */
    public function getRecipientCount(Request $request)
    {
        try {
            $recipientType = $request->recipient_type;
            $criteria = $request->recipient_criteria ?? [];

            $query = User::query();

            // Filter by recipient type
            if ($recipientType !== 'all') {
                $query->where('role', $recipientType);
            }

            // Apply additional criteria
            if (!empty($criteria['active_only'])) {
                $query->where('is_active', true);
            }

            if (!empty($criteria['has_bookings'])) {
                $query->whereHas('bookings');
            }

            if (!empty($criteria['exclude_unsubscribed'])) {
                $query->where('email_notifications', true);
            }

            if (!empty($criteria['registration_date_from'])) {
                $query->whereDate('created_at', '>=', $criteria['registration_date_from']);
            }

            if (!empty($criteria['registration_date_to'])) {
                $query->whereDate('created_at', '<=', $criteria['registration_date_to']);
            }

            if (!empty($criteria['last_booking_from'])) {
                $query->whereHas('bookings', function($q) use ($criteria) {
                    $q->whereDate('created_at', '>=', $criteria['last_booking_from']);
                });
            }

            $count = $query->count();

            return response()->json([
                'success' => true,
                'count' => $count,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating recipient count: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get campaign statistics for AJAX
     */
    public function getStatistics(EmailCampaign $campaign)
    {
        try {
            $campaign->updateStatistics();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_recipients' => $campaign->total_recipients,
                    'sent_count' => $campaign->sent_count,
                    'delivered_count' => $campaign->delivered_count,
                    'failed_count' => $campaign->failed_count,
                    'bounce_count' => $campaign->bounce_count,
                    'open_count' => $campaign->open_count,
                    'click_count' => $campaign->click_count,
                    'delivery_rate' => $campaign->delivery_rate,
                    'open_rate' => $campaign->open_rate,
                    'click_rate' => $campaign->click_rate,
                    'bounce_rate' => $campaign->bounce_rate,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting campaign statistics: ' . $e->getMessage(),
            ], 500);
        }
    }
}
