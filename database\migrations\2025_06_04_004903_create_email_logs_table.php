<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // contact_form, corporate_inquiry, booking_confirmation, etc.
            $table->enum('direction', ['sent', 'received', 'bounced', 'failed']);
            $table->string('from_email')->nullable();
            $table->string('to_email')->nullable();
            $table->string('subject')->nullable();
            $table->text('content')->nullable();
            $table->enum('status', ['pending', 'sent', 'delivered', 'bounced', 'failed', 'processed'])->default('pending');
            $table->string('related_type')->nullable(); // Model type (Booking, ContactSubmission, etc.)
            $table->unsignedBigInteger('related_id')->nullable(); // Model ID
            $table->json('metadata')->nullable(); // Additional data (IP, user agent, etc.)
            $table->text('error_message')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('bounced_at')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['type', 'direction', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['related_type', 'related_id']);
            $table->index(['from_email', 'created_at']);
            $table->index(['to_email', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_logs');
    }
};
