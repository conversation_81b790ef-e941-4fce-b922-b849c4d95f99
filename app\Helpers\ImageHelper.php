<?php

namespace App\Helpers;

use App\Services\StorageService;
use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    /**
     * Get image URL with fallback.
     *
     * @param string|null $imagePath
     * @param string $type
     * @param string $disk
     * @return string
     */
    public static function getImageUrl(?string $imagePath, string $type = 'default', string $disk = 'public'): string
    {
        if (!$imagePath) {
            return self::getPlaceholderUrl($type);
        }

        try {
            if (Storage::disk($disk)->exists($imagePath)) {
                // Add cache busting parameter to force browser refresh
                $timestamp = Storage::disk($disk)->lastModified($imagePath);
                return asset('storage/' . $imagePath . '?v=' . $timestamp);
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get image URL', [
                'path' => $imagePath,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }

        return self::getPlaceholderUrl($type);
    }

    /**
     * Get placeholder URL based on type.
     *
     * @param string $type
     * @return string
     */
    public static function getPlaceholderUrl(string $type): string
    {
        $placeholders = [
            'vehicle' => 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'profile' => 'https://ui-avatars.com/api/?name=User&background=667eea&color=fff&size=150',
            'logo' => asset('images/logo-placeholder.png'),
            'favicon' => asset('favicon.ico'),
            'document' => 'https://via.placeholder.com/300x200?text=Document',
            'default' => 'https://via.placeholder.com/300x200?text=No+Image'
        ];

        return $placeholders[$type] ?? $placeholders['default'];
    }

    /**
     * Get vehicle image URL.
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getVehicleImageUrl(?string $imagePath): string
    {
        return self::getImageUrl($imagePath, 'vehicle');
    }

    /**
     * Get profile image URL.
     *
     * @param string|null $imagePath
     * @param string|null $userName
     * @return string
     */
    public static function getProfileImageUrl(?string $imagePath, ?string $userName = null): string
    {
        if (!$imagePath) {
            if ($userName) {
                return 'https://ui-avatars.com/api/?name=' . urlencode($userName) . '&background=667eea&color=fff&size=150';
            }
            return self::getPlaceholderUrl('profile');
        }

        return self::getImageUrl($imagePath, 'profile');
    }

    /**
     * Get logo image URL.
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getLogoImageUrl(?string $imagePath): string
    {
        return self::getImageUrl($imagePath, 'logo');
    }

    /**
     * Get favicon URL.
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getFaviconUrl(?string $imagePath): string
    {
        return self::getImageUrl($imagePath, 'favicon');
    }

    /**
     * Get document preview URL.
     *
     * @param string|null $documentPath
     * @return string
     */
    public static function getDocumentPreviewUrl(?string $documentPath): string
    {
        if (!$documentPath) {
            return self::getPlaceholderUrl('document');
        }

        $extension = strtolower(pathinfo($documentPath, PATHINFO_EXTENSION));
        
        // For images, return the image URL
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            return self::getImageUrl($documentPath, 'document');
        }

        // For PDFs, return the PDF URL for iframe viewing
        if ($extension === 'pdf') {
            try {
                if (Storage::disk('public')->exists($documentPath)) {
                    $timestamp = Storage::disk('public')->lastModified($documentPath);
                    return asset('storage/' . $documentPath . '?v=' . $timestamp);
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to get PDF URL', ['path' => $documentPath]);
            }
        }

        return self::getPlaceholderUrl('document');
    }

    /**
     * Check if image exists.
     *
     * @param string|null $imagePath
     * @param string $disk
     * @return bool
     */
    public static function imageExists(?string $imagePath, string $disk = 'public'): bool
    {
        if (!$imagePath) {
            return false;
        }

        try {
            return Storage::disk($disk)->exists($imagePath);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get image dimensions.
     *
     * @param string $imagePath
     * @param string $disk
     * @return array|null
     */
    public static function getImageDimensions(string $imagePath, string $disk = 'public'): ?array
    {
        try {
            if (!Storage::disk($disk)->exists($imagePath)) {
                return null;
            }

            $fullPath = Storage::disk($disk)->path($imagePath);
            $imageInfo = getimagesize($fullPath);

            if ($imageInfo) {
                return [
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'mime' => $imageInfo['mime']
                ];
            }
        } catch (\Exception $e) {
            \Log::error('Failed to get image dimensions', [
                'path' => $imagePath,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Generate responsive image HTML.
     *
     * @param string|null $imagePath
     * @param string $alt
     * @param string $class
     * @param string $type
     * @return string
     */
    public static function generateResponsiveImage(?string $imagePath, string $alt = '', string $class = '', string $type = 'default'): string
    {
        $imageUrl = self::getImageUrl($imagePath, $type);
        $alt = htmlspecialchars($alt);
        $class = htmlspecialchars($class);

        return "<img src=\"{$imageUrl}\" alt=\"{$alt}\" class=\"{$class}\" loading=\"lazy\">";
    }

    /**
     * Get optimized image URL for different sizes.
     *
     * @param string|null $imagePath
     * @param string $size
     * @return string
     */
    public static function getOptimizedImageUrl(?string $imagePath, string $size = 'medium'): string
    {
        if (!$imagePath) {
            return self::getPlaceholderUrl('default');
        }

        // For now, return the original image URL
        // In the future, this could return different sized versions
        return self::getImageUrl($imagePath);
    }
}
