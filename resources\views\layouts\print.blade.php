<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') - {{ config('app.name', 'YNR CARS') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #fff;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .print-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .company-logo {
            font-size: 24px;
            font-weight: 700;
            color: #343a40;
        }

        .company-logo span {
            color: #ee393d;
        }

        .company-info {
            text-align: right;
            font-size: 14px;
        }

        .document-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #343a40;
            text-align: center;
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .client-info, .document-details {
            flex: 1;
        }

        .info-label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #6c757d;
        }

        .info-value {
            margin-bottom: 15px;
        }

        .table {
            width: 100%;
            margin-bottom: 30px;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #343a40;
        }

        .table-bordered th, .table-bordered td {
            border: 1px solid #dee2e6;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .totals {
            width: 300px;
            margin-left: auto;
            margin-bottom: 30px;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .totals-row:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 18px;
            color: #343a40;
        }

        .totals-label {
            font-weight: 600;
        }

        .notes {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .notes-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #343a40;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }

        .print-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 15px;
            border-radius: 5px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
        }

        .btn-print {
            background-color: #ee393d;
            color: #343a40;
            border: none;
        }

        .btn-download {
            background-color: #343a40;
            color: #fff;
            border: none;
        }

        .btn-back {
            background-color: #6c757d;
            color: #fff;
            border: none;
        }

        @media print {
            .print-buttons {
                display: none;
            }

            body {
                padding: 0;
                margin: 0;
            }

            .print-container {
                width: 100%;
                max-width: none;
                padding: 0;
                margin: 0;
            }

            @page {
                size: A4;
                margin: 1.5cm;
            }
        }

        @yield('styles')
    </style>
</head>
<body>
    <div class="print-buttons">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print me-1"></i> Print
        </button>
        <a href="@yield('download-url', '#')" class="btn btn-download">
            <i class="fas fa-download me-1"></i> Download PDF
        </a>
        <a href="@yield('back-url', 'javascript:history.back()')" class="btn btn-back">
            <i class="fas fa-arrow-left me-1"></i> Back
        </a>
    </div>

    <div class="print-container">
        <div class="print-header">
            <div class="company-logo">
                @php
                    $logoPath = \App\Services\SettingsService::get('logo', '');
                    $logoExists = $logoPath && \App\Services\StorageService::fileExists($logoPath, 'public');
                @endphp

                @if($logoExists)
                    <img src="{{ asset('storage/' . $logoPath) }}" alt="{{ config('app.name', 'YNR Cars') }}" style="max-height: 60px;">
                @else
                    {{ config('app.name', 'YNR Cars') }}
                @endif
            </div>
            <div class="company-info">
                <div>{{ \App\Services\SettingsService::get('company_address', 'Company Address') }}</div>
                <div>{{ \App\Services\SettingsService::get('company_phone', 'Company Phone') }}</div>
                <div>{{ \App\Services\SettingsService::get('company_email', 'Company Email') }}</div>
            </div>
        </div>

        @yield('content')

        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ \App\Services\SettingsService::get('company_name', 'YNR CARS') }}. All rights reserved.</p>
            <p>This is a computer-generated document. No signature is required.</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    @yield('scripts')
</body>
</html>
