<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add via stops fields
            $table->json('via_stops')->nullable()->after('dropoff_lng');
            $table->decimal('via_charges', 10, 2)->default(0)->after('amount');
            $table->integer('via_count')->default(0)->after('via_charges');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn(['via_stops', 'via_charges', 'via_count']);
        });
    }
};
