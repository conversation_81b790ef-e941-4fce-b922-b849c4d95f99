@extends('layouts.admin')

@section('title', 'Create New User')

@section('styles')
<style>
    /* Main Layout */
    .content-wrapper {
        padding: 30px;
        background: #f8f9fa;
        min-height: calc(100vh - 76px);
    }

    /* Header Section */
    .page-header {
        background: #ee393d;
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .page-header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
    }

    .page-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    /* Form Container */
    .form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .form-header {
        background: #ee393d;
        color: white;
        padding: 25px 30px;
        margin: 0;
    }

    .form-header h3 {
        margin: 0;
        font-weight: 600;
    }

    .form-body {
        padding: 40px;
    }

    /* Form Fields */
    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        background: #f8f9fa;
        border-radius: 15px;
        border-left: 4px solid #ee393d;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 10px;
        color: #ee393d;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .form-label i {
        margin-right: 8px;
        color: #ee393d;
        width: 16px;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Photo Upload */
    .photo-upload {
        text-align: center;
        padding: 30px;
        border: 2px dashed #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .photo-upload:hover {
        border-color: #ee393d;
        background: #f8f9ff;
    }

    .photo-upload.has-image {
        border-color: #28a745;
        background: #f8fff8;
    }

    .upload-icon {
        font-size: 3rem;
        color: #bdc3c7;
        margin-bottom: 15px;
    }

    .upload-text {
        color: #7f8c8d;
        margin-bottom: 10px;
    }

    .preview-container {
        margin-top: 20px;
        display: none;
    }

    .preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    /* Toggle Switch */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #ee393d;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    /* Action Buttons */
    .form-actions {
        background: #f8f9fa;
        padding: 25px 40px;
        margin: 0 -40px -40px -40px;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn-custom {
        padding: 12px 30px;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-primary-custom {
        background: #ee393d;
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary-custom {
        background: #6c757d;
        color: white;
    }

    .btn-secondary-custom:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .content-wrapper {
            padding: 20px;
        }

        .form-body {
            padding: 25px;
        }

        .form-actions {
            flex-direction: column;
            gap: 15px;
        }
    }
</style>
@endsection

@section('content')
<div class="content-wrapper">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-user-plus me-3"></i>Create New User</h1>
                <p>Add a new user to your taxi booking system</p>
            </div>
            <div>
                <a href="{{ route('admin.users.index') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Form Container -->
    <div class="form-container">
        <div class="form-header">
            <h3><i class="fas fa-user-circle me-2"></i>User Information</h3>
        </div>

        <div class="form-body">
            <!-- Form Steps -->

            <form action="{{ route('admin.users.store') }}" method="POST" enctype="multipart/form-data" id="userForm">
                @csrf

                <!-- Step 1: Basic Information -->
                <div class="form-section" id="section1">
                    <div class="section-title">
                        <i class="fas fa-user"></i>Basic Information
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i>Full Name
                                </label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ old('name') }}" required placeholder="Enter full name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i>Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ old('email') }}" required placeholder="Enter email address">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i>Phone Number
                                </label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="{{ old('phone') }}" placeholder="Enter phone number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i>Address
                                </label>
                                <input type="text" class="form-control" id="address" name="address"
                                       value="{{ old('address') }}" placeholder="Enter address">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Account & Role -->
                <div class="form-section" id="section2">
                    <div class="section-title">
                        <i class="fas fa-key"></i>Account & Role
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i>Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password"
                                       required placeholder="Enter password">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password_confirmation" class="form-label">
                                    <i class="fas fa-lock"></i>Confirm Password
                                </label>
                                <input type="password" class="form-control" id="password_confirmation"
                                       name="password_confirmation" required placeholder="Confirm password">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Profile & Settings -->
                <div class="form-section" id="section3">
                    <div class="section-title">
                        <i class="fas fa-image"></i>Profile Photo & Settings
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-camera"></i>Profile Photo
                        </label>
                        <div class="photo-upload" onclick="document.getElementById('profile_photo').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">Click to upload profile photo</div>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF (Max: 2MB)</small>
                            <input type="file" id="profile_photo" name="profile_photo" accept="image/*"
                                   style="display: none;" onchange="previewImage(this)">
                        </div>
                        <div class="preview-container" id="previewContainer">
                            <img id="preview" class="preview-image" src="#" alt="Preview">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-toggle-on"></i>Account Status
                        </label>
                        <div class="d-flex align-items-center">
                            <label class="toggle-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" id="is_active" name="is_active" value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <span class="slider"></span>
                            </label>
                            <span class="ms-3">Account is active</span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary-custom btn-custom">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary-custom btn-custom">
                        <i class="fas fa-user-plus me-2"></i>Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // Initialize form

        // Form validation
        $('#userForm').on('submit', function(e) {
            e.preventDefault();

            // Basic validation
            if (!validateForm()) {
                return false;
            }

            // Show loading
            Swal.fire({
                title: 'Creating User...',
                text: 'Please wait while we create the user account.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit form
            this.submit();
        });

        // Real-time validation
        $('input[required]').on('blur', function() {
            validateField($(this));
        });

        // Password confirmation validation
        $('#password_confirmation').on('input', function() {
            const password = $('#password').val();
            const confirmation = $(this).val();

            if (confirmation && password !== confirmation) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">Passwords do not match</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        // Email validation
        $('#email').on('blur', function() {
            const email = $(this).val();
            if (email) {
                checkEmailAvailability(email);
            }
        });
    });

    function previewImage(input) {
        const preview = document.getElementById('preview');
        const previewContainer = document.getElementById('previewContainer');
        const photoUpload = document.querySelector('.photo-upload');

        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Validate file size (2MB)
            if (file.size > 2 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Please select an image smaller than 2MB.'
                });
                input.value = '';
                return;
            }

            // Validate file type
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: 'Please select a valid image file.'
                });
                input.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                previewContainer.style.display = 'block';
                photoUpload.classList.add('has-image');
            }
            reader.readAsDataURL(file);
        } else {
            previewContainer.style.display = 'none';
            photoUpload.classList.remove('has-image');
        }
    }

    function validateForm() {
        let isValid = true;

        // Clear previous validation
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // Validate required fields
        $('input[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                $(this).after('<div class="invalid-feedback">This field is required</div>');
                isValid = false;
            }
        });

        // Validate password confirmation
        const password = $('#password').val();
        const confirmation = $('#password_confirmation').val();
        if (password !== confirmation) {
            $('#password_confirmation').addClass('is-invalid');
            $('#password_confirmation').after('<div class="invalid-feedback">Passwords do not match</div>');
            isValid = false;
        }

        // Validate email format
        const email = $('#email').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('#email').addClass('is-invalid');
            $('#email').after('<div class="invalid-feedback">Please enter a valid email address</div>');
            isValid = false;
        }

        return isValid;
    }

    function validateField(field) {
        const value = field.val().trim();

        if (field.prop('required') && !value) {
            field.addClass('is-invalid');
            if (!field.next('.invalid-feedback').length) {
                field.after('<div class="invalid-feedback">This field is required</div>');
            }
        } else {
            field.removeClass('is-invalid');
            field.next('.invalid-feedback').remove();
        }
    }

    function checkEmailAvailability(email) {
        $.ajax({
            url: '{{ route("admin.users.check-email") }}',
            method: 'POST',
            data: {
                email: email,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (!response.available) {
                    $('#email').addClass('is-invalid');
                    $('#email').next('.invalid-feedback').remove();
                    $('#email').after('<div class="invalid-feedback">This email is already taken</div>');
                } else {
                    $('#email').removeClass('is-invalid');
                    $('#email').next('.invalid-feedback').remove();
                }
            },
            error: function() {
                // Silently fail - don't block form submission
            }
        });
    }

    // Auto-generate username from name
    $('#name').on('input', function() {
        const name = $(this).val();
        const username = name.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
        // You can use this for username field if you have one
    });

    // Show password strength
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = getPasswordStrength(password);

        // Remove existing strength indicator
        $(this).next('.password-strength').remove();

        if (password.length > 0) {
            const strengthHtml = `
                <div class="password-strength mt-1">
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar bg-${strength.color}" style="width: ${strength.percentage}%"></div>
                    </div>
                    <small class="text-${strength.color}">${strength.text}</small>
                </div>
            `;
            $(this).after(strengthHtml);
        }
    });

    function getPasswordStrength(password) {
        let score = 0;

        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;

        switch (score) {
            case 0:
            case 1:
                return { color: 'danger', percentage: 20, text: 'Very Weak' };
            case 2:
                return { color: 'warning', percentage: 40, text: 'Weak' };
            case 3:
                return { color: 'info', percentage: 60, text: 'Fair' };
            case 4:
                return { color: 'primary', percentage: 80, text: 'Good' };
            case 5:
                return { color: 'success', percentage: 100, text: 'Strong' };
            default:
                return { color: 'secondary', percentage: 0, text: '' };
        }
    }
</script>
@endsection
