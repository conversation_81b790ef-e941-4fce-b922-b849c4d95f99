@extends('layouts.driver')

@section('title', 'Edit Profile')

@section('styles')
<style>
    /* Profile Styles */
    .profile-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .profile-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .profile-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .profile-card .card-body {
        padding: 25px;
    }

    .profile-photo-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 0 auto 20px;
    }

    .profile-photo {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
        border: 5px solid #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .profile-photo-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background-color: #ee393d;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        font-weight: 600;
        border: 5px solid #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .profile-photo-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 40px;
        height: 40px;
        background-color: #ee393d;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border: 3px solid #fff;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .profile-photo-edit:hover {
        background-color: #d31a1e;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
    }

    .form-control {
        border-radius: 10px;
        padding: 12px 15px;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .form-control:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
    }

    .form-select {
        border-radius: 10px;
        padding: 12px 15px;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .form-select:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
    }

    .form-check-input:checked {
        background-color: #ee393d;
        border-color: #ee393d;
    }

    .form-check-input:focus {
        border-color: #ee393d;
        box-shadow: 0 0 0 0.25rem rgba(238, 57, 61, 0.25);
    }

    .btn-primary {
        background-color: #ee393d;
        border-color: #ee393d;
    }

    .btn-primary:hover {
        background-color: #d31a1e;
        border-color: #d31a1e;
    }

    .btn-outline-primary {
        color: #ee393d;
        border-color: #ee393d;
    }

    .btn-outline-primary:hover {
        background-color: #ee393d;
        border-color: #ee393d;
    }

    .section-title {
        font-weight: 600;
        color: #343a40;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #ee393d;
        display: inline-block;
    }

    .vehicle-info-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .vehicle-info-card h6 {
        font-weight: 600;
        color: #343a40;
        margin-bottom: 15px;
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .info-item i {
        width: 25px;
        color: #ee393d;
        margin-right: 10px;
    }

    .info-item span {
        color: #495057;
    }

    .preview-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 10px;
        display: none;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .profile-card {
            margin-bottom: 20px;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-edit me-2 text-primary"></i> Edit Profile
        </h1>
        <a href="{{ route('driver.dashboard') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <!-- Personal Information -->
        <div class="col-lg-8">
            <div class="profile-card">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i> Personal Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('driver.profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="text-center mb-4">
                            <div class="profile-photo-container">
                                @if($user->profile_photo)
                                    <img src="{{ asset('storage/' . $user->profile_photo) }}" alt="{{ $user->name }}" class="profile-photo" id="preview-current">
                                @else
                                    <div class="profile-photo-placeholder" id="preview-placeholder">
                                        {{ strtoupper(substr($user->name, 0, 1)) }}
                                    </div>
                                @endif
                                <label for="profile_photo" class="profile-photo-edit" title="Change Photo">
                                    <i class="fas fa-camera"></i>
                                    <input type="file" id="profile_photo" name="profile_photo" class="d-none" accept="image/*">
                                </label>
                            </div>
                            <img id="preview-new" class="preview-image mx-auto" src="#" alt="Profile Preview">
                            <h5 class="mb-1">{{ $user->name }}</h5>
                            <p class="text-muted">Driver since {{ $user->created_at->format('M d, Y') }}</p>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="license_number" class="form-label">License Number</label>
                                <input type="text" class="form-control @error('license_number') is-invalid @enderror" id="license_number" name="license_number" value="{{ old('license_number', $user->license_number) }}" required>
                                @error('license_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" id="address" name="address" rows="3" required>{{ old('address', $user->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="hidden" name="is_available" value="0">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available" value="1" {{ $user->is_available ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_available">
                                    I am available to accept rides
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Vehicle Information -->
            <div class="profile-card">
                <div class="card-header">
                    <h5><i class="fas fa-car me-2"></i> Vehicle Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('driver.vehicle.update') }}" method="POST">
                        @csrf
                        @method('PUT')
                        <!-- Using PUT method for updating vehicle information -->

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="vehicle_make" class="form-label">Vehicle Make</label>
                                <input type="text" class="form-control @error('vehicle_make') is-invalid @enderror" id="vehicle_make" name="vehicle_make" value="{{ old('vehicle_make', $user->vehicle_make) }}" required>
                                @error('vehicle_make')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="vehicle_model" class="form-label">Vehicle Model</label>
                                <input type="text" class="form-control @error('vehicle_model') is-invalid @enderror" id="vehicle_model" name="vehicle_model" value="{{ old('vehicle_model', $user->vehicle_model) }}" required>
                                @error('vehicle_model')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="vehicle_color" class="form-label">Vehicle Color</label>
                                <input type="text" class="form-control @error('vehicle_color') is-invalid @enderror" id="vehicle_color" name="vehicle_color" value="{{ old('vehicle_color', $user->vehicle_color) }}" required>
                                @error('vehicle_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="vehicle_reg_number" class="form-label">Registration Number</label>
                                <input type="text" class="form-control @error('vehicle_reg_number') is-invalid @enderror" id="vehicle_reg_number" name="vehicle_reg_number" value="{{ old('vehicle_reg_number', $user->vehicle_reg_number) }}" required>
                                @error('vehicle_reg_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="insurance_expiry" class="form-label">Insurance Expiry Date</label>
                                <input type="date" class="form-control @error('insurance_expiry') is-invalid @enderror" id="insurance_expiry" name="insurance_expiry" value="{{ old('insurance_expiry', $user->insurance_expiry ? $user->insurance_expiry->format('Y-m-d') : '') }}" required>
                                @error('insurance_expiry')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="mot_expiry" class="form-label">MOT Expiry Date</label>
                                <input type="date" class="form-control @error('mot_expiry') is-invalid @enderror" id="mot_expiry" name="mot_expiry" value="{{ old('mot_expiry', $user->mot_expiry ? $user->mot_expiry->format('Y-m-d') : '') }}" required>
                                @error('mot_expiry')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="vehicle_info" class="form-label">Additional Vehicle Information</label>
                            <textarea class="form-control @error('vehicle_info') is-invalid @enderror" id="vehicle_info" name="vehicle_info" rows="3">{{ old('vehicle_info', $user->vehicle_info) }}</textarea>
                            @error('vehicle_info')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Vehicle Information
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Account Security -->
            <div class="profile-card">
                <div class="card-header">
                    <h5><i class="fas fa-lock me-2"></i> Account Security</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('driver.password.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key me-1"></i> Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Document Status -->
            <div class="profile-card">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt me-2"></i> Document Status</h5>
                </div>
                <div class="card-body">
                    @php
                        $documentTypes = [
                            'Driver License' => ['icon' => 'id-card', 'count' => 0, 'expired' => false],
                            'Driver PHD License' => ['icon' => 'id-badge', 'count' => 0, 'expired' => false],
                            'Vehicle PHD License' => ['icon' => 'car', 'count' => 0, 'expired' => false],
                            'Insurance' => ['icon' => 'shield-alt', 'count' => 0, 'expired' => false],
                            'MOT Certificate' => ['icon' => 'check-circle', 'count' => 0, 'expired' => false],
                            'V5C Logbook' => ['icon' => 'book', 'count' => 0, 'expired' => false],
                            'Vehicle Photos' => ['icon' => 'images', 'count' => 0, 'expired' => false],
                        ];

                        foreach($user->driverDocuments as $document) {
                            if (isset($documentTypes[$document->document_type])) {
                                $documentTypes[$document->document_type]['count']++;
                                if ($document->isExpired()) {
                                    $documentTypes[$document->document_type]['expired'] = true;
                                }
                            }
                        }
                    @endphp

                    <ul class="list-group">
                        @foreach($documentTypes as $type => $info)
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-{{ $info['icon'] }} text-primary me-2"></i> {{ $type }}
                                </div>
                                @if($info['count'] > 0)
                                    @if($info['expired'])
                                        <span class="badge bg-danger rounded-pill">Expired</span>
                                    @else
                                        <span class="badge bg-success rounded-pill">Uploaded</span>
                                    @endif
                                @else
                                    <span class="badge bg-warning rounded-pill">Missing</span>
                                @endif
                            </li>
                        @endforeach
                    </ul>

                    <div class="d-grid mt-3">
                        <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-file-upload me-1"></i> Manage Documents
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Preview profile photo before upload
    document.getElementById('profile_photo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Hide current photo or placeholder
                const currentPhoto = document.getElementById('preview-current');
                const placeholder = document.getElementById('preview-placeholder');

                if (currentPhoto) {
                    currentPhoto.style.display = 'none';
                }

                if (placeholder) {
                    placeholder.style.display = 'none';
                }

                // Show new preview
                const previewNew = document.getElementById('preview-new');
                previewNew.src = e.target.result;
                previewNew.style.display = 'block';
            }
            reader.readAsDataURL(file);
        }
    });
</script>
@endsection
