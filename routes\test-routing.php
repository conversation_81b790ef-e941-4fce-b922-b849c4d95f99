<?php

use Illuminate\Support\Facades\Route;
use App\Services\GoogleMapsService;

/*
|--------------------------------------------------------------------------
| Test Routing Routes
|--------------------------------------------------------------------------
|
| These routes are for testing the highway preference routing functionality.
| Remove this file after testing is complete.
|
*/

Route::get('/test-routing', function () {
    $googleMapsService = new GoogleMapsService();
    
    // Test route preferences
    $preferences = $googleMapsService->getRoutePreferences();
    
    // Test distance calculation with highway preference
    $origin = "New York, NY";
    $destination = "Philadelphia, PA";
    
    $distanceMatrix = $googleMapsService->getDistanceMatrix($origin, $destination);
    $directions = $googleMapsService->getDirectionsWithHighwayPreference($origin, $destination);
    
    return response()->json([
        'message' => 'Highway preference routing test',
        'route_preferences' => $preferences,
        'test_route' => [
            'origin' => $origin,
            'destination' => $destination,
            'distance_matrix' => $distanceMatrix,
            'directions' => $directions,
        ],
        'settings' => [
            'prefer_highways' => \App\Helpers\SettingsHelper::get('prefer_highways', 'true'),
            'avoid_tolls' => \App\Helpers\SettingsHelper::get('avoid_tolls', 'false'),
            'avoid_ferries' => \App\Helpers\SettingsHelper::get('avoid_ferries', 'true'),
            'optimize_waypoints' => \App\Helpers\SettingsHelper::get('optimize_waypoints', 'true'),
        ]
    ]);
});

Route::get('/test-routing-frontend', function () {
    return view('test-routing');
});
