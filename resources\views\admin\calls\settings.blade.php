@extends('layouts.admin')

@section('title', 'Call System Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Call System Settings</h1>
                <a href="{{ route('admin.calls.dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Notification Settings -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell"></i> Notification Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="notificationSettings">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="soundEnabled" checked>
                                <label class="form-check-label" for="soundEnabled">
                                    <strong>Sound Alerts</strong><br>
                                    <small class="text-muted">Play sound when calls are received</small>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="browserNotifications" checked>
                                <label class="form-check-label" for="browserNotifications">
                                    <strong>Browser Notifications</strong><br>
                                    <small class="text-muted">Show desktop notifications for incoming calls</small>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                <label class="form-check-label" for="autoRefresh">
                                    <strong>Auto Refresh</strong><br>
                                    <small class="text-muted">Automatically refresh call data every 5 seconds</small>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Notification Duration</label>
                            <select class="form-select" id="notificationDuration">
                                <option value="5">5 seconds</option>
                                <option value="10">10 seconds</option>
                                <option value="15" selected>15 seconds</option>
                                <option value="30">30 seconds</option>
                                <option value="60">1 minute</option>
                            </select>
                            <small class="text-muted">How long to show call notifications</small>
                        </div>

                        <button type="button" class="btn btn-primary" onclick="saveNotificationSettings()">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="testNotification()">
                            <i class="fas fa-test-tube"></i> Test Notification
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- CircleLoop Configuration -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog"></i> CircleLoop Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Webhook URL</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ url('/api/circleloop/webhook') }}" readonly>
                            <button class="btn btn-outline-secondary" onclick="copyToClipboard(this.previousElementSibling.value)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <small class="text-muted">Use this URL in your CircleLoop webhook settings</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">HTTP Method</label>
                        <input type="text" class="form-control" value="POST" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Events to Enable</label>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>New Phonecalls</strong><br>
                                    <small class="text-muted">call_ringing, call_missed events</small>
                                </div>
                                <span class="badge bg-success">Required</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>New Voicemails</strong><br>
                                    <small class="text-muted">Voicemail notifications</small>
                                </div>
                                <span class="badge bg-secondary">Optional</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="testWebhookConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                        <a href="https://app.circleloop.com/settings/notifications" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> Open CircleLoop Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Call Handling Rules -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-rules"></i> Call Handling Rules
                    </h5>
                </div>
                <div class="card-body">
                    <form id="handlingRules">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Auto-identify Clients</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoIdentify" checked>
                                        <label class="form-check-label" for="autoIdentify">
                                            Automatically match phone numbers to existing clients
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Auto-mark Handled</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoMarkHandled">
                                        <label class="form-check-label" for="autoMarkHandled">
                                            Mark calls as handled after 5 minutes
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Follow-up Reminders</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="followUpReminders" checked>
                                        <label class="form-check-label" for="followUpReminders">
                                            Send reminders for follow-up calls
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Default Call Status</label>
                                    <select class="form-select" id="defaultStatus">
                                        <option value="ringing" selected>Ringing</option>
                                        <option value="answered">Answered</option>
                                        <option value="missed">Missed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Call Retention Period</label>
                                    <select class="form-select" id="retentionPeriod">
                                        <option value="30">30 days</option>
                                        <option value="90" selected>90 days</option>
                                        <option value="180">6 months</option>
                                        <option value="365">1 year</option>
                                        <option value="0">Never delete</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-primary" onclick="saveHandlingRules()">
                            <i class="fas fa-save"></i> Save Rules
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-heartbeat"></i> System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="status-indicator bg-success mb-2"></div>
                                <h6>Webhook Endpoint</h6>
                                <small class="text-muted">Active and receiving calls</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="status-indicator bg-success mb-2"></div>
                                <h6>Database</h6>
                                <small class="text-muted">Connected and operational</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="status-indicator bg-warning mb-2"></div>
                                <h6>Real-time Updates</h6>
                                <small class="text-muted">Polling every 5 seconds</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="status-indicator bg-info mb-2"></div>
                                <h6>Last Call</h6>
                                <small class="text-muted" id="lastCallTime">Checking...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 auto;
    position: relative;
}

.status-indicator.bg-success::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}
</style>
@endpush

@push('scripts')
<script>
// Load current settings
document.addEventListener('DOMContentLoaded', function() {
    loadCurrentSettings();
    updateLastCallTime();
    setInterval(updateLastCallTime, 30000); // Update every 30 seconds
});

function loadCurrentSettings() {
    // Load from localStorage or defaults
    document.getElementById('soundEnabled').checked = localStorage.getItem('call_sound_enabled') !== 'false';
    document.getElementById('browserNotifications').checked = localStorage.getItem('call_notification_enabled') !== 'false';
    document.getElementById('autoRefresh').checked = localStorage.getItem('call_auto_refresh') !== 'false';
    
    const duration = localStorage.getItem('call_notification_duration') || '15';
    document.getElementById('notificationDuration').value = duration;
}

// Ensure functions are in global scope
window.saveNotificationSettings = function() {
    const settings = {
        sound: document.getElementById('soundEnabled').checked,
        notifications: document.getElementById('browserNotifications').checked,
        autoRefresh: document.getElementById('autoRefresh').checked,
        duration: document.getElementById('notificationDuration').value
    };

    // Save to localStorage
    localStorage.setItem('call_sound_enabled', settings.sound);
    localStorage.setItem('call_notification_enabled', settings.notifications);
    localStorage.setItem('call_auto_refresh', settings.autoRefresh);
    localStorage.setItem('call_notification_duration', settings.duration);

    // Update notification system if available
    if (window.callNotificationSystem) {
        if (settings.sound) {
            window.callNotificationSystem.enableSound();
        } else {
            window.callNotificationSystem.disableSound();
        }

        if (settings.notifications) {
            window.callNotificationSystem.enableNotifications();
        } else {
            window.callNotificationSystem.disableNotifications();
        }
    }

    showAlert('Settings saved successfully!', 'success');
};

window.saveHandlingRules = function() {
    const rules = {
        autoIdentify: document.getElementById('autoIdentify').checked,
        autoMarkHandled: document.getElementById('autoMarkHandled').checked,
        followUpReminders: document.getElementById('followUpReminders').checked,
        defaultStatus: document.getElementById('defaultStatus').value,
        retentionPeriod: document.getElementById('retentionPeriod').value
    };

    // Save rules (would typically send to server)
    localStorage.setItem('call_handling_rules', JSON.stringify(rules));
    showAlert('Handling rules saved successfully!', 'success');
};

window.testNotification = function() {
    if (window.callNotificationSystem) {
        // Simulate a test call
        const testCall = {
            caller_number: '+44 ************',
            client: {
                name: 'Test Client'
            }
        };

        window.callNotificationSystem.handleNewCall(testCall);
    } else {
        showAlert('Test notification sent!', 'info');
    }
};

window.testWebhookConnection = function() {
    fetch('/api/circleloop/test')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Webhook connection successful!', 'success');
            } else {
                showAlert('Webhook connection failed!', 'danger');
            }
        })
        .catch(error => {
            showAlert('Failed to test webhook connection!', 'danger');
        });
};

window.copyToClipboard = function(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('Copied to clipboard!', 'success');
    });
};

function updateLastCallTime() {
    fetch('/admin/calls/stats')
        .then(response => response.json())
        .then(data => {
            const lastCallElement = document.getElementById('lastCallTime');
            if (data.recent_calls && data.recent_calls.length > 0) {
                lastCallElement.textContent = data.recent_calls[0].time_ago;
            } else {
                lastCallElement.textContent = 'No calls yet';
            }
        })
        .catch(error => {
            console.error('Failed to update last call time:', error);
        });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
@endpush
