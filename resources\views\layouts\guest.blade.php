<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="currency-symbol" content="{{ $currencySymbol ?? \App\Services\SettingsService::getCurrencySymbol() }}">
    <meta name="currency-code" content="{{ $currencyCode ?? \App\Services\SettingsService::getCurrencyCode() }}">
    <meta name="distance-unit" content="{{ \App\Services\SettingsService::getDistanceUnit() }}">

    <!-- Autocomplete Settings -->
    @php
        $autocompleteSettings = \App\Services\SettingsService::getAutocompleteSettings();
    @endphp
    <meta name="autocomplete-enabled" content="{{ $autocompleteSettings['enabled'] ? 'true' : 'false' }}">
    <meta name="autocomplete-restrict-country" content="{{ $autocompleteSettings['restrict_country'] ? 'true' : 'false' }}">
    <meta name="autocomplete-country" content="{{ $autocompleteSettings['country'] }}">
    <meta name="autocomplete-types" content="{{ $autocompleteSettings['types'] }}">
    <meta name="autocomplete-bias-radius" content="{{ $autocompleteSettings['bias_radius'] }}">
    <meta name="autocomplete-use-strict-bounds" content="{{ $autocompleteSettings['use_strict_bounds'] ? 'true' : 'false' }}">
    <meta name="autocomplete-fields" content="{{ $autocompleteSettings['fields'] }}">

    <title>@yield('title') - {{ $companyName ?? config('app.name', 'YNR CARS') }}</title>

    <!-- Favicon -->
    @php
        $faviconPath = \App\Services\SettingsService::get('favicon');
        $faviconExists = $faviconPath && \App\Services\StorageService::fileExists($faviconPath, 'public');
    @endphp

    @if($faviconExists)
        <link rel="icon" href="{{ asset('storage/' . $faviconPath) }}" type="image/png">
        <link rel="shortcut icon" href="{{ asset('storage/' . $faviconPath) }}" type="image/png">
        <link rel="apple-touch-icon" href="{{ asset('storage/' . $faviconPath) }}">
        <meta name="msapplication-TileImage" content="{{ asset('storage/' . $faviconPath) }}">
    @else
        <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    <link rel="stylesheet" href="{{ asset('css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('css/pagination.css') }}">

    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            --bs-primary: {{ $primaryColor ?? '#ee393d' }};
            --bs-secondary: {{ $secondaryColor ?? '#343a40' }};
        }

        .btn-primary {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10) }};
            border-color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -10) }};
        }

        .btn-outline-primary {
            color: var(--bs-primary);
            border-color: var(--bs-primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: var(--bs-secondary);
        }

        .navbar-light {
            background-color: white;
        }

        .text-primary {
            color: var(--bs-primary) !important;
        }

        .bg-primary {
            background-color: var(--bs-primary) !important;
        }

        a {
            color: var(--bs-primary);
        }

        a:hover {
            color: {{ $colorHelper->adjustBrightness($primaryColor ?? '#ee393d', -15) }};
        }

        /* Custom preloader spinner */
        .preloader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--bs-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Logo styling */
        .navbar-logo {
            max-height: 40px;
            width: auto;
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .navbar-logo:hover {
            transform: scale(1.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
        }
    </style>

    @yield('styles')
</head>
<body>
    <!-- Preloader -->
    <div class="preloader">
        <div class="preloader-content">
            <!-- <h2 style="color: {{ $primaryColor ?? '#ee393d' }}; font-weight: 700; margin-bottom: 20px;">{{ $companyName ?? 'YNR CARS' }}</h2> -->
            <div class="preloader-spinner"></div>
        </div>
    </div>

    <header>
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="{{ route('home') }}">
                    @php
                        $logoPath = \App\Services\SettingsService::get('logo');
                        $logoExists = $logoPath && \App\Services\StorageService::fileExists($logoPath, 'public');
                    @endphp

                    @if($logoExists)
                        <img src="{{ asset('storage/' . $logoPath) }}" alt="{{ $companyName ?? config('app.name', 'YNR Cars') }}" height="40" class="navbar-logo">
                    @else
                        <span style="font-weight: bold; font-size: 24px; color: #ee393d;">YNR <span style="color: #343a40;">Cars</span></span>
                    @endif
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}" href="{{ route('services') }}">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fleet') ? 'active' : '' }}" href="{{ route('fleet') }}">Fleet</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">About Us</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">Contact</a>
                        </li>
                         <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('terms-and-conditions') ? 'active' : '' }}" href="{{ route('terms-and-conditions') }}">T&C</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        @guest
                            <a href="{{ route('login') }}" class="btn btn-link text-black text-decoration-none">Login</a>
                            <a href="{{ route('register') }}" class="btn btn-primary ms-2">Register</a>
                        @else
                            <div class="dropdown">
                                <a class="btn btn-link text-black text-decoration-none dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ Auth::user()->name }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    @if(Auth::user()->role === 'admin')
                                        <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                    @elseif(Auth::user()->role === 'client')
                                        <li><a class="dropdown-item" href="{{ route('client.dashboard') }}">Dashboard</a></li>
                                    @elseif(Auth::user()->role === 'driver')
                                        <li><a class="dropdown-item" href="{{ route('driver.dashboard') }}">Dashboard</a></li>
                                    @else
                                        <li><a class="dropdown-item" href="{{ route('dashboard') }}">Dashboard</a></li>
                                    @endif
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">Logout</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        @endguest
                    </div>
                </div>
            </div>
        </nav>

        @hasSection('page-header')
            <div class="page-header">
                <div class="container">
                    <h1>@yield('page-header')</h1>
                    @hasSection('breadcrumbs')
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                @yield('breadcrumbs')
                            </ol>
                        </nav>
                    @endif
                </div>
            </div>
        @endif
    </header>

    <main>
        @yield('content')
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5>{{ config('app.name', 'YNR CARS') }}</h5>
                    <p>YNR CARS Taxi Services for all your needs. Experience comfort, reliability, and elegance.</p>
                    <div class="social-icons">
                        @if($socialMediaSettings['facebook_url'])
                            <a href="{{ $socialMediaSettings['facebook_url'] }}" target="_blank" rel="noopener"><i class="fab fa-facebook-f"></i></a>
                        @endif
                        @if($socialMediaSettings['twitter_url'])
                            <a href="{{ $socialMediaSettings['twitter_url'] }}" target="_blank" rel="noopener"><i class="fab fa-twitter"></i></a>
                        @endif
                        @if($socialMediaSettings['instagram_url'])
                            <a href="{{ $socialMediaSettings['instagram_url'] }}" target="_blank" rel="noopener"><i class="fab fa-instagram"></i></a>
                        @endif
                        @if($socialMediaSettings['linkedin_url'])
                            <a href="{{ $socialMediaSettings['linkedin_url'] }}" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
                        @endif
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Company</h5>
                    <ul>
                        <li><a href="{{ route('about') }}">About Us</a></li>
                        <li><a href="{{ route('services') }}">Services</a></li>
                        <li><a href="{{ route('fleet') }}">Fleet</a></li>

                        <li><a href="{{ route('faq') }}">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Support</h5>
                    <ul>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                        <li><a href="{{ route('privacy-policy') }}">Privacy Policy</a></li>
                        <li><a href="{{ route('terms-and-conditions') }}">Terms and Conditions</a></li>
                        <li><a href="{{ route('faq') }}">FAQ</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Contact Us</h5>
                    <p>
                        <i class="fas fa-phone me-2"></i> <a style=" color: white;" href="tel:{{ $companyPhone }}">{{ $companyPhone }}</a><br>
                        <i class="fas fa-phone me-2"></i> <a style=" color: white;" href="tel:+44 ( 0 ) 7859286909">+44 ( 0 ) 7859286909</a><br>
                        <i class="fas fa-envelope me-2"></i> <a style=" color: white;" href="mailto:{{ $companyEmail }}">{{ $companyEmail }}</a><br>
                        <i class="fas fa-map-marker-alt me-2"></i> {{ $companyAddress }}
                    </p>
                </div>
            </div>
            <div class="text-center copyright">
                <p>&copy; {{ date('Y') }} {{ $companyName }}. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <div class="whatsapp-out">
        <div class="whatsapp">
            <a target="_blank" href="https://wa.me/447859286909"><svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M16 31C23.732 31 30 24.732 30 17C30 9.26801 23.732 3 16 3C8.26801 3 2 9.26801 2 17C2 19.5109 2.661 21.8674 3.81847 23.905L2 31L9.31486 29.3038C11.3014 30.3854 13.5789 31 16 31ZM16 28.8462C22.5425 28.8462 27.8462 23.5425 27.8462 17C27.8462 10.4576 22.5425 5.15385 16 5.15385C9.45755 5.15385 4.15385 10.4576 4.15385 17C4.15385 19.5261 4.9445 21.8675 6.29184 23.7902L5.23077 27.7692L9.27993 26.7569C11.1894 28.0746 13.5046 28.8462 16 28.8462Z" fill="#BFC8D0"></path> <path d="M28 16C28 22.6274 22.6274 28 16 28C13.4722 28 11.1269 27.2184 9.19266 25.8837L5.09091 26.9091L6.16576 22.8784C4.80092 20.9307 4 18.5589 4 16C4 9.37258 9.37258 4 16 4C22.6274 4 28 9.37258 28 16Z" fill="url(#paint0_linear_87_7264)"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M16 30C23.732 30 30 23.732 30 16C30 8.26801 23.732 2 16 2C8.26801 2 2 8.26801 2 16C2 18.5109 2.661 20.8674 3.81847 22.905L2 30L9.31486 28.3038C11.3014 29.3854 13.5789 30 16 30ZM16 27.8462C22.5425 27.8462 27.8462 22.5425 27.8462 16C27.8462 9.45755 22.5425 4.15385 16 4.15385C9.45755 4.15385 4.15385 9.45755 4.15385 16C4.15385 18.5261 4.9445 20.8675 6.29184 22.7902L5.23077 26.7692L9.27993 25.7569C11.1894 27.0746 13.5046 27.8462 16 27.8462Z" fill="white"></path> <path d="M12.5 9.49989C12.1672 8.83131 11.6565 8.8905 11.1407 8.8905C10.2188 8.8905 8.78125 9.99478 8.78125 12.05C8.78125 13.7343 9.52345 15.578 12.0244 18.3361C14.438 20.9979 17.6094 22.3748 20.2422 22.3279C22.875 22.2811 23.4167 20.0154 23.4167 19.2503C23.4167 18.9112 23.2062 18.742 23.0613 18.696C22.1641 18.2654 20.5093 17.4631 20.1328 17.3124C19.7563 17.1617 19.5597 17.3656 19.4375 17.4765C19.0961 17.8018 18.4193 18.7608 18.1875 18.9765C17.9558 19.1922 17.6103 19.083 17.4665 19.0015C16.9374 18.7892 15.5029 18.1511 14.3595 17.0426C12.9453 15.6718 12.8623 15.2001 12.5959 14.7803C12.3828 14.4444 12.5392 14.2384 12.6172 14.1483C12.9219 13.7968 13.3426 13.254 13.5313 12.9843C13.7199 12.7145 13.5702 12.305 13.4803 12.05C13.0938 10.953 12.7663 10.0347 12.5 9.49989Z" fill="white"></path> <defs> <linearGradient id="paint0_linear_87_7264" x1="26.5" y1="7" x2="4" y2="28" gradientUnits="userSpaceOnUse"> <stop stop-color="#5BD066"></stop> <stop offset="1" stop-color="#27B43E"></stop> </linearGradient> </defs> </g></svg>   </a>
        </div>
    </div>

    <style>
        .whatsapp-out {
            z-index: 9999;
            position: fixed;
            bottom: 0px;
            right: 0px;
            padding: 2rem;
        }

        .whatsapp svg {
            width: 60px;
            height: 60px;
        }
    </style>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="{{ asset('js/main.js') }}"></script>
    @yield('scripts')
</body>
</html>
