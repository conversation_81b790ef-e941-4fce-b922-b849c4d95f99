<?php

namespace App\Services;

use App\Helpers\SettingsHelper;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class PayPalService
{
    /**
     * PayPal client instance
     *
     * @var \Srmklive\PayPal\Services\PayPal
     */
    protected $paypalClient;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->paypalClient = new PayPalClient();

        // Get PayPal configuration from SettingsHelper
        $mode = SettingsHelper::getPaypalMode();
        $clientId = SettingsHelper::getPaypalClientId();
        $clientSecret = SettingsHelper::getPaypalSecret();

        // Set up configuration
        $config = [
            'mode' => $mode,
            $mode => [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'app_id' => 'APP-80W284485P519543T',
            ],
            'payment_action' => 'Sale',
            'currency' => 'USD',
            'notify_url' => '',
            'locale' => 'en_US',
            'validate_ssl' => true,
        ];

        // Log the configuration for debugging
        Log::info('PayPal configuration', [
            'mode' => $mode,
            'client_id' => $clientId ? 'Set' : 'Not set',
            'client_secret' => $clientSecret ? 'Set' : 'Not set',
        ]);

        $this->paypalClient->setApiCredentials($config);
        $this->paypalClient->getAccessToken();
    }

    /**
     * Create a PayPal order
     *
     * @param float $amount
     * @param string $currency
     * @param string $returnUrl
     * @param string $cancelUrl
     * @return array|null
     */
    public function createOrder($amount, $currency = 'USD', $returnUrl, $cancelUrl)
    {
        try {
            $order = $this->paypalClient->createOrder([
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => number_format($amount, 2, '.', ''),
                        ],
                    ],
                ],
                'application_context' => [
                    'return_url' => $returnUrl,
                    'cancel_url' => $cancelUrl,
                ],
            ]);

            return $order;
        } catch (\Exception $e) {
            Log::error('PayPal create order exception', [
                'message' => $e->getMessage(),
                'amount' => $amount,
                'currency' => $currency,
            ]);

            // Check for specific PayPal errors
            $errorMessage = $e->getMessage();
            if (strpos($errorMessage, 'PAYEE_ACCOUNT_RESTRICTED') !== false) {
                Log::error('PayPal account is restricted. Please contact PayPal support or switch to sandbox mode.');
            }

            return null;
        }
    }

    /**
     * Capture a PayPal order
     *
     * @param string $orderId
     * @return array|null
     */
    public function captureOrder($orderId)
    {
        try {
            $result = $this->paypalClient->capturePaymentOrder($orderId);

            return $result;
        } catch (\Exception $e) {
            Log::error('PayPal capture order exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get PayPal order details
     *
     * @param string $orderId
     * @return array|null
     */
    public function getOrderDetails($orderId)
    {
        try {
            $order = $this->paypalClient->showOrderDetails($orderId);

            return $order;
        } catch (\Exception $e) {
            Log::error('PayPal get order details exception', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
