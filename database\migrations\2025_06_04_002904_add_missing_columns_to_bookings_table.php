<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add missing columns that are referenced in the booking creation
            if (!Schema::hasColumn('bookings', 'meet_and_greet')) {
                $table->boolean('meet_and_greet')->default(false)->after('notes');
            }
            if (!Schema::hasColumn('bookings', 'distance_value')) {
                $table->integer('distance_value')->nullable()->after('distance');
            }
            if (!Schema::hasColumn('bookings', 'duration_value')) {
                $table->integer('duration_value')->nullable()->after('distance_value');
            }
            if (!Schema::hasColumn('bookings', 'payment_status')) {
                $table->enum('payment_status', ['pending', 'processing', 'completed', 'failed', 'refunded'])->default('pending')->after('status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Remove the columns we added
            $table->dropColumn(['meet_and_greet', 'distance_value', 'duration_value', 'payment_status']);
        });
    }
};
