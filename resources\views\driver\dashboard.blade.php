@extends('layouts.driver')

@section('title', 'Driver Dashboard')

@section('styles')
<style>
    /* Dashboard Styles */
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }

    .dashboard-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .dashboard-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #ee393d;
    }

    .dashboard-card .card-body {
        padding: 25px;
    }

    .dashboard-icon {
        font-size: 2.5rem;
        color: #ee393d;
        margin-bottom: 15px;
    }

    .stat-card {
        text-align: center;
        padding: 25px 15px;
    }

    .stat-card .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #343a40;
        margin: 10px 0;
    }

    .stat-card .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    .stat-card .stat-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(238, 57, 61, 0.1);
        color: #ee393d;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 1.5rem;
    }

    .earnings-card {
        background: linear-gradient(135deg, #ee393d 0%, #d31a1e 100%);
        color: white;
    }

    .earnings-card .stat-value {
        color: white;
        font-size: 2.5rem;
    }

    .earnings-card .stat-label {
        color: rgba(255, 255, 255, 0.8);
    }

    .earnings-card .stat-icon {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .rides-table {
        margin-bottom: 0;
    }

    .rides-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .rides-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .rides-table tr {
        transition: all 0.3s ease;
    }

    .rides-table tr:hover {
        background-color: rgba(238, 57, 61, 0.03);
    }

    .booking-status {
        padding: 5px 10px;
        border-radius: 50px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .status-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .status-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .status-assigned {
        background-color: #e2e3e5;
        color: #41464b;
    }

    .status-in_progress {
        background-color: #cfe2ff;
        color: #084298;
    }

    .document-alert {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .document-alert i {
        font-size: 1.5rem;
        margin-right: 15px;
    }

    .document-alert-danger {
        background-color: rgba(248, 215, 218, 0.5);
        border-left: 4px solid #dc3545;
    }

    .document-alert-warning {
        background-color: rgba(255, 243, 205, 0.5);
        border-left: 4px solid #ffc107;
    }

    .document-alert-info {
        background-color: rgba(207, 244, 252, 0.5);
        border-left: 4px solid #0dcaf0;
    }

    .quick-action-btn {
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .quick-action-btn i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .quick-action-btn:hover {
        transform: translateY(-5px);
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .rides-table {
            min-width: 800px;
        }

        .card-body {
            overflow-x: auto;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tachometer-alt me-2 text-primary"></i> Welcome, {{ Auth::user()->name }}
        </h1>
        <div class="d-flex">
            <a href="{{ route('driver.rides.my-rides') }}" class="btn btn-primary me-2">
                <i class="fas fa-car me-1"></i> My Rides
            </a>
            <a href="{{ route('driver.documents.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-file-alt me-1"></i> My Documents
            </a>
        </div>
    </div>

    <!-- Document Alerts -->
    @php
        $hasExpiredDocuments = false;
        $hasExpiringDocuments = false;

        foreach(auth()->user()->driverDocuments as $document) {
            if($document->isExpired()) {
                $hasExpiredDocuments = true;
                break;
            } elseif($document->isExpiringSoon()) {
                $hasExpiringDocuments = true;
            }
        }
    @endphp

    @if($hasExpiredDocuments)
    <div class="document-alert document-alert-danger mb-4">
        <i class="fas fa-exclamation-circle text-danger"></i>
        <div>
            <h6 class="mb-1 fw-bold">Document Expired</h6>
            <p class="mb-0">One or more of your documents have expired. Please update them as soon as possible.</p>
            <a href="{{ route('driver.documents.index') }}" class="btn btn-sm btn-danger mt-2">Update Documents</a>
        </div>
    </div>
    @elseif($hasExpiringDocuments)
    <div class="document-alert document-alert-warning mb-4">
        <i class="fas fa-exclamation-triangle text-warning"></i>
        <div>
            <h6 class="mb-1 fw-bold">Documents Expiring Soon</h6>
            <p class="mb-0">One or more of your documents will expire soon. Please update them before they expire.</p>
            <a href="{{ route('driver.documents.index') }}" class="btn btn-sm btn-warning mt-2">Update Documents</a>
        </div>
    </div>
    @endif

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-value">{{ $totalTrips }}</div>
                    <div class="stat-label">Total Trips</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="stat-value">{{ $upcomingTrips }}</div>
                    <div class="stat-label">Upcoming Trips</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="stat-value">{{ number_format($totalDistance, 1) }}</div>
                    <div class="stat-label">Total Distance (km)</div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card earnings-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-value">@currency(){{ number_format($totalEarnings, 2) }}</div>
                    <div class="stat-label">Total Earnings</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Earnings Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-value">@currency(){{ number_format($currentMonthEarnings, 2) }}</div>
                    <div class="stat-label">This Month</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-value">@currency(){{ number_format($currentWeekEarnings, 2) }}</div>
                    <div class="stat-label">This Week</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="dashboard-card h-100">
                <div class="card-body stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value">{{ $completedRidesCount }}</div>
                    <div class="stat-label">Completed Rides</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt me-2"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ route('driver.rides.my-rides') }}" class="quick-action-btn btn btn-outline-primary">
                                <i class="fas fa-car"></i>
                                <span>My Rides</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ route('driver.documents.index') }}" class="quick-action-btn btn btn-outline-info">
                                <i class="fas fa-file-alt"></i>
                                <span>Documents</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ route('driver.earnings.index') }}" class="quick-action-btn btn btn-outline-success">
                                <i class="fas fa-dollar-sign"></i>
                                <span>Earnings</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{{ route('driver.profile.edit') }}" class="quick-action-btn btn btn-outline-secondary">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rides Tables -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-car me-2"></i> My Upcoming Rides</h5>
                    <a href="{{ route('driver.rides.my-rides') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list me-1"></i> View All
                    </a>
                </div>
                <div class="card-body">
                    @if ($upcomingRides->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-car-side fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No upcoming rides found</h5>
                            <p class="text-muted">You don't have any upcoming rides at the moment</p>
                            <a href="{{ route('driver.rides.index') }}" class="btn btn-primary mt-2">
                                <i class="fas fa-search me-1"></i> Find Available Rides
                            </a>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table rides-table">
                                <thead>
                                    <tr>
                                        <th>Booking</th>
                                        <th>Client</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($upcomingRides as $ride)
                                        <tr>
                                            <td>
                                                <div>
                                                    <span class="fw-bold">{{ $ride->booking_number }}</span>
                                                    @if($ride->via_stops && count($ride->via_stops) > 0)
                                                        <div class="mt-1">
                                                            <span class="badge bg-info" style="font-size: 0.7rem;">
                                                                <i class="fas fa-route me-1"></i>{{ count($ride->via_stops) }} via
                                                            </span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($ride->user->profile_photo)
                                                        <img src="{{ asset('storage/' . $ride->user->profile_photo) }}" alt="{{ $ride->user->name }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; color: white; font-size: 12px;">
                                                            {{ strtoupper(substr($ride->user->name, 0, 1)) }}
                                                        </div>
                                                    @endif
                                                    <span>{{ $ride->user->name }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div><i class="fas fa-calendar-alt text-primary me-1"></i> {{ $ride->pickup_date->format('M d, Y') }}</div>
                                                <div><i class="fas fa-clock text-primary me-1"></i> {{ $ride->pickup_date->format('h:i A') }}</div>
                                            </td>
                                            <td>
                                                <span class="booking-status status-{{ $ride->status }}">
                                                    {{ ucfirst(str_replace('_', ' ', $ride->status)) }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-search me-2"></i> Available Rides</h5>
                    <a href="{{ route('driver.rides.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list me-1"></i> View All
                    </a>
                </div>
                <div class="card-body">
                    @if ($availableRides->isEmpty())
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No available rides found</h5>
                            <p class="text-muted">There are no rides available for you at the moment</p>
                            <a href="{{ route('driver.rides.index') }}" class="btn btn-primary mt-2">
                                <i class="fas fa-sync me-1"></i> Refresh
                            </a>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table rides-table">
                                <thead>
                                    <tr>
                                        <th>Booking</th>
                                        <th>Vehicle</th>
                                        <th>Date & Time</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($availableRides as $ride)
                                        <tr>
                                            <td>
                                                <div>
                                                    <span class="fw-bold">{{ $ride->booking_number }}</span>
                                                    @if($ride->via_stops && count($ride->via_stops) > 0)
                                                        <div class="mt-1">
                                                            <span class="badge bg-info" style="font-size: 0.7rem;">
                                                                <i class="fas fa-route me-1"></i>{{ count($ride->via_stops) }} via
                                                            </span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($ride->vehicle->image)
                                                        <img src="{{ asset('storage/' . $ride->vehicle->image) }}" alt="{{ $ride->vehicle->name }}" class="rounded me-2" style="width: 40px; height: 30px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 30px; color: white; font-size: 12px;">
                                                            <i class="fas fa-car"></i>
                                                        </div>
                                                    @endif
                                                    <span>{{ $ride->vehicle->name }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div><i class="fas fa-calendar-alt text-primary me-1"></i> {{ $ride->pickup_date->format('M d, Y') }}</div>
                                                <div><i class="fas fa-clock text-primary me-1"></i> {{ $ride->pickup_date->format('h:i A') }}</div>
                                            </td>
                                            <td>
                                                <a href="{{ route('driver.rides.show', $ride->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Earnings Chart -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-chart-line me-2"></i> Monthly Earnings ({{ date('Y') }})</h5>
                    <a href="{{ route('driver.earnings.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-dollar-sign me-1"></i> View All Earnings
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="earningsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Monthly Earnings Chart
    var ctx = document.getElementById('earningsChart').getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Earnings ($)',
                data: [
                    {{ $monthlyEarnings[1] ?? 0 }},
                    {{ $monthlyEarnings[2] ?? 0 }},
                    {{ $monthlyEarnings[3] ?? 0 }},
                    {{ $monthlyEarnings[4] ?? 0 }},
                    {{ $monthlyEarnings[5] ?? 0 }},
                    {{ $monthlyEarnings[6] ?? 0 }},
                    {{ $monthlyEarnings[7] ?? 0 }},
                    {{ $monthlyEarnings[8] ?? 0 }},
                    {{ $monthlyEarnings[9] ?? 0 }},
                    {{ $monthlyEarnings[10] ?? 0 }},
                    {{ $monthlyEarnings[11] ?? 0 }},
                    {{ $monthlyEarnings[12] ?? 0 }}
                ],
                backgroundColor: 'rgba(238, 57, 61, 0.1)',
                borderColor: '#ee393d',
                borderWidth: 3,
                pointBackgroundColor: '#ee393d',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#ee393d',
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '@currency()' + value;
                        },
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 14
                    },
                    padding: 15,
                    cornerRadius: 10,
                    callbacks: {
                        label: function(context) {
                            return 'Earnings: @currency()' + context.raw.toFixed(2);
                        }
                    }
                }
            }
        }
    });
</script>
@endsection
