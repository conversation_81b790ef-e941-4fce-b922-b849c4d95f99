<?php

namespace App\View\Components;

use App\Helpers\ImageHelper;
use Illuminate\View\Component;

class ImageDisplay extends Component
{
    public $src;
    public $alt;
    public $class;
    public $type;
    public $fallback;
    public $lazy;

    /**
     * Create a new component instance.
     *
     * @param string|null $src
     * @param string $alt
     * @param string $class
     * @param string $type
     * @param string|null $fallback
     * @param bool $lazy
     */
    public function __construct(
        ?string $src = null,
        string $alt = '',
        string $class = '',
        string $type = 'default',
        ?string $fallback = null,
        bool $lazy = true
    ) {
        $this->src = $src;
        $this->alt = $alt;
        $this->class = $class;
        $this->type = $type;
        $this->fallback = $fallback;
        $this->lazy = $lazy;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.image-display');
    }

    /**
     * Get the image URL.
     *
     * @return string
     */
    public function getImageUrl(): string
    {
        if ($this->fallback) {
            return ImageHelper::getImageUrl($this->src, $this->type) ?: $this->fallback;
        }

        return ImageHelper::getImageUrl($this->src, $this->type);
    }

    /**
     * Get loading attribute.
     *
     * @return string
     */
    public function getLoadingAttribute(): string
    {
        return $this->lazy ? 'lazy' : 'eager';
    }
}
