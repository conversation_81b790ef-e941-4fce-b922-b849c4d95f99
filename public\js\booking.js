/**
 * New Booking Form JavaScript
 * Handles the booking form interactions, calculations, and validations
 */

// Global variables
let currentStep = 1;
const totalSteps = 3;
let maps = {
    oneWay: null,
    return: null,
    airport: null
};
let directionsService;
let directionsRenderers = {};
let viaStopCount = 0;
const maxViaStops = 5;

// Get the currency symbol and distance unit from the page or use existing variables if already defined
// This prevents "Identifier has already been declared" errors when the variables are defined in the HTML
let currencySymbol, distanceUnit;
if (typeof window.currencySymbol === 'undefined') {
    currencySymbol = document.querySelector('meta[name="currency-symbol"]')?.getAttribute('content') || '$';
} else {
    currencySymbol = window.currencySymbol;
}

if (typeof window.distanceUnit === 'undefined') {
    distanceUnit = document.querySelector('meta[name="distance-unit"]')?.getAttribute('content') || 'miles';
} else {
    distanceUnit = window.distanceUnit;
}

// Custom map style
const mapStyles = [
    {
        "featureType": "administrative",
        "elementType": "labels.text.fill",
        "stylers": [{"color": "#444444"}]
    },
    {
        "featureType": "landscape",
        "elementType": "all",
        "stylers": [{"color": "#f2f2f2"}]
    },
    {
        "featureType": "poi",
        "elementType": "all",
        "stylers": [{"visibility": "off"}]
    },
    {
        "featureType": "road",
        "elementType": "all",
        "stylers": [{"saturation": -100}, {"lightness": 45}]
    },
    {
        "featureType": "road.highway",
        "elementType": "all",
        "stylers": [{"visibility": "simplified"}]
    },
    {
        "featureType": "road.arterial",
        "elementType": "labels.icon",
        "stylers": [{"visibility": "off"}]
    },
    {
        "featureType": "transit",
        "elementType": "all",
        "stylers": [{"visibility": "off"}]
    },
    {
        "featureType": "water",
        "elementType": "all",
        "stylers": [{"color": "#c4e5f9"}, {"visibility": "on"}]
    }
];

// Initialize Google Maps Autocomplete
// Enhanced to support postal code searches using 'geocode' type
function initializeAutocomplete() {
    try {
        // Check if Google Maps API is loaded
        if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
            console.error('Google Maps API not loaded. Autocomplete will not work.');
            showApiKeyError();
            return;
        }

        // Get autocomplete settings from window.autocompleteSettings or fallback to meta tags
        let autocompleteEnabled, restrictCountry, country, types, biasRadius, useStrictBounds, fields;

        if (window.autocompleteSettings) {
            // Use settings from window.autocompleteSettings with safe defaults
            autocompleteEnabled = window.autocompleteSettings.enabled ?? true;
            restrictCountry = window.autocompleteSettings.restrict_country ?? false;
            country = window.autocompleteSettings.country ?? '';
            types = window.autocompleteSettings.types ?? 'geocode';
            biasRadius = window.autocompleteSettings.bias_radius ?? 50;
            useStrictBounds = window.autocompleteSettings.use_strict_bounds ?? false;
            fields = window.autocompleteSettings.fields ?? 'formatted_address';
        } else {
            // Fallback to meta tags
            autocompleteEnabled = document.querySelector('meta[name="autocomplete-enabled"]')?.getAttribute('content') === 'true';
            restrictCountry = document.querySelector('meta[name="autocomplete-restrict-country"]')?.getAttribute('content') === 'true';
            country = document.querySelector('meta[name="autocomplete-country"]')?.getAttribute('content') || '';
            types = document.querySelector('meta[name="autocomplete-types"]')?.getAttribute('content') || 'geocode';
            biasRadius = parseInt(document.querySelector('meta[name="autocomplete-bias-radius"]')?.getAttribute('content') || '50');
            useStrictBounds = document.querySelector('meta[name="autocomplete-use-strict-bounds"]')?.getAttribute('content') === 'true';
            fields = document.querySelector('meta[name="autocomplete-fields"]')?.getAttribute('content') || 'formatted_address';
        }

        // If autocomplete is disabled, return early
        if (!autocompleteEnabled) {
            console.log('Address autocomplete is disabled in settings.');
            return;
        }

        // Options for autocomplete
        const options = {};

        // Set types based on settings - enhance to include postal codes
        if (types) {
            // If types is 'address', include geocode for better postal code support
            if (types === 'address') {
                options.types = ['geocode']; // geocode includes addresses and postal codes
            } else if (types === 'geocode') {
                options.types = ['geocode'];
            } else {
                options.types = [types];
            }
        } else {
            // Default to geocode for comprehensive address and postal code search
            options.types = ['geocode'];
        }

        // Add country restriction if enabled
        if (restrictCountry && country) {
            options.componentRestrictions = {
                country: country
            };
        }

        // Initialize autocomplete for each input
        const addressInputs = [
            'pickup_address',
            'dropoff_address',
            'return_pickup_address',
            'return_dropoff_address',
            'airport_pickup_address',
            'airport_dropoff_address'
        ];

        addressInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                try {
                    const autocomplete = new google.maps.places.Autocomplete(input, options);

                    // Apply location bias if radius is set
                    if (biasRadius > 0) {
                        // Try to get user's location for bias
                        if (navigator.geolocation) {
                            navigator.geolocation.getCurrentPosition(
                                (position) => {
                                    const circle = new google.maps.Circle({
                                        center: {
                                            lat: position.coords.latitude,
                                            lng: position.coords.longitude
                                        },
                                        radius: biasRadius * 1000 // Convert km to meters
                                    });

                                    if (useStrictBounds) {
                                        autocomplete.setBounds(circle.getBounds());
                                        autocomplete.setOptions({ strictBounds: true });
                                    } else {
                                        autocomplete.setBounds(circle.getBounds());
                                    }
                                },
                                () => {
                                    console.log('Geolocation permission denied or unavailable');
                                }
                            );
                        }
                    }

                    // Add listener to update the input with the full address when a place is selected
                    autocomplete.addListener('place_changed', function() {
                        const place = autocomplete.getPlace();
                        if (place) {
                            // Enhanced address handling for better postal code support
                            let addressValue = '';

                            if (fields === 'formatted_address' && place.formatted_address) {
                                addressValue = place.formatted_address;
                            } else if (fields === 'address_components' && place.address_components) {
                                // Build comprehensive address from components including postal code
                                const components = {
                                    street_number: '',
                                    route: '',
                                    locality: '',
                                    administrative_area_level_1: '',
                                    postal_code: '',
                                    country: ''
                                };

                                // Extract components
                                for (const component of place.address_components) {
                                    const types = component.types;
                                    if (types.includes('street_number')) {
                                        components.street_number = component.long_name;
                                    } else if (types.includes('route')) {
                                        components.route = component.long_name;
                                    } else if (types.includes('locality')) {
                                        components.locality = component.long_name;
                                    } else if (types.includes('administrative_area_level_1')) {
                                        components.administrative_area_level_1 = component.short_name;
                                    } else if (types.includes('postal_code')) {
                                        components.postal_code = component.long_name;
                                    } else if (types.includes('country')) {
                                        components.country = component.long_name;
                                    }
                                }

                                // Build formatted address
                                const addressParts = [];
                                if (components.street_number && components.route) {
                                    addressParts.push(`${components.street_number} ${components.route}`);
                                } else if (components.route) {
                                    addressParts.push(components.route);
                                }
                                if (components.locality) {
                                    addressParts.push(components.locality);
                                }
                                if (components.administrative_area_level_1) {
                                    addressParts.push(components.administrative_area_level_1);
                                }
                                if (components.postal_code) {
                                    addressParts.push(components.postal_code);
                                }
                                if (components.country) {
                                    addressParts.push(components.country);
                                }

                                addressValue = addressParts.join(', ');
                            } else if (fields === 'geometry' && place.geometry && place.geometry.location) {
                                // Use formatted address with coordinates
                                addressValue = place.formatted_address || place.name;

                                // Store coordinates as data attributes
                                input.dataset.lat = place.geometry.location.lat();
                                input.dataset.lng = place.geometry.location.lng();
                            } else {
                                // Default to formatted address, fallback to name if no formatted address
                                addressValue = place.formatted_address || place.name;
                            }

                            // Set the input value
                            input.value = addressValue;

                            // Store additional place data for postal code searches
                            if (place.geometry && place.geometry.location) {
                                input.dataset.lat = place.geometry.location.lat();
                                input.dataset.lng = place.geometry.location.lng();
                            }
                            if (place.place_id) {
                                input.dataset.placeId = place.place_id;
                            }

                            // Trigger change event to update maps and calculations
                            const event = new Event('change', { bubbles: true });
                            input.dispatchEvent(event);
                        }
                    });
                } catch (error) {
                    console.warn(`Error initializing autocomplete for ${inputId}:`, error);
                }
            }
        });
    } catch (error) {
        console.error('Error in initializeAutocomplete function:', error);
    }
}

// Initialize Google Maps
function initMaps() {
    // Initialize directions service
    directionsService = new google.maps.DirectionsService();

    // Initialize maps
    const mapElements = {
        oneWay: document.getElementById('map'),
        return: document.getElementById('return-map'),
        airport: document.getElementById('airport-map')
    };

    // Default map options
    const mapOptions = {
        center: { lat: 51.5074, lng: -0.1278 }, // Default to London
        zoom: 10,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
        styles: mapStyles
    };

    // Create maps
    Object.keys(mapElements).forEach(key => {
        if (mapElements[key]) {
            maps[key] = new google.maps.Map(mapElements[key], mapOptions);

            // Create directions renderer for this map
            directionsRenderers[key] = new google.maps.DirectionsRenderer({
                map: maps[key],
                suppressMarkers: false,
                polylineOptions: {
                    strokeColor: '#ee393d',
                    strokeWeight: 5
                }
            });
        }
    });
}

// Display route on map
function displayRoute(origin, destination, mapType = 'oneWay') {
    if (!origin || !destination) {
        console.warn('Origin or destination is missing');
        return;
    }

    if (!maps[mapType] || !directionsRenderers[mapType]) {
        console.warn(`Map or directions renderer for ${mapType} not initialized`);
        return;
    }

    // Show loading indicator
    showMapLoading(mapType);

    const request = {
        origin: origin,
        destination: destination,
        travelMode: google.maps.TravelMode.DRIVING,
        avoidHighways: false, // Prefer highways for long distance
        avoidTolls: false,    // Allow tolls for faster routes
        avoidFerries: true,   // Avoid ferries for reliability
        optimizeWaypoints: true, // Optimize route for efficiency
        provideRouteAlternatives: false // Single best route
    };

    directionsService.route(request, function(result, status) {
        // Hide loading indicator
        hideMapLoading(mapType);

        if (status === google.maps.DirectionsStatus.OK) {
            directionsRenderers[mapType].setDirections(result);
        } else {
            console.warn('Directions request failed due to ' + status);

            // Handle different error types
            let errorMessage = 'Could not calculate route. Please check your addresses and try again.';

            switch(status) {
                case google.maps.DirectionsStatus.NOT_FOUND:
                    errorMessage = 'One or more addresses could not be found. Please verify your addresses.';
                    break;
                case google.maps.DirectionsStatus.ZERO_RESULTS:
                    errorMessage = 'No route could be found between these locations.';
                    break;
                case google.maps.DirectionsStatus.OVER_QUERY_LIMIT:
                    errorMessage = 'Too many requests. Please try again in a moment.';
                    break;
                case google.maps.DirectionsStatus.REQUEST_DENIED:
                    errorMessage = 'Directions service denied. Please contact support.';
                    break;
                case google.maps.DirectionsStatus.INVALID_REQUEST:
                    errorMessage = 'Invalid route request. Please check your addresses.';
                    break;
            }

            // Only show error for critical failures, not for minor issues
            if (status !== google.maps.DirectionsStatus.NOT_FOUND) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Route Information',
                    text: errorMessage,
                    confirmButtonColor: '#ee393d'
                });
            }
        }
    });
}

// Show map loading indicator
function showMapLoading(mapType) {
    const mapElement = document.getElementById(mapType === 'oneWay' ? 'map' : `${mapType}-map`);
    if (!mapElement) return;

    // Create loading overlay if it doesn't exist
    let loadingOverlay = mapElement.querySelector('.map-loading-overlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'map-loading-overlay';
        loadingOverlay.style.position = 'absolute';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.zIndex = '10';
        loadingOverlay.style.borderRadius = '10px';

        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-primary';
        spinner.setAttribute('role', 'status');

        const span = document.createElement('span');
        span.className = 'visually-hidden';
        span.textContent = 'Loading...';

        spinner.appendChild(span);
        loadingOverlay.appendChild(spinner);

        // Make sure the map container is positioned relatively
        mapElement.style.position = 'relative';
        mapElement.appendChild(loadingOverlay);
    } else {
        loadingOverlay.style.display = 'flex';
    }
}

// Hide map loading indicator
function hideMapLoading(mapType) {
    const mapElement = document.getElementById(mapType === 'oneWay' ? 'map' : `${mapType}-map`);
    if (!mapElement) return;

    const loadingOverlay = mapElement.querySelector('.map-loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// Helper function to format date and time
function formatDateTime(date, time) {
    try {
        if (!date || !time) return '';

        const dateObj = new Date(date + 'T' + time);

        // Format: "Jan 1, 2023 12:00 PM"
        return dateObj.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        }) + ' ' + dateObj.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    } catch (error) {
        console.warn('Error formatting date and time:', error);
        return date + ' ' + time;
    }
}

// Enhanced debounce function
function debounce(func, wait, immediate = false) {
    let timeout;
    return function(...args) {
        const context = this;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Show API key error message
function showApiKeyError() {
    const addressInputs = document.querySelectorAll('input[id*="address"]');
    addressInputs.forEach(input => {
        if (input) {
            input.placeholder = 'Google Maps API key required for autocomplete';
            input.style.borderColor = '#dc3545';
            input.style.backgroundColor = '#fff5f5';

            // Add tooltip or help text
            const helpText = document.createElement('small');
            helpText.className = 'text-danger mt-1 d-block';
            helpText.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Google Maps API key is not configured. Please contact administrator.';

            // Only add if not already present
            if (!input.parentNode.querySelector('.text-danger')) {
                input.parentNode.appendChild(helpText);
            }
        }
    });
}

// Via stops functionality
function addViaStop() {
    if (viaStopCount >= maxViaStops) {
        Swal.fire({
            icon: 'warning',
            title: 'Maximum Via Stops',
            text: `You can add up to ${maxViaStops} via stops only.`,
            confirmButtonColor: '#ee393d'
        });
        return;
    }

    viaStopCount++;
    const container = document.getElementById('viaStopsContainer');

    const viaStopDiv = document.createElement('div');
    viaStopDiv.className = 'via-stop-item mb-2';
    viaStopDiv.dataset.stopIndex = viaStopCount;

    viaStopDiv.innerHTML = `
        <div class="via-stop-number">${viaStopCount}</div>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-map-marker-alt"></i>
            </span>
            <input type="text"
                   class="form-control address-autocomplete"
                   id="via_stop_${viaStopCount}"
                   name="via_stops[${viaStopCount - 1}][address]"
                   placeholder="Enter via stop ${viaStopCount} address"
                   data-lat-field="via_stop_${viaStopCount}_lat"
                   data-lng-field="via_stop_${viaStopCount}_lng">
            <button type="button" class="btn btn-outline-danger" onclick="removeViaStop(${viaStopCount})" title="Remove this via stop">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <input type="hidden" id="via_stop_${viaStopCount}_lat" name="via_stops[${viaStopCount - 1}][lat]">
        <input type="hidden" id="via_stop_${viaStopCount}_lng" name="via_stops[${viaStopCount - 1}][lng]">
    `;

    container.appendChild(viaStopDiv);

    // Initialize autocomplete for the new input
    initializeViaStopAutocomplete(`via_stop_${viaStopCount}`);

    // Update add button state and counter
    updateAddViaStopButton();
    updateViaStopCounter();

    // Focus on the new input
    const newInput = document.getElementById(`via_stop_${viaStopCount}`);
    if (newInput) {
        setTimeout(() => newInput.focus(), 100);
    }

    // Trigger fare recalculation
    debouncedCalculateFare();

    // Show success message for first via stop
    if (viaStopCount === 1) {
        showToast('Via stop added! You can add up to 5 intermediate destinations.', 'success');
    }
}

function removeViaStop(stopIndex) {
    const viaStopDiv = document.querySelector(`[data-stop-index="${stopIndex}"]`);
    if (viaStopDiv) {
        // Add removing animation
        viaStopDiv.classList.add('via-stop-removing');

        // Remove after animation completes
        setTimeout(() => {
            viaStopDiv.remove();
            viaStopCount--;

            // Reindex remaining stops
            reindexViaStops();

            // Update add button state and counter
            updateAddViaStopButton();
            updateViaStopCounter();

            // Trigger fare recalculation
            debouncedCalculateFare();

            // Show feedback
            showToast('Via stop removed successfully.', 'info');
        }, 300);
    }
}

function reindexViaStops() {
    const viaStops = document.querySelectorAll('.via-stop-item');
    viaStopCount = 0;

    viaStops.forEach((stop, index) => {
        viaStopCount++;
        stop.dataset.stopIndex = viaStopCount;

        // Update stop number
        const stopNumber = stop.querySelector('.via-stop-number');
        if (stopNumber) {
            stopNumber.textContent = viaStopCount;
        }

        const input = stop.querySelector('input[type="text"]');
        const latInput = stop.querySelector('input[name*="[lat]"]');
        const lngInput = stop.querySelector('input[name*="[lng]"]');
        const removeBtn = stop.querySelector('button');

        if (input) {
            input.id = `via_stop_${viaStopCount}`;
            input.name = `via_stops[${index}][address]`;
            input.placeholder = `Enter via stop ${viaStopCount} address`;
            input.setAttribute('data-lat-field', `via_stop_${viaStopCount}_lat`);
            input.setAttribute('data-lng-field', `via_stop_${viaStopCount}_lng`);
        }

        if (latInput) {
            latInput.id = `via_stop_${viaStopCount}_lat`;
            latInput.name = `via_stops[${index}][lat]`;
        }

        if (lngInput) {
            lngInput.id = `via_stop_${viaStopCount}_lng`;
            lngInput.name = `via_stops[${index}][lng]`;
        }

        if (removeBtn) {
            removeBtn.setAttribute('onclick', `removeViaStop(${viaStopCount})`);
        }
    });
}

function updateAddViaStopButton() {
    const addBtn = document.getElementById('addViaStopBtn');
    if (addBtn) {
        if (viaStopCount >= maxViaStops) {
            addBtn.disabled = true;
            addBtn.innerHTML = '<i class="fas fa-ban me-1"></i>Max Stops Reached';
            addBtn.classList.add('btn-secondary');
            addBtn.classList.remove('btn-outline-primary');
        } else {
            addBtn.disabled = false;
            addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Stop';
            addBtn.classList.add('btn-outline-primary');
            addBtn.classList.remove('btn-secondary');
        }
    }
}

function updateViaStopCounter() {
    const counter = document.getElementById('currentViaStops');
    if (counter) {
        counter.textContent = viaStopCount;
    }

    // Update counter color based on usage
    const counterContainer = document.getElementById('viaStopCounter');
    if (counterContainer) {
        if (viaStopCount >= maxViaStops) {
            counterContainer.classList.add('text-warning');
            counterContainer.classList.remove('text-muted');
        } else {
            counterContainer.classList.add('text-muted');
            counterContainer.classList.remove('text-warning');
        }
    }
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

function initializeViaStopAutocomplete(inputId) {
    // Check if Google Maps API is loaded
    if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
        console.error('Google Maps API not loaded. Autocomplete will not work for via stops.');
        return;
    }

    const input = document.getElementById(inputId);
    if (!input) return;

    // Use the same options as other address inputs
    const options = {
        types: ['geocode']
    };

    // Add country restriction if enabled (get from existing settings)
    const existingInput = document.getElementById('pickup_address');
    if (existingInput && existingInput.dataset.countryRestriction) {
        options.componentRestrictions = {
            country: existingInput.dataset.countryRestriction
        };
    }

    try {
        const autocomplete = new google.maps.places.Autocomplete(input, options);

        autocomplete.addListener('place_changed', function() {
            const place = autocomplete.getPlace();
            if (place && place.geometry) {
                // Store coordinates
                const latField = input.getAttribute('data-lat-field');
                const lngField = input.getAttribute('data-lng-field');

                if (latField && lngField) {
                    const latInput = document.getElementById(latField);
                    const lngInput = document.getElementById(lngField);

                    if (latInput) latInput.value = place.geometry.location.lat();
                    if (lngInput) lngInput.value = place.geometry.location.lng();
                }

                // Trigger fare recalculation
                debouncedCalculateFare();
            }
        });
    } catch (error) {
        console.warn(`Error initializing autocomplete for via stop ${inputId}:`, error);
    }
}

function addViaStopForTab(tabType) {
    const config = getTabConfig(tabType);

    if (config.count >= maxViaStops) {
        Swal.fire({
            icon: 'warning',
            title: 'Maximum Via Stops',
            text: `You can add up to ${maxViaStops} via stops only.`,
            confirmButtonColor: '#ee393d'
        });
        return;
    }

    config.count++;
    const container = document.getElementById(config.containerId);

    const viaStopDiv = document.createElement('div');
    viaStopDiv.className = 'via-stop-item mb-2';
    viaStopDiv.dataset.stopIndex = config.count;
    viaStopDiv.dataset.tabType = tabType;

    viaStopDiv.innerHTML = `
        <div class="via-stop-number">${config.count}</div>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-map-marker-alt"></i>
            </span>
            <input type="text"
                   class="form-control address-autocomplete"
                   id="${config.prefix}_via_stop_${config.count}"
                   name="${config.prefix}_via_stops[${config.count - 1}][address]"
                   placeholder="Enter via stop ${config.count} address"
                   data-lat-field="${config.prefix}_via_stop_${config.count}_lat"
                   data-lng-field="${config.prefix}_via_stop_${config.count}_lng">
            <button type="button" class="btn btn-outline-danger" onclick="removeViaStopForTab('${tabType}', ${config.count})" title="Remove this via stop">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <input type="hidden" id="${config.prefix}_via_stop_${config.count}_lat" name="${config.prefix}_via_stops[${config.count - 1}][lat]">
        <input type="hidden" id="${config.prefix}_via_stop_${config.count}_lng" name="${config.prefix}_via_stops[${config.count - 1}][lng]">
    `;

    container.appendChild(viaStopDiv);

    // Initialize autocomplete for the new input
    initializeViaStopAutocomplete(`${config.prefix}_via_stop_${config.count}`);

    // Update button state and counter
    updateTabViaStopButton(tabType);
    updateTabViaStopCounter(tabType);

    // Focus on the new input
    const newInput = document.getElementById(`${config.prefix}_via_stop_${config.count}`);
    if (newInput) {
        setTimeout(() => newInput.focus(), 100);
    }

    // Trigger fare recalculation
    debouncedCalculateFare();

    // Show success message for first via stop
    if (config.count === 1) {
        showToast(`Via stop added for ${tabType} journey!`, 'success');
    }
}

function removeViaStopForTab(tabType, stopIndex) {
    const viaStopDiv = document.querySelector(`[data-stop-index="${stopIndex}"][data-tab-type="${tabType}"]`);
    if (viaStopDiv) {
        // Add removing animation
        viaStopDiv.classList.add('via-stop-removing');

        // Remove after animation completes
        setTimeout(() => {
            viaStopDiv.remove();
            const config = getTabConfig(tabType);
            config.count--;

            // Reindex remaining stops for this tab
            reindexViaStopsForTab(tabType);

            // Update button state and counter
            updateTabViaStopButton(tabType);
            updateTabViaStopCounter(tabType);

            // Trigger fare recalculation
            debouncedCalculateFare();

            // Show feedback
            showToast('Via stop removed successfully.', 'info');
        }, 300);
    }
}

function getTabConfig(tabType) {
    const configs = {
        'one_way': {
            count: viaStopCount,
            containerId: 'viaStopsContainer',
            prefix: '',
            buttonId: 'addViaStopBtn',
            counterId: 'currentViaStops'
        },
        'return': {
            count: window.returnViaStopCount || 0,
            containerId: 'returnViaStopsContainer',
            prefix: 'return',
            buttonId: 'addReturnViaStopBtn',
            counterId: 'currentReturnViaStops'
        },
        'airport': {
            count: window.airportViaStopCount || 0,
            containerId: 'airportViaStopsContainer',
            prefix: 'airport',
            buttonId: 'addAirportViaStopBtn',
            counterId: 'currentAirportViaStops'
        }
    };

    // Initialize counts if not set
    if (tabType === 'return' && !window.returnViaStopCount) {
        window.returnViaStopCount = 0;
    }
    if (tabType === 'airport' && !window.airportViaStopCount) {
        window.airportViaStopCount = 0;
    }

    return configs[tabType] || configs['one_way'];
}

function updateTabViaStopButton(tabType) {
    const config = getTabConfig(tabType);
    const addBtn = document.getElementById(config.buttonId);

    if (addBtn) {
        if (config.count >= maxViaStops) {
            addBtn.disabled = true;
            addBtn.innerHTML = '<i class="fas fa-ban me-1"></i>Max Stops Reached';
            addBtn.classList.add('btn-secondary');
            addBtn.classList.remove('btn-outline-primary');
        } else {
            addBtn.disabled = false;
            addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Stop';
            addBtn.classList.add('btn-outline-primary');
            addBtn.classList.remove('btn-secondary');
        }
    }
}

function updateTabViaStopCounter(tabType) {
    const config = getTabConfig(tabType);
    const counter = document.getElementById(config.counterId);

    if (counter) {
        counter.textContent = config.count;
    }

    // Update counter color based on usage
    const counterContainer = document.getElementById(config.counterId.replace('current', '') + 'Counter');
    if (counterContainer) {
        if (config.count >= maxViaStops) {
            counterContainer.classList.add('text-warning');
            counterContainer.classList.remove('text-muted');
        } else {
            counterContainer.classList.add('text-muted');
            counterContainer.classList.remove('text-warning');
        }
    }
}

function reindexViaStopsForTab(tabType) {
    const config = getTabConfig(tabType);
    const viaStops = document.querySelectorAll(`[data-tab-type="${tabType}"].via-stop-item`);
    config.count = 0;

    viaStops.forEach((stop, index) => {
        config.count++;
        stop.dataset.stopIndex = config.count;

        // Update stop number
        const stopNumber = stop.querySelector('.via-stop-number');
        if (stopNumber) {
            stopNumber.textContent = config.count;
        }

        const input = stop.querySelector('input[type="text"]');
        const latInput = stop.querySelector('input[name*="[lat]"]');
        const lngInput = stop.querySelector('input[name*="[lng]"]');
        const removeBtn = stop.querySelector('button');

        if (input) {
            input.id = `${config.prefix}_via_stop_${config.count}`;
            input.name = `${config.prefix}_via_stops[${index}][address]`;
            input.placeholder = `Enter via stop ${config.count} address`;
            input.setAttribute('data-lat-field', `${config.prefix}_via_stop_${config.count}_lat`);
            input.setAttribute('data-lng-field', `${config.prefix}_via_stop_${config.count}_lng`);
        }

        if (latInput) {
            latInput.id = `${config.prefix}_via_stop_${config.count}_lat`;
            latInput.name = `${config.prefix}_via_stops[${index}][lat]`;
        }

        if (lngInput) {
            lngInput.id = `${config.prefix}_via_stop_${config.count}_lng`;
            lngInput.name = `${config.prefix}_via_stops[${index}][lng]`;
        }

        if (removeBtn) {
            removeBtn.setAttribute('onclick', `removeViaStopForTab('${tabType}', ${config.count})`);
        }
    });

    // Update global counts
    if (tabType === 'return') {
        window.returnViaStopCount = config.count;
    } else if (tabType === 'airport') {
        window.airportViaStopCount = config.count;
    }
}

function getViaStopsData() {
    const viaStops = [];
    const viaStopItems = document.querySelectorAll('.via-stop-item');

    viaStopItems.forEach((item, index) => {
        const addressInput = item.querySelector('input[type="text"]');
        const latInput = item.querySelector('input[name*="[lat]"]');
        const lngInput = item.querySelector('input[name*="[lng]"]');

        if (addressInput && addressInput.value.trim()) {
            viaStops.push({
                address: addressInput.value.trim(),
                lat: latInput ? latInput.value : null,
                lng: lngInput ? lngInput.value : null
            });
        }
    });

    return viaStops;
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Check if Google Maps API is already loaded
    if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
        // Initialize Google Maps and Autocomplete if API is already loaded
        initializeAutocomplete();
        initMaps();
    } else {
        // Wait for Google Maps API to load
        window.addEventListener('google-maps-loaded', function() {
            console.log('Google Maps API loaded. Initializing maps and autocomplete...');
            initializeAutocomplete();
            initMaps();
        });
    }

    // Elements
    const bookingForm = document.getElementById('bookingForm');
    const bookingTypeTab = document.getElementById('bookingTypeTab');
    const bookingTypeInput = document.getElementById('bookingType');
    const selectedVehicleInput = document.getElementById('selectedVehicle');
    const totalFareInput = document.getElementById('totalFare');
    // Automatic fare calculation - no manual button needed
    const airportDirectionSelect = document.getElementById('airport_direction');

    // Step navigation elements
    const nextToVehicleBtn = document.getElementById('nextToVehicleBtn');
    const backToTripBtn = document.getElementById('backToTripBtn');
    const toReviewBtn = document.getElementById('toReviewBtn');
    const backToVehicleBtn = document.getElementById('backToVehicleBtn');

    // Step indicators
    const step1Indicator = document.getElementById('step1-indicator');
    const step2Indicator = document.getElementById('step2-indicator');
    const step3Indicator = document.getElementById('step3-indicator');

    // Step content
    const step1Content = document.getElementById('step1-content');
    const step2Content = document.getElementById('step2-content');
    const step3Content = document.getElementById('step3-content');

    // Initialize event listeners
    initEventListeners();

    // Function to initialize all event listeners
    function initEventListeners() {
        // Handle booking type tab changes
        if (bookingTypeTab) {
            bookingTypeTab.addEventListener('click', handleBookingTypeChange);
        }

        // Handle airport direction changes
        if (airportDirectionSelect) {
            airportDirectionSelect.addEventListener('change', handleAirportDirectionChange);
        }

        // Handle vehicle selection
        setupVehicleSelection();

        // Handle step navigation
        if (nextToVehicleBtn) {
            nextToVehicleBtn.addEventListener('click', () => goToStep(2));
        }

        if (backToTripBtn) {
            backToTripBtn.addEventListener('click', () => goToStep(1));
        }

        if (toReviewBtn) {
            toReviewBtn.addEventListener('click', () => goToStep(3));
        }

        if (backToVehicleBtn) {
            backToVehicleBtn.addEventListener('click', () => goToStep(2));
        }

        // Handle form submission
        if (bookingForm) {
            bookingForm.addEventListener('submit', handleFormSubmit);
        }

        // Handle via stops
        const addViaStopBtn = document.getElementById('addViaStopBtn');
        if (addViaStopBtn) {
            addViaStopBtn.addEventListener('click', addViaStop);
        }

        // Handle return via stops
        const addReturnViaStopBtn = document.getElementById('addReturnViaStopBtn');
        if (addReturnViaStopBtn) {
            addReturnViaStopBtn.addEventListener('click', () => addViaStopForTab('return'));
        }

        // Handle airport via stops
        const addAirportViaStopBtn = document.getElementById('addAirportViaStopBtn');
        if (addAirportViaStopBtn) {
            addAirportViaStopBtn.addEventListener('click', () => addViaStopForTab('airport'));
        }

        // Setup address input change handlers
        setupAddressChangeHandlers();

        // Setup extra services change handlers
        setupExtraServicesHandlers();

        // Initialize required fields
        updateRequiredFields();
    }

    // Handle booking type tab changes
    function handleBookingTypeChange(e) {
        if (e.target.classList.contains('nav-link')) {
            // Update hidden input value
            if (e.target.id === 'one-way-tab') {
                bookingTypeInput.value = 'one_way';
            } else if (e.target.id === 'return-tab') {
                bookingTypeInput.value = 'return';
            } else if (e.target.id === 'airport-tab') {
                bookingTypeInput.value = 'airport_transfer';
            }

            // Update required attributes based on active tab
            updateRequiredFields();

            // Reset total fare input
            if (totalFareInput) {
                totalFareInput.value = '';
            }

            // Hide the continue to review button
            if (toReviewBtn) {
                toReviewBtn.classList.add('d-none');
            }

            // Auto-calculate fare if vehicle is selected and addresses are complete
            setTimeout(() => {
                if (selectedVehicleInput && selectedVehicleInput.value) {
                    const bookingType = bookingTypeInput.value;

                    if (bookingType === 'one_way') {
                        const pickup = document.getElementById('pickup_address').value;
                        const dropoff = document.getElementById('dropoff_address').value;

                        if (pickup && dropoff) {
                            calculateFare();
                        }
                    } else if (bookingType === 'return') {
                        const pickup = document.getElementById('return_pickup_address').value;
                        const dropoff = document.getElementById('return_dropoff_address').value;

                        if (pickup && dropoff) {
                            calculateFare();
                        }
                    } else if (bookingType === 'airport_transfer') {
                        const direction = document.getElementById('airport_direction').value;
                        const airport = document.getElementById('airport_id').value;

                        if (airport) {
                            if ((direction === 'to_airport' && document.getElementById('airport_pickup_address').value) ||
                                (direction === 'from_airport' && document.getElementById('airport_dropoff_address').value)) {
                                calculateFare();
                            }
                        }
                    }
                }
            }, 500);
        }
    }

    // Handle airport direction changes
    function handleAirportDirectionChange() {
        const direction = this.value;
        const toAirportFields = document.querySelectorAll('.to-airport-field');
        const fromAirportFields = document.querySelectorAll('.from-airport-field');

        if (direction === 'to_airport') {
            toAirportFields.forEach(field => field.style.display = 'block');
            fromAirportFields.forEach(field => field.style.display = 'none');
        } else {
            toAirportFields.forEach(field => field.style.display = 'none');
            fromAirportFields.forEach(field => field.style.display = 'block');
        }

        // Update map if we have addresses
        updateAirportMap();

        // Auto-calculate fare if vehicle is selected and addresses are complete
        setTimeout(() => {
            if (selectedVehicleInput && selectedVehicleInput.value) {
                const airport = document.getElementById('airport_id').value;

                if (airport) {
                    if ((direction === 'to_airport' && document.getElementById('airport_pickup_address').value) ||
                        (direction === 'from_airport' && document.getElementById('airport_dropoff_address').value)) {
                        calculateFare();
                    }
                }
            }
        }, 500);
    }

    // Update required fields based on active tab
    function updateRequiredFields() {
        const activeTab = document.querySelector('.nav-link.active');
        if (!activeTab) return;

        // Remove required from all address fields first (keep date/time required)
        const addressFields = [
            'pickup_address', 'dropoff_address',
            'return_pickup_address', 'return_dropoff_address',
            'airport_pickup_address', 'airport_dropoff_address'
        ];

        addressFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.removeAttribute('required');
            }
        });

        // Remove required from airport select
        const airportSelect = document.getElementById('airport_id');
        if (airportSelect) {
            airportSelect.removeAttribute('required');
        }

        // Add required based on active tab
        if (activeTab.id === 'one-way-tab') {
            document.getElementById('pickup_address')?.setAttribute('required', 'required');
            document.getElementById('dropoff_address')?.setAttribute('required', 'required');
        } else if (activeTab.id === 'return-tab') {
            document.getElementById('return_pickup_address')?.setAttribute('required', 'required');
            document.getElementById('return_dropoff_address')?.setAttribute('required', 'required');
        } else if (activeTab.id === 'airport-tab') {
            // Airport is always required for airport transfers
            document.getElementById('airport_id')?.setAttribute('required', 'required');

            // Only require the relevant address field based on direction
            const direction = document.getElementById('airport_direction')?.value || 'to_airport';
            if (direction === 'to_airport') {
                // Going to airport: only pickup address required
                document.getElementById('airport_pickup_address')?.setAttribute('required', 'required');
            } else {
                // Coming from airport: only dropoff address required
                document.getElementById('airport_dropoff_address')?.setAttribute('required', 'required');
            }
        }
    }

    // Update airport map
    function updateAirportMap() {
        const direction = document.getElementById('airport_direction')?.value || 'to_airport';
        const airportSelect = document.getElementById('airport_id');
        const airportName = airportSelect ? airportSelect.options[airportSelect.selectedIndex].text : '';

        if (!airportName || airportName === 'Select Airport') return;

        let pickupAddress, dropoffAddress;

        if (direction === 'to_airport') {
            pickupAddress = document.getElementById('airport_pickup_address')?.value || '';
            dropoffAddress = airportName;
        } else {
            pickupAddress = airportName;
            dropoffAddress = document.getElementById('airport_dropoff_address')?.value || '';
        }

        if (pickupAddress && dropoffAddress) {
            displayRoute(pickupAddress, dropoffAddress, 'airport');
        }
    }

    // Setup vehicle selection
    function setupVehicleSelection() {
        const vehicleCards = document.querySelectorAll('.vehicle-card');

        vehicleCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                vehicleCards.forEach(c => {
                    c.classList.remove('selected');

                    // Reset select button
                    const selectBtn = c.querySelector('.select-vehicle-btn');
                    if (selectBtn) {
                        selectBtn.classList.remove('btn-primary');
                        selectBtn.classList.add('btn-outline-primary');
                        selectBtn.innerHTML = '<i class="fas fa-check me-1"></i>Select';
                    }
                });

                // Add selected class to clicked card
                this.classList.add('selected');

                // Update select button
                const selectBtn = this.querySelector('.select-vehicle-btn');
                if (selectBtn) {
                    selectBtn.classList.remove('btn-outline-primary');
                    selectBtn.classList.add('btn-primary');
                    selectBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i>Selected';
                }

                // Update hidden input
                const vehicleId = this.getAttribute('data-vehicle-id');
                if (selectedVehicleInput) {
                    selectedVehicleInput.value = vehicleId;

                    // Automatically calculate fare when vehicle is selected
                    calculateFare();
                }
            });
        });
    }

    // Calculate fare
    function calculateFare() {
        // Get booking type
        const bookingType = bookingTypeInput.value;

        // Get vehicle ID
        const vehicleId = selectedVehicleInput.value;

        if (!vehicleId) {
            // Don't show error, just return silently
            return;
        }

        // Get addresses based on booking type
        let pickupAddress, dropoffAddress;

        if (bookingType === 'one_way') {
            const pickupInput = document.getElementById('pickup_address');
            const dropoffInput = document.getElementById('dropoff_address');

            // Try to get value from input or data attribute
            pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '';
            dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '';

            // Update the input values if they're empty but we have data attributes
            if (!pickupInput.value && pickupInput.getAttribute('data-value')) {
                pickupInput.value = pickupInput.getAttribute('data-value');
            }

            if (!dropoffInput.value && dropoffInput.getAttribute('data-value')) {
                dropoffInput.value = dropoffInput.getAttribute('data-value');
            }

            if (!pickupAddress || !dropoffAddress) {
                // Don't show error, just return silently
                return;
            }
        } else if (bookingType === 'return') {
            const pickupInput = document.getElementById('return_pickup_address');
            const dropoffInput = document.getElementById('return_dropoff_address');

            // Try to get value from input or data attribute
            pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '';
            dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '';

            // Update the input values if they're empty but we have data attributes
            if (!pickupInput.value && pickupInput.getAttribute('data-value')) {
                pickupInput.value = pickupInput.getAttribute('data-value');
            }

            if (!dropoffInput.value && dropoffInput.getAttribute('data-value')) {
                dropoffInput.value = dropoffInput.getAttribute('data-value');
            }

            if (!pickupAddress || !dropoffAddress) {
                // Don't show error, just return silently
                return;
            }
        } else if (bookingType === 'airport_transfer') {
            const direction = document.getElementById('airport_direction').value;
            const airportSelect = document.getElementById('airport_id');
            const airportName = airportSelect ? airportSelect.options[airportSelect.selectedIndex].text : '';

            if (!airportName) {
                // Don't show error, just return silently
                return;
            }

            if (direction === 'to_airport') {
                const pickupInput = document.getElementById('airport_pickup_address');

                // Try to get value from input or data attribute
                pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '';
                dropoffAddress = airportName;

                // Update the input value if it's empty but we have a data attribute
                if (!pickupInput.value && pickupInput.getAttribute('data-value')) {
                    pickupInput.value = pickupInput.getAttribute('data-value');
                }

                if (!pickupAddress) {
                    // Don't show error, just return silently
                    return;
                }
            } else {
                const dropoffInput = document.getElementById('airport_dropoff_address');

                pickupAddress = airportName;
                // Try to get value from input or data attribute
                dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '';

                // Update the input value if it's empty but we have a data attribute
                if (!dropoffInput.value && dropoffInput.getAttribute('data-value')) {
                    dropoffInput.value = dropoffInput.getAttribute('data-value');
                }

                if (!dropoffAddress) {
                    // Don't show error, just return silently
                    return;
                }
            }
        }

        // Show loading indicator
        const fareCalculationStatus = document.getElementById('fareCalculationStatus');
        if (fareCalculationStatus) {
            fareCalculationStatus.classList.remove('d-none');
        }

        // Hide the continue to review button during calculation
        if (toReviewBtn) {
            toReviewBtn.classList.add('d-none');
        }

        // Get pickup date and time based on booking type
        let pickupDate = '';
        let pickupTime = '';

        if (bookingType === 'one_way') {
            pickupDate = document.getElementById('pickup_date').value;
            pickupTime = document.getElementById('pickup_time').value;
        } else if (bookingType === 'return') {
            pickupDate = document.getElementById('return_pickup_date').value;
            pickupTime = document.getElementById('return_pickup_time').value;
        } else if (bookingType === 'airport_transfer') {
            pickupDate = document.getElementById('airport_pickup_date').value;
            pickupTime = document.getElementById('airport_pickup_time').value;
        }

        // Prepare data for AJAX request
        const data = {
            booking_type: bookingType,
            vehicle_id: vehicleId,
            pickup_address: pickupAddress,
            dropoff_address: dropoffAddress,
            pickup_date: pickupDate,
            pickup_time: pickupTime
        };

        // Add airport transfer specific data
        if (bookingType === 'airport_transfer') {
            const airportDirection = document.getElementById('airport_direction').value;
            const airportSelect = document.getElementById('airport_id');
            const airportId = airportSelect ? airportSelect.value : '';

            data.airport_direction = airportDirection;
            data.airport_id = airportId;
        }

        // Add return trip data
        if (bookingType === 'return') {
            const returnDate = document.getElementById('return_date').value;
            const returnTime = document.getElementById('return_time').value;
            data.return_date = returnDate;
            data.return_time = returnTime;
        }

        // Add extra services data
        data.extra_services = getExtraServicesData(bookingType);

        // Add passenger count
        data.passengers = getPassengerCount(bookingType);

        // Add flight information for airport transfers
        if (bookingType === 'airport_transfer') {
            const flightNumber = document.getElementById('flight_number')?.value || '';
            const airline = document.getElementById('airline')?.value || '';
            if (flightNumber) data.flight_number = flightNumber;
            if (airline) data.airline = airline;
        }

        // Add special notes
        const notes = getNotesForBookingType(bookingType);
        if (notes) data.notes = notes;

        // Add via stops data
        const viaStops = getViaStopsData();
        if (viaStops.length > 0) {
            data.via_stops = viaStops;
        }

        // Send AJAX request
        fetch('/booking/calculate-fare', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            if (fareCalculationStatus) {
                fareCalculationStatus.classList.add('d-none');
            }

            if (data.success) {
                // Update total fare input
                if (totalFareInput) {
                    totalFareInput.value = data.fare;
                }

                // Store fare details in a hidden input
                if (data.fare_details) {
                    const fareDetailsInput = document.getElementById('fare_details');
                    if (fareDetailsInput) {
                        fareDetailsInput.value = JSON.stringify(data.fare_details);
                    }
                }

                // Update distance and duration values in hidden inputs
                if (data.fare_details && data.fare_details.distance_km) {
                    const distanceValueInput = document.getElementById('distance_value');
                    if (distanceValueInput) {
                        distanceValueInput.value = parseFloat(data.fare_details.distance_km);
                    }
                }

                if (data.fare_details && data.fare_details.duration_minutes) {
                    const durationValueInput = document.getElementById('duration_value');
                    if (durationValueInput) {
                        durationValueInput.value = parseFloat(data.fare_details.duration_minutes);
                    }
                }

                // Show the continue to review button
                if (toReviewBtn) {
                    toReviewBtn.classList.remove('d-none');
                }

                // Update summary
                updateSummary();
            } else {
                console.error('Fare calculation failed:', data.message);

                // Show a user-friendly error message
                Swal.fire({
                    icon: 'warning',
                    title: 'Fare Calculation Issue',
                    text: 'We had trouble calculating the fare. Please check your addresses and try again, or contact support if the issue persists.',
                    confirmButtonColor: '#ee393d'
                });

                // Hide the continue to review button
                if (toReviewBtn) {
                    toReviewBtn.classList.add('d-none');
                }

                // Reset fare input
                if (totalFareInput) {
                    totalFareInput.value = '';
                }
            }
        })
        .catch(error => {
            console.error('Error calculating fare:', error);

            // Hide loading indicator
            if (fareCalculationStatus) {
                fareCalculationStatus.classList.add('d-none');
            }

            // Show a user-friendly error message
            Swal.fire({
                icon: 'error',
                title: 'Connection Error',
                text: 'We encountered a problem connecting to our servers. Please check your internet connection and try again.',
                confirmButtonColor: '#ee393d'
            });

            // Hide the continue to review button
            if (toReviewBtn) {
                toReviewBtn.classList.add('d-none');
            }

            // Reset fare input
            if (totalFareInput) {
                totalFareInput.value = '';
            }
        });
    }

    // Setup address input change handlers
    function setupAddressChangeHandlers() {
        // One Way
        const pickupAddress = document.getElementById('pickup_address');
        const dropoffAddress = document.getElementById('dropoff_address');

        if (pickupAddress && dropoffAddress) {
            const updateOneWayMap = debounce(() => {
                const pickup = pickupAddress.value;
                const dropoff = dropoffAddress.value;

                if (pickup && dropoff) {
                    displayRoute(pickup, dropoff, 'oneWay');

                    // Auto-calculate fare if vehicle is selected
                    if (selectedVehicleInput && selectedVehicleInput.value) {
                        calculateFare();
                    }
                }
            }, 1000);

            // Add input event for real-time updates
            pickupAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            dropoffAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            pickupAddress.addEventListener('change', updateOneWayMap);
            pickupAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateOneWayMap();
                }
            });

            dropoffAddress.addEventListener('change', updateOneWayMap);
            dropoffAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateOneWayMap();
                }
            });
        }

        // Return
        const returnPickupAddress = document.getElementById('return_pickup_address');
        const returnDropoffAddress = document.getElementById('return_dropoff_address');

        if (returnPickupAddress && returnDropoffAddress) {
            const updateReturnMap = debounce(() => {
                const pickup = returnPickupAddress.value;
                const dropoff = returnDropoffAddress.value;

                if (pickup && dropoff) {
                    displayRoute(pickup, dropoff, 'return');

                    // Auto-calculate fare if vehicle is selected
                    if (selectedVehicleInput && selectedVehicleInput.value) {
                        calculateFare();
                    }
                }
            }, 1000);

            // Add input event for real-time updates
            returnPickupAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            returnDropoffAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            returnPickupAddress.addEventListener('change', updateReturnMap);
            returnPickupAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateReturnMap();
                }
            });

            returnDropoffAddress.addEventListener('change', updateReturnMap);
            returnDropoffAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateReturnMap();
                }
            });
        }

        // Airport
        const airportPickupAddress = document.getElementById('airport_pickup_address');
        const airportDropoffAddress = document.getElementById('airport_dropoff_address');
        const airportSelect = document.getElementById('airport_id');

        if (airportPickupAddress && airportDropoffAddress && airportSelect) {
            const updateAirportMapHandler = debounce(() => {
                updateAirportMap();

                // Auto-calculate fare if vehicle is selected and addresses are complete
                if (selectedVehicleInput && selectedVehicleInput.value) {
                    const direction = document.getElementById('airport_direction').value;
                    const airport = airportSelect.value;

                    if (airport) {
                        if ((direction === 'to_airport' && airportPickupAddress.value) ||
                            (direction === 'from_airport' && airportDropoffAddress.value)) {
                            calculateFare();
                        }
                    }
                }
            }, 1000);

            // Add input event for real-time updates
            airportPickupAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            airportDropoffAddress.addEventListener('input', function() {
                // Store the value in a data attribute for persistence
                this.setAttribute('data-value', this.value);
            });

            airportPickupAddress.addEventListener('change', updateAirportMapHandler);
            airportPickupAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateAirportMapHandler();
                }
            });

            airportDropoffAddress.addEventListener('change', updateAirportMapHandler);
            airportDropoffAddress.addEventListener('blur', function() {
                // If the value is empty but we have a stored value, restore it
                if (!this.value && this.getAttribute('data-value')) {
                    this.value = this.getAttribute('data-value');
                    updateAirportMapHandler();
                }
            });

            airportSelect.addEventListener('change', updateAirportMapHandler);

            // Airport direction change handler
            const airportDirectionSelect = document.getElementById('airport_direction');
            if (airportDirectionSelect) {
                airportDirectionSelect.addEventListener('change', function() {
                    const direction = this.value;
                    const toAirportField = document.querySelector('.to-airport-field');
                    const fromAirportField = document.querySelector('.from-airport-field');

                    if (direction === 'to_airport') {
                        toAirportField.style.display = 'block';
                        fromAirportField.style.display = 'none';
                    } else {
                        toAirportField.style.display = 'none';
                        fromAirportField.style.display = 'block';
                    }

                    // Update required fields
                    updateRequiredFields();

                    // Update map
                    updateAirportMap();
                });
            }
        }
    }

    // Go to step
    function goToStep(step) {
        // Validate current step before proceeding
        if (currentStep < step && !validateStep(currentStep)) {
            return;
        }

        // Update current step
        currentStep = step;

        // Update step indicators
        step1Indicator.classList.remove('active', 'completed');
        step2Indicator.classList.remove('active', 'completed');
        step3Indicator.classList.remove('active', 'completed');

        if (step === 1) {
            step1Indicator.classList.add('active');
        } else if (step === 2) {
            step1Indicator.classList.add('completed');
            step2Indicator.classList.add('active');
        } else if (step === 3) {
            step1Indicator.classList.add('completed');
            step2Indicator.classList.add('completed');
            step3Indicator.classList.add('active');

            // Update summary when going to step 3
            updateSummary();
        }

        // Show/hide step content
        step1Content.classList.remove('active');
        step2Content.classList.remove('active');
        step3Content.classList.remove('active');

        if (step === 1) {
            step1Content.classList.add('active');
        } else if (step === 2) {
            step2Content.classList.add('active');
        } else if (step === 3) {
            step3Content.classList.add('active');
        }

        // Scroll to top of form
        bookingForm.scrollIntoView({ behavior: 'smooth' });
    }

    // Validate step
    function validateStep(step) {
        if (step === 1) {
            return validateStep1();
        } else if (step === 2) {
            return validateStep2();
        }

        return true;
    }

    // Validate step 1 (Trip Details)
    function validateStep1() {
        try {
            const bookingType = bookingTypeInput.value;

            if (bookingType === 'one_way') {
                const pickupAddress = document.getElementById('pickup_address').value;
                const dropoffAddress = document.getElementById('dropoff_address').value;
                const pickupDate = document.getElementById('pickup_date').value;
                const pickupTime = document.getElementById('pickup_time').value;

                if (!pickupAddress) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Address Required',
                        text: 'Please enter your pickup address.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!dropoffAddress) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Dropoff Address Required',
                        text: 'Please enter your dropoff address.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!pickupDate) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Date Required',
                        text: 'Please select your pickup date.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!pickupTime) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Time Required',
                        text: 'Please select your pickup time.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                // Update pickup_datetime hidden input
                const pickupDatetime = pickupDate + ' ' + pickupTime;
                document.getElementById('pickup_datetime').value = pickupDatetime;

            } else if (bookingType === 'return') {
                const pickupAddress = document.getElementById('return_pickup_address').value;
                const dropoffAddress = document.getElementById('return_dropoff_address').value;
                const pickupDate = document.getElementById('return_pickup_date').value;
                const pickupTime = document.getElementById('return_pickup_time').value;
                const returnDate = document.getElementById('return_date').value;
                const returnTime = document.getElementById('return_time').value;

                if (!pickupAddress) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Address Required',
                        text: 'Please enter your pickup address.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!dropoffAddress) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Dropoff Address Required',
                        text: 'Please enter your dropoff address.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!pickupDate) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Date Required',
                        text: 'Please select your pickup date.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!pickupTime) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Time Required',
                        text: 'Please select your pickup time.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!returnDate) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Return Date Required',
                        text: 'Please select your return date.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!returnTime) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Return Time Required',
                        text: 'Please select your return time.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                // Update pickup_datetime and return_datetime hidden inputs
                const pickupDatetime = pickupDate + ' ' + pickupTime;
                const returnDatetime = returnDate + ' ' + returnTime;
                document.getElementById('pickup_datetime').value = pickupDatetime;
                document.getElementById('return_datetime').value = returnDatetime;

            } else if (bookingType === 'airport_transfer') {
                const direction = document.getElementById('airport_direction').value;
                const airportSelect = document.getElementById('airport_id');
                const airportId = airportSelect ? airportSelect.value : '';
                const pickupDate = document.getElementById('airport_pickup_date').value;
                const pickupTime = document.getElementById('airport_pickup_time').value;

                if (!airportId) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Airport Required',
                        text: 'Please select an airport.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (direction === 'to_airport') {
                    const pickupAddress = document.getElementById('airport_pickup_address').value;

                    if (!pickupAddress) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Pickup Address Required',
                            text: 'Please enter your pickup address.',
                            confirmButtonColor: '#ee393d'
                        });
                        return false;
                    }
                } else {
                    const dropoffAddress = document.getElementById('airport_dropoff_address').value;

                    if (!dropoffAddress) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Dropoff Address Required',
                            text: 'Please enter your dropoff address.',
                            confirmButtonColor: '#ee393d'
                        });
                        return false;
                    }
                }

                if (!pickupDate) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Date Required',
                        text: 'Please select your pickup date.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                if (!pickupTime) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Pickup Time Required',
                        text: 'Please select your pickup time.',
                        confirmButtonColor: '#ee393d'
                    });
                    return false;
                }

                // Update pickup_datetime hidden input
                const pickupDatetime = pickupDate + ' ' + pickupTime;
                document.getElementById('pickup_datetime').value = pickupDatetime;
            }

            return true;
        } catch (error) {
            console.error('Error validating step 1:', error);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'An error occurred during validation. Please try again.',
                confirmButtonColor: '#ee393d'
            });
            return false;
        }
    }

    // Validate step 2 (Vehicle Selection)
    function validateStep2() {
        try {
            // Check if a vehicle is selected
            const vehicleId = selectedVehicleInput ? selectedVehicleInput.value : null;

            // Check if fare is calculated
            const totalFare = totalFareInput ? totalFareInput.value : null;

            if (!vehicleId) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Vehicle Selection Required',
                    text: 'Please select a vehicle to continue.',
                    confirmButtonColor: '#ee393d'
                });

                // Highlight vehicle cards to make it clear they need to be selected
                const vehicleCards = document.querySelectorAll('.vehicle-card');
                vehicleCards.forEach(card => {
                    card.style.boxShadow = '0 0 10px rgba(238, 57, 61, 0.5)';
                    setTimeout(() => {
                        card.style.boxShadow = '';
                    }, 2000);
                });

                return false;
            }

            if (!totalFare) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Fare Calculation Required',
                    text: 'Please calculate the fare before proceeding.',
                    confirmButtonColor: '#ee393d'
                });

                // Highlight the calculate fare button
                if (calculateFareBtn) {
                    calculateFareBtn.classList.add('btn-pulse');
                    calculateFareBtn.style.animation = 'pulse 1s infinite';
                    setTimeout(() => {
                        calculateFareBtn.classList.remove('btn-pulse');
                        calculateFareBtn.style.animation = '';
                    }, 2000);
                }

                return false;
            }

            return true;
        } catch (error) {
            console.error('Error validating step 2:', error);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'An error occurred during validation. Please try again.',
                confirmButtonColor: '#ee393d'
            });
            return false;
        }
    }

    // Update summary
    function updateSummary() {
        try {
            const bookingType = bookingTypeInput.value;

            // Update trip type
            const summaryTripType = document.getElementById('summary-trip-type');
            if (summaryTripType) {
                if (bookingType === 'one_way') {
                    summaryTripType.textContent = 'One Way';
                } else if (bookingType === 'return') {
                    summaryTripType.textContent = 'Return';
                } else if (bookingType === 'airport_transfer') {
                    summaryTripType.textContent = 'Airport Transfer';
                }
            }

            // Update addresses
            if (bookingType === 'one_way') {
                const pickupInput = document.getElementById('pickup_address');
                const dropoffInput = document.getElementById('dropoff_address');
                const pickupDate = document.getElementById('pickup_date').value;
                const pickupTime = document.getElementById('pickup_time').value;

                // Get address from input or data attribute
                const pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '-';
                const dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '-';

                document.getElementById('summary-pickup').textContent = pickupAddress;
                document.getElementById('summary-dropoff').textContent = dropoffAddress;
                document.getElementById('summary-datetime').textContent = formatDateTime(pickupDate, pickupTime) || '-';

                // Hide return container
                document.getElementById('summary-return-container').style.display = 'none';
                // Show dropoff container
                document.getElementById('summary-dropoff-container').style.display = 'flex';
                // Hide airport direction container
                document.getElementById('summary-airport-direction-container').style.display = 'none';

            } else if (bookingType === 'return') {
                const pickupInput = document.getElementById('return_pickup_address');
                const dropoffInput = document.getElementById('return_dropoff_address');
                const pickupDate = document.getElementById('return_pickup_date').value;
                const pickupTime = document.getElementById('return_pickup_time').value;
                const returnDate = document.getElementById('return_date').value;
                const returnTime = document.getElementById('return_time').value;

                // Get address from input or data attribute
                const pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '-';
                const dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '-';

                document.getElementById('summary-pickup').textContent = pickupAddress;
                document.getElementById('summary-dropoff').textContent = dropoffAddress;
                document.getElementById('summary-datetime').textContent = formatDateTime(pickupDate, pickupTime) || '-';
                document.getElementById('summary-return-datetime').textContent = formatDateTime(returnDate, returnTime) || '-';

                // Show return container
                document.getElementById('summary-return-container').style.display = 'flex';
                // Show dropoff container
                document.getElementById('summary-dropoff-container').style.display = 'flex';
                // Hide airport direction container
                document.getElementById('summary-airport-direction-container').style.display = 'none';

            } else if (bookingType === 'airport_transfer') {
                const direction = document.getElementById('airport_direction').value;
                const airportSelect = document.getElementById('airport_id');
                const airportName = airportSelect ? airportSelect.options[airportSelect.selectedIndex].text : '';
                const pickupDate = document.getElementById('airport_pickup_date').value;
                const pickupTime = document.getElementById('airport_pickup_time').value;

                // Update airport direction
                const summaryAirportDirection = document.getElementById('summary-airport-direction');
                if (summaryAirportDirection) {
                    summaryAirportDirection.textContent = direction === 'to_airport' ? 'To Airport' : 'From Airport';
                }

                if (direction === 'to_airport') {
                    const pickupInput = document.getElementById('airport_pickup_address');
                    // Get address from input or data attribute
                    const pickupAddress = pickupInput.value || pickupInput.getAttribute('data-value') || '-';

                    document.getElementById('summary-pickup').textContent = pickupAddress;
                    document.getElementById('summary-dropoff').textContent = airportName || '-';
                } else {
                    const dropoffInput = document.getElementById('airport_dropoff_address');
                    // Get address from input or data attribute
                    const dropoffAddress = dropoffInput.value || dropoffInput.getAttribute('data-value') || '-';

                    document.getElementById('summary-pickup').textContent = airportName || '-';
                    document.getElementById('summary-dropoff').textContent = dropoffAddress;
                }

                document.getElementById('summary-datetime').textContent = formatDateTime(pickupDate, pickupTime) || '-';

                // Hide return container
                document.getElementById('summary-return-container').style.display = 'none';
                // Show dropoff container
                document.getElementById('summary-dropoff-container').style.display = 'flex';
                // Show airport direction container
                document.getElementById('summary-airport-direction-container').style.display = 'flex';
            }

            // Update vehicle details
            const selectedVehicleCard = document.querySelector('.vehicle-card.selected');
            if (selectedVehicleCard) {
                const vehicleName = selectedVehicleCard.querySelector('.vehicle-name')?.textContent || '-';
                const vehicleType = selectedVehicleCard.querySelector('.text-muted')?.textContent || '-';

                // Get both passengers and luggage information
                const passengersElement = selectedVehicleCard.querySelector('.vehicle-detail:nth-child(1) p');
                const luggageElement = selectedVehicleCard.querySelector('.vehicle-detail:nth-child(2) p');

                const passengers = passengersElement?.textContent || '-';
                const luggage = luggageElement?.textContent || '-';

                // Create a combined capacity string
                const capacity = passengers + ' / ' + luggage;

                document.getElementById('summary-vehicle').textContent = vehicleName;
                document.getElementById('summary-vehicle-type').textContent = vehicleType;
                document.getElementById('summary-capacity').textContent = capacity;

                // Update passenger count
                const passengerCount = getPassengerCount(bookingType);
                document.getElementById('summary-passengers').textContent = passengerCount + ' passenger' + (passengerCount > 1 ? 's' : '');
            }

            // Update via stops summary
            updateViaStopsSummary();

            // Update extra services summary
            updateExtraServicesSummary(bookingType);

            // Update flight information summary
            updateFlightInfoSummary(bookingType);

            // Update special requests summary
            updateSpecialRequestsSummary(bookingType);

            // Update fare details
            const distanceValue = document.getElementById('distance_value')?.value || 0;
            const totalFare = totalFareInput?.value || 0;

            // Get fare details from the hidden input if available
            let fareDetails = {};
            const fareDetailsInput = document.getElementById('fare_details');
            if (fareDetailsInput && fareDetailsInput.value) {
                try {
                    fareDetails = JSON.parse(fareDetailsInput.value);
                } catch (e) {
                    console.warn('Failed to parse fare details:', e);
                }
            }

            // Use fare details if available, otherwise use simplified calculation
            const baseFare = fareDetails.base_fare || (parseFloat(totalFare) * 0.5); // 50% of total fare
            const distanceFare = fareDetails.distance_fare || (parseFloat(totalFare) * 0.3); // 30% of total fare
            const bookingFee = fareDetails.booking_fee || 2.50;
            // Calculate subtotal for display if needed
            // const subtotal = fareDetails.subtotal || (baseFare + distanceFare + bookingFee);
            const taxRate = fareDetails.tax_rate || 0;
            const taxAmount = fareDetails.tax_amount || 0;

            // Format distance with the appropriate unit
            const distanceUnitLabel = distanceUnit === 'miles' ? ' mi' : ' km';
            const displayDistance = distanceUnit === 'miles' ?
                (parseFloat(distanceValue) * 0.621371).toFixed(1) :
                parseFloat(distanceValue).toFixed(1);

            document.getElementById('summary-distance').textContent = displayDistance + distanceUnitLabel;
            document.getElementById('summary-base-fare').textContent = currencySymbol + parseFloat(baseFare).toFixed(2);
            document.getElementById('summary-distance-fare').textContent = currencySymbol + parseFloat(distanceFare).toFixed(2);
            document.getElementById('summary-booking-fee').textContent = currencySymbol + parseFloat(bookingFee).toFixed(2);

            // Add surcharge rows if applicable
            const airportSurchargeRow = document.getElementById('summary-airport-surcharge-row');
            if (airportSurchargeRow) {
                if (fareDetails.airport_surcharge) {
                    airportSurchargeRow.style.display = 'flex';
                    document.getElementById('summary-airport-surcharge').textContent = currencySymbol + parseFloat(fareDetails.airport_surcharge).toFixed(2);
                } else {
                    airportSurchargeRow.style.display = 'none';
                }
            }

            const weekendSurchargeRow = document.getElementById('summary-weekend-surcharge-row');
            if (weekendSurchargeRow) {
                if (fareDetails.weekend_surcharge) {
                    weekendSurchargeRow.style.display = 'flex';
                    document.getElementById('summary-weekend-surcharge').textContent = currencySymbol + parseFloat(fareDetails.weekend_surcharge).toFixed(2);
                } else {
                    weekendSurchargeRow.style.display = 'none';
                }
            }

            const nightSurchargeRow = document.getElementById('summary-night-surcharge-row');
            if (nightSurchargeRow) {
                if (fareDetails.night_surcharge) {
                    nightSurchargeRow.style.display = 'flex';
                    document.getElementById('summary-night-surcharge').textContent = currencySymbol + parseFloat(fareDetails.night_surcharge).toFixed(2);
                } else {
                    nightSurchargeRow.style.display = 'none';
                }
            }

            // Add via charges row if applicable
            const viaChargesRow = document.getElementById('summary-via-charges-row');
            if (viaChargesRow) {
                if (fareDetails.via_charges && fareDetails.via_count > 0) {
                    viaChargesRow.style.display = 'flex';
                    document.getElementById('summary-via-count').textContent = fareDetails.via_count;
                    document.getElementById('summary-via-charges').textContent = currencySymbol + parseFloat(fareDetails.via_charges).toFixed(2);
                } else {
                    viaChargesRow.style.display = 'none';
                }
            }

            // Add extra services to fare breakdown
            updateExtraServicesFareBreakdown(bookingType, fareDetails);

            // Add tax row if tax rate is greater than 0
            const taxRow = document.getElementById('summary-tax-row');
            if (taxRow) {
                if (taxRate > 0) {
                    taxRow.style.display = 'flex';
                    document.getElementById('summary-tax-rate').textContent = parseFloat(taxRate).toFixed(2) + '%';
                    document.getElementById('summary-tax-amount').textContent = currencySymbol + parseFloat(taxAmount).toFixed(2);
                } else {
                    taxRow.style.display = 'none';
                }
            }

            document.getElementById('summary-total-fare').textContent = currencySymbol + parseFloat(totalFare).toFixed(2);

        } catch (error) {
            console.error('Error updating summary:', error);
        }
    }

    // Setup extra services change handlers
    function setupExtraServicesHandlers() {
        const extraServiceCheckboxes = [
            'meet_and_greet', 'child_seat', 'wheelchair_accessible', 'extra_luggage',
            'return_meet_and_greet', 'return_child_seat', 'return_wheelchair_accessible', 'return_extra_luggage',
            'airport_meet_and_greet', 'airport_child_seat', 'airport_wheelchair_accessible', 'airport_extra_luggage'
        ];

        extraServiceCheckboxes.forEach(checkboxId => {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.addEventListener('change', function() {
                    // Auto-calculate fare when extra services change
                    if (selectedVehicleInput && selectedVehicleInput.value) {
                        setTimeout(() => {
                            calculateFare();
                        }, 100);
                    }
                });
            }
        });

        // Also listen for passenger count changes
        const passengerSelects = ['passengers', 'return_passengers', 'airport_passengers'];
        passengerSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.addEventListener('change', function() {
                    // Auto-calculate fare when passenger count changes
                    if (selectedVehicleInput && selectedVehicleInput.value) {
                        setTimeout(() => {
                            calculateFare();
                        }, 100);
                    }
                });
            }
        });
    }

    // Get extra services data for current booking type
    function getExtraServicesData(bookingType) {
        const services = {};
        let prefix = '';

        if (bookingType === 'return') {
            prefix = 'return_';
        } else if (bookingType === 'airport_transfer') {
            prefix = 'airport_';
        }

        const serviceTypes = ['meet_and_greet', 'child_seat', 'wheelchair_accessible', 'extra_luggage'];

        serviceTypes.forEach(service => {
            const checkbox = document.getElementById(prefix + service);
            if (checkbox) {
                services[service] = checkbox.checked;
            }
        });

        return services;
    }

    // Get passenger count for current booking type
    function getPassengerCount(bookingType) {
        let selectId = 'passengers';

        if (bookingType === 'return') {
            selectId = 'return_passengers';
        } else if (bookingType === 'airport_transfer') {
            selectId = 'airport_passengers';
        }

        const select = document.getElementById(selectId);
        return select ? parseInt(select.value) || 2 : 2;
    }

    // Get notes for current booking type
    function getNotesForBookingType(bookingType) {
        let notesId = 'notes';

        if (bookingType === 'return') {
            notesId = 'return_notes';
        } else if (bookingType === 'airport_transfer') {
            notesId = 'airport_notes';
        }

        const notesField = document.getElementById(notesId);
        return notesField ? notesField.value.trim() : '';
    }

    // Update via stops summary
    function updateViaStopsSummary() {
        const viaStops = getViaStopsData();
        const summarySection = document.getElementById('via-stops-summary');

        if (viaStops.length > 0) {
            // Create or update via stops list
            let viaStopsList = document.getElementById('summary-via-stops-list');
            if (!viaStopsList) {
                // Create the list if it doesn't exist
                viaStopsList = document.createElement('div');
                viaStopsList.id = 'summary-via-stops-list';
                viaStopsList.className = 'mt-2';

                if (summarySection) {
                    summarySection.appendChild(viaStopsList);
                }
            }

            // Clear existing content
            viaStopsList.innerHTML = '';

            // Add header with count
            const headerDiv = document.createElement('div');
            headerDiv.className = 'summary-item border-bottom pb-2 mb-2';
            headerDiv.innerHTML = `
                <span class="summary-item-label">
                    <i class="fas fa-route me-1 text-primary"></i>
                    Total Via Stops:
                </span>
                <span class="summary-item-value fw-bold text-primary">${viaStops.length}</span>
            `;
            viaStopsList.appendChild(headerDiv);

            // Add each via stop
            viaStops.forEach((stop, index) => {
                const stopDiv = document.createElement('div');
                stopDiv.className = 'summary-item';
                stopDiv.innerHTML = `
                    <span class="summary-item-label">
                        <span class="badge bg-primary rounded-pill me-2">${index + 1}</span>
                        Stop ${index + 1}:
                    </span>
                    <span class="summary-item-value via-stop-address">${stop.address}</span>
                `;
                viaStopsList.appendChild(stopDiv);
            });

            // Show the section
            if (summarySection) {
                summarySection.style.display = 'block';
            }
        } else {
            // Hide the section if no via stops
            if (summarySection) {
                summarySection.style.display = 'none';
            }
        }
    }

    // Update extra services summary
    function updateExtraServicesSummary(bookingType) {
        const extraServices = getExtraServicesData(bookingType);
        const summarySection = document.getElementById('extra-services-summary');

        let hasServices = false;

        // Check each service and update display
        const serviceRows = {
            'meet_and_greet': 'summary-meet-greet-row',
            'child_seat': 'summary-child-seat-row',
            'wheelchair_accessible': 'summary-wheelchair-row',
            'extra_luggage': 'summary-extra-luggage-row'
        };

        Object.keys(serviceRows).forEach(service => {
            const row = document.getElementById(serviceRows[service]);
            if (row) {
                if (extraServices[service]) {
                    row.style.display = 'flex';
                    hasServices = true;
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // Show/hide the entire section
        if (summarySection) {
            summarySection.style.display = hasServices ? 'block' : 'none';
        }
    }

    // Update flight information summary
    function updateFlightInfoSummary(bookingType) {
        const summarySection = document.getElementById('flight-info-summary');

        if (bookingType === 'airport_transfer') {
            const flightNumber = document.getElementById('flight_number')?.value?.trim();
            const airline = document.getElementById('airline')?.value?.trim();

            let hasFlightInfo = false;

            // Update flight number
            const flightNumberRow = document.getElementById('summary-flight-number-row');
            if (flightNumberRow && flightNumber) {
                document.getElementById('summary-flight-number').textContent = flightNumber;
                flightNumberRow.style.display = 'flex';
                hasFlightInfo = true;
            } else if (flightNumberRow) {
                flightNumberRow.style.display = 'none';
            }

            // Update airline
            const airlineRow = document.getElementById('summary-airline-row');
            if (airlineRow && airline) {
                document.getElementById('summary-airline').textContent = airline;
                airlineRow.style.display = 'flex';
                hasFlightInfo = true;
            } else if (airlineRow) {
                airlineRow.style.display = 'none';
            }

            // Show/hide the entire section
            if (summarySection) {
                summarySection.style.display = hasFlightInfo ? 'block' : 'none';
            }
        } else {
            // Hide for non-airport transfers
            if (summarySection) {
                summarySection.style.display = 'none';
            }
        }
    }

    // Update special requests summary
    function updateSpecialRequestsSummary(bookingType) {
        const summarySection = document.getElementById('special-requests-summary');
        const notes = getNotesForBookingType(bookingType);

        if (notes) {
            document.getElementById('summary-notes').textContent = notes;
            if (summarySection) {
                summarySection.style.display = 'block';
            }
        } else {
            if (summarySection) {
                summarySection.style.display = 'none';
            }
        }
    }

    // Update extra services in fare breakdown
    function updateExtraServicesFareBreakdown(bookingType, fareDetails) {
        const extraServices = getExtraServicesData(bookingType);

        // Service pricing from settings (passed from backend)
        const servicePrices = {
            'meet_and_greet': fareDetails.extra_services?.meet_and_greet || window.extraServicesSettings?.meet_and_greet?.fee || 10.00,
            'child_seat': fareDetails.extra_services?.child_seat || window.extraServicesSettings?.child_seat?.fee || 15.00,
            'wheelchair_accessible': fareDetails.extra_services?.wheelchair_accessible || window.extraServicesSettings?.wheelchair_accessible?.fee || 0.00,
            'extra_luggage': fareDetails.extra_services?.extra_luggage || window.extraServicesSettings?.extra_luggage?.fee || 5.00
        };

        const serviceLabels = {
            'meet_and_greet': window.extraServicesSettings?.meet_and_greet?.label || 'Meet & Greet Service',
            'child_seat': window.extraServicesSettings?.child_seat?.label || 'Child Seat',
            'wheelchair_accessible': window.extraServicesSettings?.wheelchair_accessible?.label || 'Wheelchair Accessible Vehicle',
            'extra_luggage': window.extraServicesSettings?.extra_luggage?.label || 'Extra Luggage Space'
        };

        // Get or create extra services container in fare breakdown
        let extraServicesContainer = document.getElementById('fare-extra-services-container');
        if (!extraServicesContainer) {
            // Create the container if it doesn't exist
            const fareBreakdown = document.querySelector('.fare-breakdown');
            if (fareBreakdown) {
                extraServicesContainer = document.createElement('div');
                extraServicesContainer.id = 'fare-extra-services-container';

                // Insert before tax row or total row
                const taxRow = document.getElementById('summary-tax-row');
                const totalRow = document.querySelector('.fare-breakdown .border-top');

                if (taxRow) {
                    fareBreakdown.insertBefore(extraServicesContainer, taxRow);
                } else if (totalRow) {
                    fareBreakdown.insertBefore(extraServicesContainer, totalRow);
                } else {
                    fareBreakdown.appendChild(extraServicesContainer);
                }
            }
        }

        if (extraServicesContainer) {
            // Clear existing content
            extraServicesContainer.innerHTML = '';

            let hasServices = false;
            let totalExtraServicesCost = 0;

            // Add each selected service
            Object.keys(extraServices).forEach(service => {
                if (extraServices[service] && servicePrices[service] !== undefined) {
                    hasServices = true;
                    const price = parseFloat(servicePrices[service]);
                    totalExtraServicesCost += price;

                    const serviceRow = document.createElement('div');
                    serviceRow.className = 'd-flex justify-content-between align-items-center py-2';
                    serviceRow.innerHTML = `
                        <span class="text-muted">${serviceLabels[service]}</span>
                        <span>${currencySymbol}${price.toFixed(2)}</span>
                    `;
                    extraServicesContainer.appendChild(serviceRow);
                }
            });

            // Add total extra services row if there are services
            if (hasServices && totalExtraServicesCost > 0) {
                const totalRow = document.createElement('div');
                totalRow.className = 'd-flex justify-content-between align-items-center py-2 border-top';
                totalRow.innerHTML = `
                    <span class="fw-bold">Extra Services Total</span>
                    <span class="fw-bold">${currencySymbol}${totalExtraServicesCost.toFixed(2)}</span>
                `;
                extraServicesContainer.appendChild(totalRow);
            }

            // Show/hide container based on whether there are services
            extraServicesContainer.style.display = hasServices ? 'block' : 'none';
        }
    }

    // Handle form submission
    function handleFormSubmit(e) {
        try {
            // Validate step 3 (nothing to validate, just make sure we have all the data)
            const bookingType = bookingTypeInput.value;
            const vehicleId = selectedVehicleInput.value;
            const totalFare = totalFareInput.value;

            if (!bookingType || !vehicleId || !totalFare) {
                e.preventDefault();

                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please complete all steps before submitting.',
                    confirmButtonColor: '#ee393d'
                });

                return false;
            }

            // Update hidden fields with current form data
            updateHiddenFields();

            // Show loading indicator on the submit button
            const submitBtn = document.getElementById('bookNowBtn');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            }

            // Form will submit normally
            return true;
        } catch (error) {
            console.error('Error handling form submission:', error);
            e.preventDefault();

            Swal.fire({
                icon: 'error',
                title: 'Submission Error',
                text: 'An error occurred during form submission. Please try again.',
                confirmButtonColor: '#ee393d'
            });

            return false;
        }
    }

    // Update hidden fields before form submission
    function updateHiddenFields() {
        const bookingType = bookingTypeInput.value;

        // Update airport direction and ID for airport transfers
        if (bookingType === 'airport_transfer') {
            const airportDirection = document.getElementById('airport_direction')?.value;
            const airportId = document.getElementById('airport_id')?.value;

            if (airportDirection) {
                const airportDirectionHidden = document.getElementById('airport_direction_hidden');
                if (airportDirectionHidden) airportDirectionHidden.value = airportDirection;
            }

            if (airportId) {
                const airportIdHidden = document.getElementById('airport_id_hidden');
                if (airportIdHidden) airportIdHidden.value = airportId;
            }

            // Update flight information
            const flightNumber = document.getElementById('flight_number')?.value;
            const airline = document.getElementById('airline')?.value;

            if (flightNumber) {
                const flightNumberHidden = document.getElementById('flight_number_hidden');
                if (flightNumberHidden) flightNumberHidden.value = flightNumber;
            }

            if (airline) {
                const airlineHidden = document.getElementById('airline_hidden');
                if (airlineHidden) airlineHidden.value = airline;
            }
        }

        // Update pickup and return datetime
        updateDateTimeFields();
    }

    // Update date and time fields
    function updateDateTimeFields() {
        const bookingType = bookingTypeInput.value;

        let pickupDate, pickupTime, returnDate, returnTime;

        if (bookingType === 'one_way') {
            pickupDate = document.getElementById('pickup_date')?.value;
            pickupTime = document.getElementById('pickup_time')?.value;
        } else if (bookingType === 'return') {
            pickupDate = document.getElementById('return_pickup_date')?.value;
            pickupTime = document.getElementById('return_pickup_time')?.value;
            returnDate = document.getElementById('return_date')?.value;
            returnTime = document.getElementById('return_time')?.value;
        } else if (bookingType === 'airport_transfer') {
            pickupDate = document.getElementById('airport_pickup_date')?.value;
            pickupTime = document.getElementById('airport_pickup_time')?.value;
        }

        // Update pickup datetime
        if (pickupDate && pickupTime) {
            const pickupDatetimeInput = document.getElementById('pickup_datetime');
            if (pickupDatetimeInput) {
                pickupDatetimeInput.value = pickupDate + ' ' + pickupTime;
            }
        }

        // Update return datetime for return trips
        if (bookingType === 'return' && returnDate && returnTime) {
            const returnDatetimeInput = document.getElementById('return_datetime');
            if (returnDatetimeInput) {
                returnDatetimeInput.value = returnDate + ' ' + returnTime;
            }
        }
    }
});
