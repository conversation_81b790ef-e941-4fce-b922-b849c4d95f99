<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Flight details fields
            $table->string('flight_number')->nullable()->after('airport_surcharge');
            $table->string('airline')->nullable()->after('flight_number');
            $table->datetime('departure_time')->nullable()->after('airline');
            $table->datetime('arrival_time')->nullable()->after('departure_time');
            $table->string('terminal')->nullable()->after('arrival_time');
            $table->enum('flight_status', ['scheduled', 'delayed', 'cancelled', 'boarding', 'departed', 'arrived'])->nullable()->after('terminal');
            $table->text('flight_notes')->nullable()->after('flight_status');
            
            // Add indexes for better performance
            $table->index('flight_number');
            $table->index('departure_time');
            $table->index('arrival_time');
            $table->index('flight_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['flight_number']);
            $table->dropIndex(['departure_time']);
            $table->dropIndex(['arrival_time']);
            $table->dropIndex(['flight_status']);
            
            // Drop columns
            $table->dropColumn([
                'flight_number',
                'airline',
                'departure_time',
                'arrival_time',
                'terminal',
                'flight_status',
                'flight_notes'
            ]);
        });
    }
};
