@extends('layouts.admin')

@section('title', 'Email Logs')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">📋 Email Logs</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.email.outgoing') }}" class="btn btn-primary btn-sm me-2">
                            <i class="fas fa-paper-plane"></i> Outgoing Emails
                        </a>
                        <a href="{{ route('admin.email-submissions.incoming') }}" class="btn btn-success btn-sm me-2">
                            <i class="fas fa-inbox"></i> Incoming Emails
                        </a>
                        <a href="{{ route('admin.email-submissions.contact') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-envelope"></i> Contact Forms
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.email-submissions.logs') }}" class="row g-3">
                                <div class="col-md-2">
                                    <label for="type" class="form-label">Email Type</label>
                                    <select name="type" id="type" class="form-select">
                                        <option value="">All Types</option>
                                        @foreach($emailTypes as $type)
                                            <option value="{{ $type }}" {{ request('type') === $type ? 'selected' : '' }}>
                                                {{ ucfirst(str_replace('_', ' ', $type)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="direction" class="form-label">Direction</label>
                                    <select name="direction" id="direction" class="form-select">
                                        <option value="">All Directions</option>
                                        @foreach($directions as $direction)
                                            <option value="{{ $direction }}" {{ request('direction') === $direction ? 'selected' : '' }}>
                                                {{ ucfirst($direction) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">All Statuses</option>
                                        @foreach($statuses as $status)
                                            <option value="{{ $status }}" {{ request('status') === $status ? 'selected' : '' }}>
                                                {{ ucfirst($status) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label for="search" class="form-label">Search</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control" placeholder="From/To email, subject..." value="{{ request('search') }}">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        @if(request()->hasAny(['type', 'direction', 'status', 'search']))
                                            <a href="{{ route('admin.email-submissions.logs') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $logs->total() }}</h4>
                                            <p class="mb-0">Total Logs</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-list fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $logs->where('direction', 'sent')->count() }}</h4>
                                            <p class="mb-0">Sent</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-paper-plane fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $logs->where('direction', 'received')->count() }}</h4>
                                            <p class="mb-0">Received</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-inbox fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4>{{ $logs->whereIn('status', ['failed', 'bounced'])->count() }}</h4>
                                            <p class="mb-0">Failed/Bounced</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Type</th>
                                    <th>Direction</th>
                                    <th>From/To</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($logs as $log)
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ ucfirst(str_replace('_', ' ', $log->type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $log->direction_badge_class }}">
                                                {{ ucfirst($log->direction) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($log->direction === 'sent')
                                                <strong>To:</strong> {{ $log->to_email }}
                                                @if($log->from_email)
                                                    <br><small class="text-muted">From: {{ $log->from_email }}</small>
                                                @endif
                                            @else
                                                <strong>From:</strong> {{ $log->from_email }}
                                                @if($log->to_email)
                                                    <br><small class="text-muted">To: {{ $log->to_email }}</small>
                                                @endif
                                            @endif
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;" title="{{ $log->subject }}">
                                                {{ $log->subject ?: 'No Subject' }}
                                            </div>
                                            @if($log->related_type && $log->related_id)
                                                <small class="text-muted">
                                                    <i class="fas fa-link"></i> 
                                                    {{ ucfirst(str_replace('_', ' ', $log->related_type)) }} #{{ $log->related_id }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $log->status_badge_class }}">
                                                {{ ucfirst($log->status) }}
                                            </span>
                                            @if($log->error_message)
                                                <br><small class="text-danger" title="{{ $log->error_message }}">
                                                    <i class="fas fa-exclamation-circle"></i> Error
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <small>
                                                {{ $log->created_at->format('d M Y H:i') }}
                                                <br>
                                                <span class="text-muted">{{ $log->created_at->diffForHumans() }}</span>
                                            </small>
                                            @if($log->sent_at && $log->sent_at != $log->created_at)
                                                <br><small class="text-info">Sent: {{ $log->sent_at->format('H:i') }}</small>
                                            @endif
                                            @if($log->delivered_at)
                                                <br><small class="text-success">Delivered: {{ $log->delivered_at->format('H:i') }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewLogDetails({{ $log->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($log->related_type && $log->related_id)
                                                    <button type="button" class="btn btn-outline-info" onclick="viewRelated('{{ $log->related_type }}', {{ $log->related_id }})" title="View Related">
                                                        <i class="fas fa-link"></i>
                                                    </button>
                                                @endif
                                                @if($log->direction === 'received' && $log->from_email)
                                                    <a href="mailto:{{ $log->from_email }}?subject=Re: {{ urlencode($log->subject) }}" class="btn btn-outline-success" title="Reply">
                                                        <i class="fas fa-reply"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-list fa-3x mb-3"></i>
                                                <p>No email logs found</p>
                                                <small>Email logs will appear here as emails are sent and received</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($logs->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $logs->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📋 Email Log Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function viewLogDetails(logId) {
    $('#logDetailsModal').modal('show');
    $('#logDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    
    // In a real implementation, you would fetch log details via AJAX
    setTimeout(() => {
        $('#logDetailsContent').html(`
            <div class="row">
                <div class="col-md-6">
                    <strong>Log ID:</strong> ${logId}<br>
                    <strong>Type:</strong> Booking Confirmation<br>
                    <strong>Direction:</strong> <span class="badge bg-primary">Sent</span><br>
                    <strong>Status:</strong> <span class="badge bg-success">Delivered</span>
                </div>
                <div class="col-md-6">
                    <strong>From:</strong> <EMAIL><br>
                    <strong>To:</strong> <EMAIL><br>
                    <strong>Subject:</strong> Booking Confirmation<br>
                    <strong>Created:</strong> ${new Date().toLocaleString()}
                </div>
            </div>
            <hr>
            <div>
                <strong>Timeline:</strong>
                <ul class="list-unstyled mt-2">
                    <li><i class="fas fa-plus text-info"></i> Created: ${new Date().toLocaleString()}</li>
                    <li><i class="fas fa-paper-plane text-primary"></i> Sent: ${new Date().toLocaleString()}</li>
                    <li><i class="fas fa-check text-success"></i> Delivered: ${new Date().toLocaleString()}</li>
                </ul>
            </div>
            <hr>
            <div>
                <strong>Metadata:</strong>
                <div class="bg-light p-2 rounded mt-2">
                    <small>
                        <strong>Server:</strong> localhost<br>
                        <strong>Provider:</strong> SMTP<br>
                        <strong>Related:</strong> Booking #12345
                    </small>
                </div>
            </div>
        `);
    }, 500);
}

function viewRelated(type, id) {
    if (type === 'ContactSubmission') {
        window.open(`{{ route('admin.email-submissions.show-contact', '') }}/${id}`, '_blank');
    } else if (type === 'Booking') {
        window.open(`/admin/bookings/${id}`, '_blank');
    }
}
</script>
@endsection
