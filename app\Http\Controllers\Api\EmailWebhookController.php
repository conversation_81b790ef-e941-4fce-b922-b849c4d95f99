<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\EmailReceivingService;
use App\Services\EmailWebhookService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EmailWebhookController extends Controller
{
    /**
     * Handle incoming email webhooks from email providers
     * Supports SendGrid, Mailgun, Postmark, and generic webhooks
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleIncomingEmail(Request $request): JsonResponse
    {
        try {
            // Log the incoming webhook for debugging
            Log::info('Email webhook received', [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Detect email provider and process accordingly
            $provider = $this->detectEmailProvider($request);
            
            Log::info('Email provider detected', ['provider' => $provider]);

            $result = EmailWebhookService::processIncomingEmail($request->all(), $provider);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email processed successfully',
                    'email_log_id' => $result['email_log_id'] ?? null,
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'] ?? 'Failed to process email',
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Email webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Handle email bounce notifications
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleBounce(Request $request): JsonResponse
    {
        try {
            Log::info('Email bounce webhook received', [
                'data' => $request->all(),
                'ip' => $request->ip(),
            ]);

            $result = EmailWebhookService::processBounce($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Bounce processed successfully',
                'processed_count' => $result['processed_count'] ?? 0,
            ], 200);

        } catch (\Exception $e) {
            Log::error('Email bounce processing error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process bounce',
            ], 500);
        }
    }

    /**
     * Handle email delivery confirmations
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleDelivery(Request $request): JsonResponse
    {
        try {
            Log::info('Email delivery webhook received', [
                'data' => $request->all(),
                'ip' => $request->ip(),
            ]);

            $result = EmailWebhookService::processDelivery($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Delivery status updated successfully',
                'processed_count' => $result['processed_count'] ?? 0,
            ], 200);

        } catch (\Exception $e) {
            Log::error('Email delivery processing error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process delivery status',
            ], 500);
        }
    }

    /**
     * Handle email open tracking
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleOpen(Request $request): JsonResponse
    {
        try {
            Log::info('Email open webhook received', [
                'data' => $request->all(),
                'ip' => $request->ip(),
            ]);

            $result = EmailWebhookService::processOpen($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Email open tracked successfully',
            ], 200);

        } catch (\Exception $e) {
            Log::error('Email open tracking error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track email open',
            ], 500);
        }
    }

    /**
     * Test endpoint for webhook setup
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function test(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Email webhook endpoint is working',
            'timestamp' => now()->toISOString(),
            'received_data' => $request->all(),
            'supported_providers' => [
                'sendgrid',
                'mailgun',
                'postmark',
                'ses',
                'generic'
            ],
        ]);
    }

    /**
     * Get webhook configuration information
     *
     * @return JsonResponse
     */
    public function config(): JsonResponse
    {
        return response()->json([
            'webhook_urls' => [
                'incoming_email' => route('api.email.webhook.incoming'),
                'bounce' => route('api.email.webhook.bounce'),
                'delivery' => route('api.email.webhook.delivery'),
                'open' => route('api.email.webhook.open'),
                'test' => route('api.email.webhook.test'),
            ],
            'supported_providers' => [
                'sendgrid' => [
                    'name' => 'SendGrid',
                    'webhook_url' => route('api.email.webhook.incoming'),
                    'events' => ['inbound_parse', 'bounce', 'delivered', 'open'],
                ],
                'mailgun' => [
                    'name' => 'Mailgun',
                    'webhook_url' => route('api.email.webhook.incoming'),
                    'events' => ['delivered', 'bounced', 'opened'],
                ],
                'postmark' => [
                    'name' => 'Postmark',
                    'webhook_url' => route('api.email.webhook.incoming'),
                    'events' => ['delivery', 'bounce', 'open'],
                ],
                'ses' => [
                    'name' => 'Amazon SES',
                    'webhook_url' => route('api.email.webhook.incoming'),
                    'events' => ['delivery', 'bounce', 'complaint'],
                ],
            ],
            'expected_fields' => [
                'from' => 'Sender email address',
                'to' => 'Recipient email address',
                'subject' => 'Email subject',
                'text' => 'Plain text content',
                'html' => 'HTML content (optional)',
                'timestamp' => 'Email timestamp',
            ],
        ]);
    }

    /**
     * Detect email provider from webhook request
     *
     * @param Request $request
     * @return string
     */
    private function detectEmailProvider(Request $request): string
    {
        $userAgent = $request->userAgent();
        $headers = $request->headers->all();
        
        // Check for SendGrid
        if ($request->has('dkim') || $request->has('SPF') || strpos($userAgent, 'SendGrid') !== false) {
            return 'sendgrid';
        }
        
        // Check for Mailgun
        if ($request->has('signature') || $request->has('token') || strpos($userAgent, 'Mailgun') !== false) {
            return 'mailgun';
        }
        
        // Check for Postmark
        if ($request->has('MessageID') || isset($headers['x-postmark-server-token'])) {
            return 'postmark';
        }
        
        // Check for Amazon SES
        if ($request->has('Type') && $request->has('Message') || isset($headers['x-amz-sns-message-type'])) {
            return 'ses';
        }
        
        // Default to generic
        return 'generic';
    }

    /**
     * Validate webhook signature (implement based on provider)
     *
     * @param Request $request
     * @param string $provider
     * @return bool
     */
    private function validateWebhookSignature(Request $request, string $provider): bool
    {
        // Implement signature validation based on provider
        // This is a placeholder - implement actual validation
        
        switch ($provider) {
            case 'sendgrid':
                // Implement SendGrid signature validation
                return true;
                
            case 'mailgun':
                // Implement Mailgun signature validation
                return true;
                
            case 'postmark':
                // Implement Postmark signature validation
                return true;
                
            case 'ses':
                // Implement SES signature validation
                return true;
                
            default:
                return true;
        }
    }
}
