<?php

namespace Database\Factories;

use App\Models\Airport;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Airport>
 */
class AirportFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Airport::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $airports = [
            ['name' => 'Heathrow Airport', 'code' => 'LHR', 'city' => 'London', 'country' => 'United Kingdom'],
            ['name' => 'Gatwick Airport', 'code' => 'LGW', 'city' => 'London', 'country' => 'United Kingdom'],
            ['name' => 'Manchester Airport', 'code' => 'MAN', 'city' => 'Manchester', 'country' => 'United Kingdom'],
            ['name' => 'Birmingham Airport', 'code' => 'BHX', 'city' => 'Birmingham', 'country' => 'United Kingdom'],
            ['name' => 'Edinburgh Airport', 'code' => 'EDI', 'city' => 'Edinburgh', 'country' => 'United Kingdom'],
            ['name' => 'Glasgow Airport', 'code' => 'GLA', 'city' => 'Glasgow', 'country' => 'United Kingdom'],
            ['name' => 'Stansted Airport', 'code' => 'STN', 'city' => 'London', 'country' => 'United Kingdom'],
            ['name' => 'Luton Airport', 'code' => 'LTN', 'city' => 'London', 'country' => 'United Kingdom'],
        ];

        $airport = $this->faker->randomElement($airports);

        return [
            'name' => $airport['name'],
            'code' => $airport['code'],
            'city' => $airport['city'],
            'country' => $airport['country'],
            'country_code' => 'GB', // Default to UK
            'latitude' => $this->faker->latitude(49.0, 61.0), // UK latitude range
            'longitude' => $this->faker->longitude(-8.0, 2.0), // UK longitude range
            'address' => $this->faker->address(),
        ];
    }

    /**
     * Indicate that the airport is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a specific airport.
     */
    public function heathrow(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Heathrow Airport',
            'code' => 'LHR',
            'city' => 'London',
            'country' => 'United Kingdom',
            'latitude' => 51.4700,
            'longitude' => -0.4543,
        ]);
    }

    /**
     * Create a specific airport.
     */
    public function gatwick(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Gatwick Airport',
            'code' => 'LGW',
            'city' => 'London',
            'country' => 'United Kingdom',
            'latitude' => 51.1481,
            'longitude' => -0.1903,
        ]);
    }

    /**
     * Create a test airport.
     */
    public function test(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Test Airport',
            'code' => 'TST',
            'city' => 'Test City',
            'country' => 'Test Country',
            'latitude' => 51.5074,
            'longitude' => -0.1278,
        ]);
    }
}
