<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'direction',
        'from_email',
        'to_email',
        'subject',
        'content',
        'status',
        'related_type',
        'related_id',
        'metadata',
        'error_message',
        'sent_at',
        'delivered_at',
        'bounced_at',
        'processed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'bounced_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the status badge class for display
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'sent' => 'bg-primary',
            'delivered' => 'bg-success',
            'bounced' => 'bg-danger',
            'failed' => 'bg-danger',
            'processed' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    /**
     * Get the direction badge class for display
     */
    public function getDirectionBadgeClassAttribute(): string
    {
        return match($this->direction) {
            'sent' => 'badge-primary',
            'received' => 'badge-success',
            'bounced' => 'badge-danger',
            'failed' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    /**
     * Get the related model
     */
    public function related()
    {
        if (!$this->related_type || !$this->related_id) {
            return null;
        }

        $modelClass = "App\\Models\\{$this->related_type}";

        if (class_exists($modelClass)) {
            return $modelClass::find($this->related_id);
        }

        return null;
    }

    /**
     * Scope for sent emails
     */
    public function scopeSent($query)
    {
        return $query->where('direction', 'sent');
    }

    /**
     * Scope for received emails
     */
    public function scopeReceived($query)
    {
        return $query->where('direction', 'received');
    }

    /**
     * Scope for failed emails
     */
    public function scopeFailed($query)
    {
        return $query->whereIn('status', ['failed', 'bounced']);
    }

    /**
     * Scope for successful emails
     */
    public function scopeSuccessful($query)
    {
        return $query->whereIn('status', ['sent', 'delivered', 'processed']);
    }

    /**
     * Scope for recent emails
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for specific email type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}
