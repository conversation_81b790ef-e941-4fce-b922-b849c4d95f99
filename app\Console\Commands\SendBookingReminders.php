<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\User;
use App\Notifications\BookingReminder;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendBookingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder notifications for upcoming bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Sending booking reminders...');

        // Get bookings that are scheduled for tomorrow and haven't been reminded yet
        $upcomingBookings = Booking::with(['user', 'driver', 'vehicle'])
            ->whereIn('status', ['confirmed', 'assigned'])
            ->whereDate('pickup_date', Carbon::tomorrow())
            ->whereNull('reminder_sent_at')
            ->get();

        $remindersSent = 0;

        foreach ($upcomingBookings as $booking) {
            try {
                $emailsSent = 0;

                // Send email reminder to client
                if ($booking->user) {
                    if (EmailService::sendBookingReminder($booking, 'client')) {
                        $emailsSent++;
                        $this->line("✓ Email reminder sent to client for booking #{$booking->booking_number}");
                    }

                    // Also send notification
                    $booking->user->notify(new BookingReminder($booking, 'client'));
                }

                // Send email reminder to driver if assigned
                if ($booking->driver) {
                    if (EmailService::sendBookingReminder($booking, 'driver')) {
                        $emailsSent++;
                        $this->line("✓ Email reminder sent to driver for booking #{$booking->booking_number}");
                    }

                    // Also send notification
                    $booking->driver->notify(new BookingReminder($booking, 'driver'));
                }

                // Mark as reminded only if at least one email was sent
                if ($emailsSent > 0) {
                    $booking->update(['reminder_sent_at' => now()]);
                    $remindersSent++;
                }

            } catch (\Exception $e) {
                $this->error("Failed to send reminder for booking #{$booking->booking_number}: " . $e->getMessage());
                Log::error('Booking reminder command error', [
                    'booking_id' => $booking->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("Sent {$remindersSent} booking reminders.");

        return Command::SUCCESS;
    }
}
