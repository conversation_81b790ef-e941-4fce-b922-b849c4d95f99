<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'booking_type' => 'required|in:one_way,return,hourly,airport_transfer',
            'vehicle_id' => 'required|exists:vehicles,id',
            'pickup_address' => 'required|string|max:255',
            'pickup_lat' => 'nullable|numeric|between:-90,90',
            'pickup_lng' => 'nullable|numeric|between:-180,180',
            'pickup_date' => 'required|date|after:now',
            'pickup_time' => 'required|date_format:H:i',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:500',
            'distance' => 'nullable|numeric|min:0',
            'distance_value' => 'nullable|integer|min:0',
            'duration_value' => 'nullable|integer|min:0',
            'via_stops' => 'nullable|array|max:5',
            'via_stops.*.address' => 'required_with:via_stops|string|max:255',
            'via_stops.*.lat' => 'nullable|numeric|between:-90,90',
            'via_stops.*.lng' => 'nullable|numeric|between:-180,180',

            // Flight information fields (optional for all booking types)
            'flight_number' => 'nullable|string|max:20',
            'airline' => 'nullable|string|max:100',
            'departure_time' => 'nullable|date',
            'arrival_time' => 'nullable|date|after_or_equal:departure_time',
            'terminal' => 'nullable|string|max:50',
            'flight_status' => 'nullable|in:scheduled,delayed,cancelled,boarding,departed,arrived',
            'flight_notes' => 'nullable|string|max:500',
        ];

        // Add conditional rules based on booking type
        if ($this->input('booking_type') === 'hourly') {
            $rules['duration_hours'] = 'required|integer|min:1|max:24';
        } else {
            $rules['dropoff_address'] = 'required|string|max:255';
            $rules['dropoff_lat'] = 'nullable|numeric|between:-90,90';
            $rules['dropoff_lng'] = 'nullable|numeric|between:-180,180';
        }

        if ($this->input('booking_type') === 'return') {
            $rules['return_date'] = 'required|date|after:pickup_date';
            $rules['return_time'] = 'required|date_format:H:i';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'booking_type.required' => 'Please select a booking type.',
            'booking_type.in' => 'Invalid booking type selected.',
            'vehicle_id.required' => 'Please select a vehicle.',
            'vehicle_id.exists' => 'Selected vehicle is not available.',
            'pickup_address.required' => 'Pickup address is required.',
            'pickup_date.required' => 'Pickup date is required.',
            'pickup_date.after' => 'Pickup date must be in the future.',
            'pickup_time.required' => 'Pickup time is required.',
            'pickup_time.date_format' => 'Invalid pickup time format.',
            'dropoff_address.required' => 'Dropoff address is required for this booking type.',
            'duration_hours.required' => 'Duration is required for hourly bookings.',
            'duration_hours.min' => 'Minimum duration is 1 hour.',
            'duration_hours.max' => 'Maximum duration is 24 hours.',
            'return_date.required' => 'Return date is required for return trips.',
            'return_date.after' => 'Return date must be after pickup date.',
            'return_time.required' => 'Return time is required for return trips.',
            'amount.required' => 'Booking amount is required.',
            'amount.numeric' => 'Booking amount must be a valid number.',
            'amount.min' => 'Booking amount must be greater than 0.',

            // Flight information messages
            'flight_number.max' => 'Flight number cannot exceed 20 characters.',
            'airline.max' => 'Airline name cannot exceed 100 characters.',
            'departure_time.date' => 'Please enter a valid departure time.',
            'arrival_time.date' => 'Please enter a valid arrival time.',
            'arrival_time.after_or_equal' => 'Arrival time must be after or equal to departure time.',
            'terminal.max' => 'Terminal information cannot exceed 50 characters.',
            'flight_status.in' => 'Please select a valid flight status.',
            'flight_notes.max' => 'Flight notes cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'pickup_address' => 'pickup address',
            'dropoff_address' => 'dropoff address',
            'pickup_date' => 'pickup date',
            'pickup_time' => 'pickup time',
            'return_date' => 'return date',
            'return_time' => 'return time',
            'duration_hours' => 'duration',
            'vehicle_id' => 'vehicle',
            'booking_type' => 'booking type',

            // Flight information attributes
            'flight_number' => 'flight number',
            'airline' => 'airline',
            'departure_time' => 'departure time',
            'arrival_time' => 'arrival time',
            'terminal' => 'terminal',
            'flight_status' => 'flight status',
            'flight_notes' => 'flight notes',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Combine date and time for pickup
        if ($this->has('pickup_date') && $this->has('pickup_time')) {
            $this->merge([
                'pickup_datetime' => $this->pickup_date . ' ' . $this->pickup_time,
            ]);
        }

        // Combine date and time for return if applicable
        if ($this->has('return_date') && $this->has('return_time')) {
            $this->merge([
                'return_datetime' => $this->return_date . ' ' . $this->return_time,
            ]);
        }
    }
}
