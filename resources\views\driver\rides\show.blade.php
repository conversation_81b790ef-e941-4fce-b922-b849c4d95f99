@extends('layouts.driver')

@section('title', 'Ride Details')

@section('styles')
<style>
    .content-wrapper {
        padding: 20px;
    }



    .ride-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
    }

    .ride-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .ride-card .card-body {
        padding: 30px;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .status-confirmed {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-assigned {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-in-progress {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .detail-section {
        margin-bottom: 30px;
    }

    .detail-section h5 {
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .detail-row {
        display: flex;
        margin-bottom: 15px;
    }

    .detail-label {
        width: 150px;
        font-weight: 600;
    }

    .detail-value {
        flex: 1;
    }

    .vehicle-img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .client-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .client-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 15px;
    }

    .client-details {
        flex: 1;
    }

    .client-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .client-contact {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .map-container {
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .cancel-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Ride Details</h2>
                <a href="{{ $ride->driver_id ? route('driver.rides.my-rides') : route('driver.rides.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to {{ $ride->driver_id ? 'My Rides' : 'Available Rides' }}
                </a>
            </div>

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="card ride-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Booking #{{ $ride->booking_number }}</h4>
                    <span class="status-badge status-{{ str_replace('_', '-', $ride->status) }}">
                        {{ ucfirst(str_replace('_', ' ', $ride->status)) }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-section">
                                <h5>Ride Information</h5>
                                <div class="detail-row">
                                    <div class="detail-label">Booking Type:</div>
                                    <div class="detail-value">{{ ucfirst($ride->booking_type) }}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Pickup Address:</div>
                                    <div class="detail-value">{{ $ride->pickup_address }}</div>
                                </div>

                                @if($ride->via_stops && count($ride->via_stops) > 0)
                                    <div class="detail-row">
                                        <div class="detail-label">Via Stops:</div>
                                        <div class="detail-value">
                                            @foreach($ride->via_stops as $index => $viaStop)
                                                <div class="mb-1">
                                                    <span class="badge bg-primary me-1">{{ $index + 1 }}</span>
                                                    {{ $viaStop['address'] ?? 'Address not specified' }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif

                                @if ($ride->booking_type !== 'hourly')
                                    <div class="detail-row">
                                        <div class="detail-label">Dropoff Address:</div>
                                        <div class="detail-value">{{ $ride->dropoff_address }}</div>
                                    </div>
                                @endif
                                <div class="detail-row">
                                    <div class="detail-label">Pickup Date:</div>
                                    <div class="detail-value">{{ $ride->pickup_date->format('M d, Y h:i A') }}</div>
                                </div>
                                @if ($ride->booking_type === 'return' && $ride->return_date)
                                    <div class="detail-row">
                                        <div class="detail-label">Return Date:</div>
                                        <div class="detail-value">{{ $ride->return_date->format('M d, Y h:i A') }}</div>
                                    </div>
                                @endif
                                @if ($ride->booking_type === 'hourly' && $ride->duration_hours)
                                    <div class="detail-row">
                                        <div class="detail-label">Duration:</div>
                                        <div class="detail-value">{{ $ride->duration_hours }} {{ $ride->duration_hours > 1 ? 'hours' : 'hour' }}</div>
                                    </div>
                                @endif
                                <div class="detail-row">
                                    <div class="detail-label">Fare:</div>
                                    <div class="detail-value">@currency(){{ number_format($ride->amount, 2) }}</div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5>Client Information</h5>
                                <div class="client-info">
                                    @if ($ride->user->profile_photo)
                                        <img src="{{ asset('storage/' . $ride->user->profile_photo) }}" class="client-img" alt="Client">
                                    @else
                                        <img src="https://via.placeholder.com/60x60?text=Client" class="client-img" alt="Client">
                                    @endif
                                    <div class="client-details">
                                        <div class="client-name">Client #{{ substr($ride->user->id, 0, 5) }}</div>
                                        <div class="client-contact">
                                            <span class="badge bg-info">Booking #{{ $ride->booking_number }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="detail-section">
                                <h5>Vehicle Information</h5>
                                @if ($ride->vehicle->image)
                                    <img src="{{ asset('storage/' . $ride->vehicle->image) }}" class="vehicle-img" alt="{{ $ride->vehicle->name }}">
                                @else
                                    <img src="https://via.placeholder.com/400x200?text=Vehicle" class="vehicle-img" alt="{{ $ride->vehicle->name }}">
                                @endif
                                <div class="detail-row">
                                    <div class="detail-label">Vehicle:</div>
                                    <div class="detail-value">{{ $ride->vehicle->name }}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Type:</div>
                                    <div class="detail-value">{{ $ride->vehicle->type }}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Model:</div>
                                    <div class="detail-value">{{ $ride->vehicle->model }}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Seats:</div>
                                    <div class="detail-value">{{ $ride->vehicle->seats }}</div>
                                </div>
                            </div>

                            <div class="map-container">
                                <iframe
                                    width="100%"
                                    height="100%"
                                    frameborder="0"
                                    style="border:0"
                                    src="https://www.google.com/maps/embed/v1/directions?key={{ env('GOOGLE_MAPS_API_KEY') }}&origin={{ urlencode($ride->pickup_address) }}&destination={{ urlencode($ride->dropoff_address) }}"
                                    allowfullscreen
                                ></iframe>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        @if (!$ride->driver_id)
                            <form action="{{ route('driver.rides.accept', $ride->id) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-primary">Accept Ride</button>
                            </form>
                        @elseif ($ride->status === 'completed')
                            <a href="{{ route('driver.rides.summary', $ride->id) }}" class="btn btn-info">
                                <i class="fas fa-file-alt me-1"></i> View Summary
                            </a>
                        @elseif ($ride->status === 'assigned')
                            <form action="{{ route('driver.rides.start', $ride->id) }}" method="POST" class="d-inline me-2">
                                @csrf
                                <button type="submit" class="btn btn-success">Start Ride</button>
                            </form>

                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                Cancel Ride
                            </button>
                        @elseif ($ride->status === 'in_progress')
                            <a href="{{ route('driver.rides.track', $ride->id) }}" class="btn btn-primary me-2">
                                <i class="fas fa-map-marker-alt me-1"></i> Track Ride
                            </a>

                            <form action="{{ route('driver.rides.complete', $ride->id) }}" method="POST" class="d-inline me-2">
                                @csrf
                                <button type="submit" class="btn btn-success">Complete Ride</button>
                            </form>

                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                Cancel Ride
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelModalLabel">Cancel Ride</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('driver.rides.cancel', $ride->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Are you sure you want to cancel this ride? This action cannot be undone.</p>
                    <div class="mb-3">
                        <label for="cancel_reason" class="form-label">Reason for Cancellation</label>
                        <textarea class="form-control" id="cancel_reason" name="cancel_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Cancel Ride</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
