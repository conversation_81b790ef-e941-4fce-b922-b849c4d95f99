<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'vehicle_id',
        'driver_id',
        'booking_number',
        'booking_type',
        'pickup_address',
        'pickup_lat',
        'pickup_lng',
        'dropoff_address',
        'dropoff_lat',
        'dropoff_lng',
        'via_stops',
        'via_charges',
        'via_count',
        'pickup_airport_id',
        'dropoff_airport_id',
        'airport_direction',
        'airport_surcharge',
        'flight_number',
        'airline',
        'departure_time',
        'arrival_time',
        'terminal',
        'flight_status',
        'flight_notes',
        'pickup_date',
        'return_date',
        'duration_hours',
        'distance',
        'distance_value',
        'duration_value',
        'amount',
        'status',
        'payment_status',
        'notes',
        'started_at',
        'completed_at',
        'cancelled_at',
        'cancellation_reason',
        'cancelled_by',
        'location_updates',
        'rating',
        'review',
        'reviewed_at',
        'fare_details',
        'payment_processed_at',
        'reminder_sent_at',
        'meet_and_greet',
        'child_seat',
        'wheelchair_accessible',
        'extra_luggage',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'pickup_date' => 'datetime',
        'return_date' => 'datetime',
        'departure_time' => 'datetime',
        'arrival_time' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'payment_processed_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
        'distance' => 'decimal:2',
        'distance_value' => 'decimal:2',
        'amount' => 'decimal:2',
        'airport_surcharge' => 'decimal:2',
        'via_charges' => 'decimal:2',
        'via_count' => 'integer',
        'duration_hours' => 'integer',
        'duration_value' => 'integer',
        'rating' => 'integer',
        'location_updates' => 'json',
        'fare_details' => 'json',
        'via_stops' => 'json',
        'meet_and_greet' => 'boolean',
        'child_seat' => 'boolean',
        'wheelchair_accessible' => 'boolean',
        'extra_luggage' => 'boolean',
    ];

    /**
     * Get the user that made the booking
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the vehicle for this booking
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the driver assigned to this booking
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    /**
     * Get the pickup airport for the booking
     */
    public function pickupAirport()
    {
        return $this->belongsTo(Airport::class, 'pickup_airport_id');
    }

    /**
     * Get the dropoff airport for the booking
     */
    public function dropoffAirport()
    {
        return $this->belongsTo(Airport::class, 'dropoff_airport_id');
    }

    /**
     * Get the payment for this booking
     */
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Get the payments for this booking
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the history entries for this booking
     */
    public function history()
    {
        return $this->hasMany(BookingHistory::class)->orderBy('created_at', 'desc');
    }

    /**
     * Add a history entry for this booking
     *
     * @param string $action
     * @param array $details
     * @param int|null $userId
     * @return \App\Models\BookingHistory
     */
    public function addHistory($action, $details = [], $userId = null)
    {
        $userId = $userId ?? auth()->id();

        return $this->history()->create([
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'status_before' => $this->getOriginal('status'),
            'status_after' => $this->status,
        ]);
    }

    /**
     * Generate a unique booking number
     */
    public static function generateBookingNumber()
    {
        $prefix = 'YNR';
        $timestamp = now()->format('YmdHis');
        $random = rand(1000, 9999);

        return $prefix . $timestamp . $random;
    }

    /**
     * Get the formatted amount attribute
     *
     * @return string
     */
    public function getFormattedAmountAttribute()
    {
        return \App\Helpers\SettingsHelper::formatPrice($this->amount);
    }

    /**
     * Get the status badge class for display
     *
     * @return string
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge-warning',
            'confirmed' => 'badge-info',
            'assigned' => 'badge-primary',
            'in_progress' => 'badge-success',
            'completed' => 'badge-success',
            'cancelled' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    /**
     * Get the payment status badge class for display
     *
     * @return string
     */
    public function getPaymentStatusBadgeClassAttribute()
    {
        return match($this->payment_status) {
            'pending' => 'badge-warning',
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'refunded' => 'badge-info',
            default => 'badge-secondary'
        };
    }



    /**
     * Get the status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute()
    {
        $statusClasses = [
            'pending' => 'bg-warning',
            'confirmed' => 'bg-info',
            'completed' => 'bg-success',
            'cancelled' => 'bg-danger',
            'in_progress' => 'bg-primary',
        ];

        $class = $statusClasses[$this->status] ?? 'bg-secondary';

        return '<span class="badge ' . $class . '">' . ucfirst($this->status) . '</span>';
    }

    /**
     * Check if this booking has flight details
     *
     * @return bool
     */
    public function hasFlightDetails()
    {
        return !empty($this->flight_number) || !empty($this->airline);
    }

    /**
     * Get formatted flight information
     *
     * @return string|null
     */
    public function getFormattedFlightInfoAttribute()
    {
        if (!$this->hasFlightDetails()) {
            return null;
        }

        $info = [];

        if ($this->airline) {
            $info[] = $this->airline;
        }

        if ($this->flight_number) {
            $info[] = $this->flight_number;
        }

        return implode(' ', $info);
    }

    /**
     * Get flight status badge class
     *
     * @return string
     */
    public function getFlightStatusBadgeClassAttribute()
    {
        return match($this->flight_status) {
            'scheduled' => 'badge-info',
            'delayed' => 'badge-warning',
            'cancelled' => 'badge-danger',
            'boarding' => 'badge-primary',
            'departed' => 'badge-success',
            'arrived' => 'badge-success',
            default => 'badge-secondary'
        };
    }

    /**
     * Check if booking is an airport transfer
     *
     * @return bool
     */
    public function isAirportTransfer()
    {
        return $this->booking_type === 'airport_transfer';
    }


}
