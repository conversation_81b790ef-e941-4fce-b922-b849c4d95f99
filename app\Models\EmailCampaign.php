<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailCampaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subject',
        'content',
        'template_id',
        'recipient_type',
        'recipient_criteria',
        'status',
        'scheduled_at',
        'sent_at',
        'completed_at',
        'total_recipients',
        'sent_count',
        'delivered_count',
        'failed_count',
        'bounce_count',
        'open_count',
        'click_count',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'recipient_criteria' => 'array',
        'metadata' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'completed_at' => 'datetime',
        'total_recipients' => 'integer',
        'sent_count' => 'integer',
        'delivered_count' => 'integer',
        'failed_count' => 'integer',
        'bounce_count' => 'integer',
        'open_count' => 'integer',
        'click_count' => 'integer',
    ];

    /**
     * Get the template associated with this campaign
     */
    public function template()
    {
        return $this->belongsTo(EmailTemplate::class, 'template_id');
    }

    /**
     * Get the user who created this campaign
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get campaign recipients
     */
    public function recipients()
    {
        return $this->hasMany(EmailCampaignRecipient::class, 'campaign_id');
    }

    /**
     * Get the status badge class for display
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'draft' => 'bg-secondary',
            'scheduled' => 'bg-info',
            'sending' => 'bg-warning',
            'sent' => 'bg-primary',
            'completed' => 'bg-success',
            'failed' => 'bg-danger',
            'cancelled' => 'bg-dark',
            default => 'bg-secondary'
        };
    }

    /**
     * Get delivery rate percentage
     */
    public function getDeliveryRateAttribute(): float
    {
        if ($this->sent_count === 0) {
            return 0;
        }

        return round(($this->delivered_count / $this->sent_count) * 100, 2);
    }

    /**
     * Get open rate percentage
     */
    public function getOpenRateAttribute(): float
    {
        if ($this->delivered_count === 0) {
            return 0;
        }

        return round(($this->open_count / $this->delivered_count) * 100, 2);
    }

    /**
     * Get click rate percentage
     */
    public function getClickRateAttribute(): float
    {
        if ($this->delivered_count === 0) {
            return 0;
        }

        return round(($this->click_count / $this->delivered_count) * 100, 2);
    }

    /**
     * Get bounce rate percentage
     */
    public function getBounceRateAttribute(): float
    {
        if ($this->sent_count === 0) {
            return 0;
        }

        return round(($this->bounce_count / $this->sent_count) * 100, 2);
    }

    /**
     * Scope for draft campaigns
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope for scheduled campaigns
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for sent campaigns
     */
    public function scopeSent($query)
    {
        return $query->whereIn('status', ['sent', 'completed']);
    }

    /**
     * Scope for active campaigns
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['scheduled', 'sending']);
    }

    /**
     * Check if campaign can be edited
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'scheduled']);
    }

    /**
     * Check if campaign can be sent
     */
    public function canBeSent(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if campaign can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled', 'sending']);
    }

    /**
     * Get recipients based on criteria
     */
    public function getTargetRecipients(): \Illuminate\Database\Eloquent\Collection
    {
        $query = User::query();

        // Apply recipient type filter
        if ($this->recipient_type !== 'all') {
            $query->where('role', $this->recipient_type);
        }

        // Apply additional criteria
        if (!empty($this->recipient_criteria)) {
            foreach ($this->recipient_criteria as $criterion => $value) {
                switch ($criterion) {
                    case 'active_only':
                        if ($value) {
                            $query->where('is_active', true);
                        }
                        break;
                    case 'has_bookings':
                        if ($value) {
                            $query->whereHas('bookings');
                        }
                        break;
                    case 'registration_date_from':
                        if ($value) {
                            $query->whereDate('created_at', '>=', $value);
                        }
                        break;
                    case 'registration_date_to':
                        if ($value) {
                            $query->whereDate('created_at', '<=', $value);
                        }
                        break;
                    case 'last_booking_from':
                        if ($value) {
                            $query->whereHas('bookings', function($q) use ($value) {
                                $q->whereDate('created_at', '>=', $value);
                            });
                        }
                        break;
                    case 'exclude_unsubscribed':
                        if ($value) {
                            $query->whereHas('notificationSettings', function($q) {
                                $q->where('email_promotions', true);
                            });
                        }
                        break;
                }
            }
        }

        return $query->get();
    }

    /**
     * Update campaign statistics
     */
    public function updateStatistics(): void
    {
        $recipients = $this->recipients();

        $this->update([
            'total_recipients' => $recipients->count(),
            'sent_count' => $recipients->where('status', 'sent')->count(),
            'delivered_count' => $recipients->where('status', 'delivered')->count(),
            'failed_count' => $recipients->where('status', 'failed')->count(),
            'bounce_count' => $recipients->where('status', 'bounced')->count(),
            'open_count' => $recipients->where('opened_at', '!=', null)->count(),
            'click_count' => $recipients->where('clicked_at', '!=', null)->count(),
        ]);
    }

    /**
     * Render subject with variables replaced
     */
    public function renderSubject(array $data = []): string
    {
        $subject = $this->subject;

        foreach ($data as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
        }

        return $subject;
    }

    /**
     * Render content with variables replaced
     */
    public function renderContent(array $data = []): string
    {
        $content = $this->content;

        foreach ($data as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        return $content;
    }


    /**
     * Get opened count attribute
     */
    public function getOpenedCountAttribute(): int
    {
        return $this->open_count ?? 0;
    }

    /**
     * Get clicked count attribute
     */
    public function getClickedCountAttribute(): int
    {
        return $this->click_count ?? 0;
    }

    /**
     * Get unsubscribe rate attribute
     */
    public function getUnsubscribeRateAttribute(): float
    {
        // This would need to be implemented based on unsubscribe tracking
        return 0;
    }
}
