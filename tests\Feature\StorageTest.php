<?php

namespace Tests\Feature;

use App\Services\StorageService;
use App\Helpers\ImageHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class StorageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure storage directories exist
        Storage::fake('public');
    }

    /** @test */
    public function it_can_upload_a_file()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $this->assertNotFalse($path);
        $this->assertTrue(Storage::disk('public')->exists($path));
    }

    /** @test */
    public function it_validates_file_size()
    {
        // Create a large file (simulate)
        $file = UploadedFile::fake()->image('large.jpg', 2000, 2000)->size(15000); // 15MB
        
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $this->assertFalse($path);
    }

    /** @test */
    public function it_validates_file_type()
    {
        $file = UploadedFile::fake()->create('test.exe', 1000);
        
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $this->assertFalse($path);
    }

    /** @test */
    public function it_generates_unique_filenames()
    {
        $file1 = UploadedFile::fake()->image('test.jpg');
        $file2 = UploadedFile::fake()->image('test.jpg');
        
        $path1 = StorageService::uploadFile($file1, 'test-uploads', 'public');
        $path2 = StorageService::uploadFile($file2, 'test-uploads', 'public');
        
        $this->assertNotEquals($path1, $path2);
    }

    /** @test */
    public function it_creates_directories_automatically()
    {
        $file = UploadedFile::fake()->image('test.jpg');
        
        $path = StorageService::uploadFile($file, 'new-directory/sub-directory', 'public');
        
        $this->assertNotFalse($path);
        $this->assertTrue(Storage::disk('public')->exists('new-directory/sub-directory'));
    }

    /** @test */
    public function it_can_optimize_images()
    {
        $file = UploadedFile::fake()->image('test.jpg', 1920, 1080);
        
        $options = [
            'optimize' => true,
            'max_width' => 800,
            'max_height' => 600,
            'quality' => 85
        ];
        
        $path = StorageService::uploadFile($file, 'optimized', 'public', null, $options);
        
        $this->assertNotFalse($path);
        $this->assertTrue(Storage::disk('public')->exists($path));
    }

    /** @test */
    public function image_helper_returns_correct_urls()
    {
        $file = UploadedFile::fake()->image('vehicle.jpg');
        Storage::disk('public')->putFileAs('vehicles', $file, 'test-vehicle.jpg');
        
        $url = ImageHelper::getVehicleImageUrl('vehicles/test-vehicle.jpg');
        
        $this->assertStringContains('storage/vehicles/test-vehicle.jpg', $url);
    }

    /** @test */
    public function image_helper_returns_placeholder_for_missing_images()
    {
        $url = ImageHelper::getVehicleImageUrl('non-existent.jpg');
        
        $this->assertStringContains('unsplash.com', $url);
    }

    /** @test */
    public function it_can_check_storage_status()
    {
        $status = StorageService::getStorageStatus();
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('storage_link_exists', $status);
        $this->assertArrayHasKey('public_disk_accessible', $status);
        $this->assertArrayHasKey('directories_exist', $status);
    }

    /** @test */
    public function it_can_delete_files()
    {
        $file = UploadedFile::fake()->image('test.jpg');
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $this->assertTrue(Storage::disk('public')->exists($path));
        
        $deleted = StorageService::deleteFile($path, 'public');
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('public')->exists($path));
    }

    /** @test */
    public function it_handles_upload_errors_gracefully()
    {
        // Simulate an invalid file
        $file = UploadedFile::fake()->create('', 0);
        
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $this->assertFalse($path);
    }

    /** @test */
    public function it_can_get_file_info()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $info = StorageService::getFileInfo($path, 'public');
        
        $this->assertIsArray($info);
        $this->assertArrayHasKey('size', $info);
        $this->assertArrayHasKey('mime_type', $info);
        $this->assertArrayHasKey('last_modified', $info);
    }

    /** @test */
    public function it_can_create_thumbnails()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $path = StorageService::uploadFile($file, 'test-uploads', 'public');
        
        $thumbnailPath = StorageService::createThumbnail($path, 'public', 300, 200);
        
        if ($thumbnailPath) {
            $this->assertTrue(Storage::disk('public')->exists($thumbnailPath));
        } else {
            // Thumbnail creation might fail if Intervention Image is not installed
            $this->markTestSkipped('Thumbnail creation requires Intervention Image package');
        }
    }

    /** @test */
    public function profile_image_helper_generates_avatar_urls()
    {
        $url = ImageHelper::getProfileImageUrl(null, 'John Doe');
        
        $this->assertStringContains('ui-avatars.com', $url);
        $this->assertStringContains('John+Doe', $url);
    }

    /** @test */
    public function it_can_handle_different_image_types()
    {
        $types = ['jpg', 'png', 'gif', 'webp'];
        
        foreach ($types as $type) {
            $file = UploadedFile::fake()->image("test.{$type}");
            $path = StorageService::uploadFile($file, 'test-uploads', 'public');
            
            $this->assertNotFalse($path, "Failed to upload {$type} file");
        }
    }

    /** @test */
    public function it_ensures_directory_exists()
    {
        $result = StorageService::ensureDirectoryExists('new-test-directory', 'public');
        
        $this->assertTrue($result);
        $this->assertTrue(Storage::disk('public')->exists('new-test-directory'));
    }

    /** @test */
    public function it_formats_bytes_correctly()
    {
        $this->assertEquals('1 KB', StorageService::formatBytes(1024));
        $this->assertEquals('1 MB', StorageService::formatBytes(1024 * 1024));
        $this->assertEquals('1 GB', StorageService::formatBytes(1024 * 1024 * 1024));
    }
}
