@extends('layouts.admin')

@section('title', 'Available Drivers')

@section('styles')
<style>
    .driver-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .driver-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 20px;
    }

    .driver-card .card-header h6 {
        margin: 0;
        font-weight: 600;
        color: #4e73df;
    }

    .driver-card .card-body {
        padding: 25px;
    }

    .booking-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .badge-pending {
        background-color: #fff3cd;
        color: #664d03;
    }

    .badge-confirmed {
        background-color: #cff4fc;
        color: #055160;
    }

    .badge-completed {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .badge-cancelled {
        background-color: #f8d7da;
        color: #842029;
    }

    .badge-warning {
        background-color: #fff3cd;
        color: #664d03;
    }

    .badge-success {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .driver-profile-img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .driver-profile-placeholder {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: #fff;
        background-color: #4e73df;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .driver-table {
        margin-bottom: 0;
    }

    .driver-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 15px;
        border-top: none;
    }

    .driver-table td {
        padding: 15px;
        vertical-align: middle;
    }

    .driver-table tr {
        transition: all 0.3s ease;
    }

    .driver-table tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    .driver-table tr.conflict-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .driver-table tr.conflict-warning:hover {
        background-color: rgba(255, 193, 7, 0.15);
    }

    .btn-group .btn {
        border-radius: 5px;
        margin-right: 3px;
        padding: 5px 10px;
    }

    .booking-info-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .booking-info-value {
        font-weight: 600;
        margin-bottom: 15px;
    }

    .current-driver-container {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .current-driver-info {
        margin-left: 15px;
    }

    .current-driver-name {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 3px;
    }

    .current-driver-contact {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Available Drivers for Booking #{{ $booking->booking_number }}</h1>
        <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Booking
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="driver-card card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i> Booking Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="booking-info-label">Client</div>
                            <div class="booking-info-value">{{ $booking->user->name }}</div>

                            <div class="booking-info-label">Pickup Date & Time</div>
                            <div class="booking-info-value">{{ $booking->pickup_date->format('M d, Y h:i A') }}</div>

                            <div class="booking-info-label">Booking Type</div>
                            <div class="booking-info-value">{{ ucfirst(str_replace('_', ' ', $booking->booking_type)) }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="booking-info-label">Vehicle</div>
                            <div class="booking-info-value">{{ $booking->vehicle->name }}</div>

                            <div class="booking-info-label">Status</div>
                            <div class="booking-info-value">
                                <span class="booking-badge badge-{{ strtolower($booking->status) }}">
                                    {{ ucfirst($booking->status) }}
                                </span>
                            </div>

                            <div class="booking-info-label">Amount</div>
                            <div class="booking-info-value">${{ number_format($booking->amount, 2) }}</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="booking-info-label">Pickup Address</div>
                            <div class="booking-info-value">{{ $booking->pickup_address }}</div>

                            @if($booking->via_stops && count($booking->via_stops) > 0)
                                @foreach($booking->via_stops as $index => $viaStop)
                                    <div class="booking-info-label">Via Stop {{ $index + 1 }}</div>
                                    <div class="booking-info-value">{{ $viaStop['address'] ?? 'Address not specified' }}</div>
                                @endforeach
                            @endif

                            <div class="booking-info-label">Dropoff Address</div>
                            <div class="booking-info-value">{{ $booking->dropoff_address }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="driver-card card">
                <div class="card-header">
                    <h6><i class="fas fa-user-check me-2"></i> Current Assignment</h6>
                </div>
                <div class="card-body">
                    @if($booking->driver)
                        <div class="current-driver-container">
                            @if($booking->driver->profile_photo)
                                <img src="{{ asset('storage/' . $booking->driver->profile_photo) }}" alt="{{ $booking->driver->name }}" class="driver-profile-img">
                            @else
                                <div class="driver-profile-placeholder">
                                    {{ strtoupper(substr($booking->driver->name, 0, 1)) }}
                                </div>
                            @endif
                            <div class="current-driver-info">
                                <div class="current-driver-name">{{ $booking->driver->name }}</div>
                                <div class="current-driver-contact">
                                    <i class="fas fa-phone me-1"></i> {{ $booking->driver->phone }}<br>
                                    <i class="fas fa-envelope me-1"></i> {{ $booking->driver->email }}
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> This booking is currently assigned to the driver above. Assigning a new driver will replace the current assignment.
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                            <h5 class="mb-2">No Driver Assigned</h5>
                            <p class="text-muted">Select a driver from the list below to assign to this booking.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="driver-card card mb-4">
        <div class="card-header">
            <h6><i class="fas fa-users me-2"></i> Available Drivers</h6>
        </div>
        <div class="card-body">
            @if($availableDrivers->isEmpty())
                <div class="text-center py-5">
                    <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                    <h5 class="mb-2">No Available Drivers</h5>
                    <p class="text-muted">Try activating more drivers or changing their availability status.</p>
                </div>
            @else
                <div class="table-responsive">
                    <table class="table driver-table" id="driversTable">
                        <thead>
                            <tr>
                                <th>Driver</th>
                                <th>Contact</th>
                                <th>License</th>
                                <th>Vehicle</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($availableDrivers as $driver)
                                <tr class="{{ in_array($driver->id, $busyDriverIds) ? 'conflict-warning' : '' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($driver->profile_photo)
                                                <img src="{{ asset('storage/' . $driver->profile_photo) }}" alt="{{ $driver->name }}" class="driver-profile-img" style="width: 40px; height: 40px;">
                                            @else
                                                <div class="driver-profile-placeholder" style="width: 40px; height: 40px; font-size: 1rem;">
                                                    {{ strtoupper(substr($driver->name, 0, 1)) }}
                                                </div>
                                            @endif
                                            <div class="ms-2">
                                                <div class="fw-bold">{{ $driver->name }}</div>
                                                <small class="text-muted">ID: {{ $driver->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><i class="fas fa-phone me-1 text-primary"></i> {{ $driver->phone }}</div>
                                        <div><i class="fas fa-envelope me-1 text-primary"></i> {{ $driver->email }}</div>
                                    </td>
                                    <td>
                                        <div><i class="fas fa-id-card me-1 text-primary"></i> {{ $driver->license_number }}</div>
                                    </td>
                                    <td>
                                        <div><i class="fas fa-car me-1 text-primary"></i> {{ $driver->vehicle_make }} {{ $driver->vehicle_model }}</div>
                                        <div><i class="fas fa-palette me-1 text-primary"></i> {{ $driver->vehicle_color }}</div>
                                        <div><i class="fas fa-hashtag me-1 text-primary"></i> {{ $driver->vehicle_reg_number }}</div>
                                    </td>
                                    <td>
                                        @if(in_array($driver->id, $busyDriverIds))
                                            <span class="booking-badge badge-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i> Potential Schedule Conflict
                                            </span>
                                        @else
                                            <span class="booking-badge badge-success">
                                                <i class="fas fa-check-circle me-1"></i> Available
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.drivers.show', $driver->id) }}" class="btn btn-sm btn-outline-info" target="_blank" title="View Driver Profile">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('admin.bookings.assign-driver', $booking->id) }}" method="POST" class="d-inline assign-driver-form">
                                                @csrf
                                                <input type="hidden" name="driver_id" value="{{ $driver->id }}">
                                                <button type="submit" class="btn btn-sm btn-outline-primary" title="Assign Driver"
                                                    data-has-conflict="{{ in_array($driver->id, $busyDriverIds) ? 'true' : 'false' }}">
                                                    <i class="fas fa-user-check me-1"></i> Assign
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#driversTable').DataTable({
            "order": [],
            "pageLength": 10,
            "language": {
                "emptyTable": "No available drivers found"
            },
            "columnDefs": [
                { "orderable": false, "targets": [0, 5] }
            ],
            "responsive": true
        });

        // Handle driver assignment with confirmation
        $('.assign-driver-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const hasConflict = form.find('button').data('has-conflict');

            if (hasConflict === true) {
                Swal.fire({
                    title: 'Schedule Conflict Detected',
                    text: "This driver may have scheduling conflicts with other bookings. Are you sure you want to assign them?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, assign anyway',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.off('submit').submit();
                    }
                });
            } else {
                Swal.fire({
                    title: 'Assign Driver',
                    text: "Are you sure you want to assign this driver to the booking?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, assign driver',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.off('submit').submit();
                    }
                });
            }
        });
    });
</script>
@endsection
