<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Highway Preference Routing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        #map { height: 400px; width: 100%; margin: 20px 0; }
        .route-info { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 8px; margin: 5px; width: 300px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Highway Preference Routing Test</h1>
        
        <div class="test-section">
            <h3>Route Configuration Test</h3>
            <p>This page tests the highway preference routing functionality implemented for YNR Cars.</p>
            
            <div>
                <label>Origin:</label>
                <input type="text" id="origin" value="New York, NY" placeholder="Enter origin address">
            </div>
            
            <div>
                <label>Destination:</label>
                <input type="text" id="destination" value="Philadelphia, PA" placeholder="Enter destination address">
            </div>
            
            <button onclick="testRouting()">Test Highway Preference Routing</button>
            <button onclick="testStandardRouting()">Test Standard Routing</button>
        </div>
        
        <div class="test-section">
            <h3>Route Information</h3>
            <div id="route-info" class="route-info">
                Click "Test Highway Preference Routing" to see route details with highway preferences.
            </div>
        </div>
        
        <div class="test-section">
            <h3>Map Display</h3>
            <div id="map"></div>
        </div>
    </div>

    <script>
        let map;
        let directionsService;
        let directionsRenderer;

        function initMap() {
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 7,
                center: { lat: 40.7128, lng: -74.0060 } // New York
            });

            directionsService = new google.maps.DirectionsService();
            directionsRenderer = new google.maps.DirectionsRenderer();
            directionsRenderer.setMap(map);
        }

        function testRouting() {
            const origin = document.getElementById('origin').value;
            const destination = document.getElementById('destination').value;
            
            if (!origin || !destination) {
                alert('Please enter both origin and destination');
                return;
            }

            // Test with highway preference settings (as implemented in the application)
            const request = {
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING,
                avoidHighways: false,      // Prefer highways for long distance
                avoidTolls: false,         // Allow tolls for faster routes
                avoidFerries: true,        // Avoid ferries for reliability
                optimizeWaypoints: true,   // Optimize route for efficiency
                provideRouteAlternatives: false // Single best route
            };

            directionsService.route(request, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                    
                    const route = response.routes[0];
                    const leg = route.legs[0];
                    
                    document.getElementById('route-info').innerHTML = `
                        <h4>Highway Preference Route Results:</h4>
                        <p><strong>Distance:</strong> ${leg.distance.text}</p>
                        <p><strong>Duration:</strong> ${leg.duration.text}</p>
                        <p><strong>Route Settings:</strong></p>
                        <ul>
                            <li>Avoid Highways: ${request.avoidHighways}</li>
                            <li>Avoid Tolls: ${request.avoidTolls}</li>
                            <li>Avoid Ferries: ${request.avoidFerries}</li>
                            <li>Optimize Waypoints: ${request.optimizeWaypoints}</li>
                        </ul>
                        <p><strong>Route Summary:</strong> ${route.summary}</p>
                    `;
                } else {
                    document.getElementById('route-info').innerHTML = `
                        <p style="color: red;">Error: ${status}</p>
                    `;
                }
            });
        }

        function testStandardRouting() {
            const origin = document.getElementById('origin').value;
            const destination = document.getElementById('destination').value;
            
            if (!origin || !destination) {
                alert('Please enter both origin and destination');
                return;
            }

            // Test with standard routing (no preferences)
            const request = {
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING
            };

            directionsService.route(request, function(response, status) {
                if (status === 'OK') {
                    directionsRenderer.setDirections(response);
                    
                    const route = response.routes[0];
                    const leg = route.legs[0];
                    
                    document.getElementById('route-info').innerHTML = `
                        <h4>Standard Route Results:</h4>
                        <p><strong>Distance:</strong> ${leg.distance.text}</p>
                        <p><strong>Duration:</strong> ${leg.duration.text}</p>
                        <p><strong>Route Settings:</strong> Default (no preferences)</p>
                        <p><strong>Route Summary:</strong> ${route.summary}</p>
                    `;
                } else {
                    document.getElementById('route-info').innerHTML = `
                        <p style="color: red;">Error: ${status}</p>
                    `;
                }
            });
        }
    </script>

    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initMap">
    </script>
</body>
</html>
