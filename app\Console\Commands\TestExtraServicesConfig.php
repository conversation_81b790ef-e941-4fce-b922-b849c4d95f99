<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SettingsService;
use App\Models\Setting;

class TestExtraServicesConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:extra-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Extra Services Configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Extra Services Configuration...');
        $this->newLine();

        // Check if extra services settings exist in database
        $extraServicesKeys = [
            'meet_and_greet_fee',
            'child_seat_fee', 
            'wheelchair_fee',
            'extra_luggage_fee',
            'meet_and_greet_enabled',
            'child_seat_enabled',
            'wheelchair_enabled',
            'extra_luggage_enabled'
        ];

        $this->info('Checking database settings:');
        foreach ($extraServicesKeys as $key) {
            $setting = Setting::where('key', $key)->first();
            if ($setting) {
                $this->line("✅ {$key}: {$setting->value}");
            } else {
                $this->error("❌ {$key}: Not found in database");
            }
        }

        $this->newLine();

        // Test SettingsService methods
        $this->info('Testing SettingsService methods:');
        
        try {
            $extraServicesSettings = SettingsService::getExtraServicesSettings();
            $this->line('✅ getExtraServicesSettings() works');
            
            foreach ($extraServicesSettings as $service => $config) {
                $this->line("  - {$service}:");
                $this->line("    Enabled: " . ($config['enabled'] ? 'Yes' : 'No'));
                $this->line("    Fee: {$config['fee']}");
                $this->line("    Label: {$config['label']}");
            }
        } catch (\Exception $e) {
            $this->error('❌ getExtraServicesSettings() failed: ' . $e->getMessage());
        }

        $this->newLine();

        // Test individual service checks
        $this->info('Testing individual service checks:');
        $services = ['meet_and_greet', 'child_seat', 'wheelchair_accessible', 'extra_luggage'];
        
        foreach ($services as $service) {
            try {
                $enabled = SettingsService::isExtraServiceEnabled($service);
                $this->line("✅ {$service}: " . ($enabled ? 'Enabled' : 'Disabled'));
            } catch (\Exception $e) {
                $this->error("❌ {$service}: Error - " . $e->getMessage());
            }
        }

        $this->newLine();

        // Check validation rules
        $this->info('Checking validation rules in SettingsController:');
        $controller = new \App\Http\Controllers\Admin\SettingsController();
        
        // Use reflection to access the validation rules
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('update');
        
        $this->line('✅ SettingsController exists and has update method');

        $this->newLine();
        $this->info('Extra Services Configuration test completed.');

        return 0;
    }
}
