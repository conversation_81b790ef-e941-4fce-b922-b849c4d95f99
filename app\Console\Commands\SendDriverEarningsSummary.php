<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Booking;
use App\Services\EmailService;
use Carbon\Carbon;

class SendDriverEarningsSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drivers:earnings-summary {--period=weekly : Period for earnings summary (weekly, monthly)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send earnings summary emails to drivers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $period = $this->option('period');
        $this->info("Sending {$period} earnings summary to drivers...");

        $drivers = User::where('role', 'driver')
            ->where('is_active', true)
            ->get();

        $summariesSent = 0;

        foreach ($drivers as $driver) {
            $earningsData = $this->calculateEarnings($driver, $period);

            if ($earningsData['total_rides'] > 0) {
                try {
                    EmailService::sendDriverEarningsSummary($driver, $earningsData, $period);
                    $summariesSent++;

                    $this->line("✅ Earnings summary sent to {$driver->name} - {$earningsData['total_rides']} rides, £{$earningsData['net_earnings']}");
                } catch (\Exception $e) {
                    $this->error("❌ Failed to send summary to {$driver->name}: " . $e->getMessage());
                }
            } else {
                $this->line("⏭️ Skipping {$driver->name} - no rides in this period");
            }
        }

        $this->info("📧 Earnings summaries sent to {$summariesSent} drivers");
        return Command::SUCCESS;
    }

    /**
     * Calculate earnings for a driver
     */
    private function calculateEarnings($driver, $period)
    {
        $startDate = $period === 'monthly'
            ? Carbon::now()->startOfMonth()
            : Carbon::now()->startOfWeek();

        $endDate = $period === 'monthly'
            ? Carbon::now()->endOfMonth()
            : Carbon::now()->endOfWeek();

        $bookings = Booking::where('driver_id', $driver->id)
            ->where('status', 'completed')
            ->whereBetween('pickup_date', [$startDate, $endDate])
            ->get();

        $totalRides = $bookings->count();
        $grossEarnings = $bookings->sum('amount');
        $totalDistance = $bookings->sum('distance') ?? 0;
        $totalHours = $bookings->sum('duration_hours') ?? 0;

        // Calculate commission (default 20%)
        $commissionRate = 20;
        $commission = $grossEarnings * ($commissionRate / 100);
        $netEarnings = $grossEarnings - $commission;

        // Calculate averages
        $averageRating = $bookings->avg('driver_rating') ?? 0;
        $onTimeCount = $bookings->where('driver_on_time', true)->count();
        $onTimePercentage = $totalRides > 0 ? ($onTimeCount / $totalRides) * 100 : 0;

        // Daily breakdown
        $dailyBreakdown = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dayBookings = $bookings->filter(function($booking) use ($currentDate) {
                return Carbon::parse($booking->pickup_date)->isSameDay($currentDate);
            });

            $dayName = $currentDate->format('l');
            $dailyBreakdown[$dayName] = [
                'rides' => $dayBookings->count(),
                'earnings' => $dayBookings->sum('amount'),
            ];

            $currentDate->addDay();
        }

        return [
            'total_rides' => $totalRides,
            'gross_earnings' => $grossEarnings,
            'commission_rate' => $commissionRate,
            'commission' => $commission,
            'net_earnings' => $netEarnings,
            'base_fare' => $grossEarnings * 0.6, // Estimated
            'tips' => $grossEarnings * 0.1, // Estimated
            'bonuses' => $grossEarnings * 0.05, // Estimated
            'total_distance' => $totalDistance,
            'total_hours' => $totalHours,
            'average_rating' => round($averageRating, 1),
            'on_time_percentage' => round($onTimePercentage, 1),
            'daily_breakdown' => $dailyBreakdown,
        ];
    }
}
