@extends('layouts.admin')

@section('title', 'Edit Booking')

@section('content')
<div class="container mt-4">
    <h2>Edit Booking #{{ $booking->booking_number }}</h2>
    <form action="{{ route('admin.bookings.update', $booking->id) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="mb-3">
            <label for="pickup_address" class="form-label">Pickup Address</label>
            <input type="text" class="form-control" id="pickup_address" name="pickup_address" value="{{ old('pickup_address', $booking->pickup_address) }}" required>
        </div>
        <div class="mb-3">
            <label for="pickup_date" class="form-label">Pickup Date & Time</label>
            <input type="datetime-local" class="form-control" id="pickup_date" name="pickup_date" value="{{ old('pickup_date', $booking->pickup_date ? $booking->pickup_date->format('Y-m-d\TH:i') : '' ) }}" required>
        </div>
        <div class="mb-3">
            <label for="dropoff_address" class="form-label">Dropoff Address</label>
            <input type="text" class="form-control" id="dropoff_address" name="dropoff_address" value="{{ old('dropoff_address', $booking->dropoff_address) }}">
        </div>
        <div class="mb-3">
            <label for="return_date" class="form-label">Return Date & Time</label>
            <input type="datetime-local" class="form-control" id="return_date" name="return_date" value="{{ old('return_date', $booking->return_date ? $booking->return_date->format('Y-m-d\TH:i') : '' ) }}">
        </div>
        <div class="mb-3">
            <label for="duration_hours" class="form-label">Duration (Hours)</label>
            <input type="number" class="form-control" id="duration_hours" name="duration_hours" value="{{ old('duration_hours', $booking->duration_hours) }}">
        </div>
        <div class="mb-3">
            <label for="vehicle_id" class="form-label">Vehicle</label>
            <select class="form-select" id="vehicle_id" name="vehicle_id" required>
                @foreach($vehicles as $vehicle)
                    <option value="{{ $vehicle->id }}" {{ $booking->vehicle_id == $vehicle->id ? 'selected' : '' }}>{{ $vehicle->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label for="booking_type" class="form-label">Booking Type</label>
            <select class="form-select" id="booking_type" name="booking_type" required>
                <option value="one_way" {{ $booking->booking_type == 'one_way' ? 'selected' : '' }}>One Way</option>
                <option value="return" {{ $booking->booking_type == 'return' ? 'selected' : '' }}>Return</option>
                <option value="hourly" {{ $booking->booking_type == 'hourly' ? 'selected' : '' }}>Hourly</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="amount" class="form-label">Amount</label>
            <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="{{ old('amount', $booking->amount) }}" required>
        </div>
        <div class="mb-3">
            <label for="notes" class="form-label">Notes</label>
            <textarea class="form-control" id="notes" name="notes">{{ old('notes', $booking->notes) }}</textarea>
        </div>

        <!-- Flight Information Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plane me-2"></i>Flight Information
                </h5>
                <small class="text-muted">Optional - helps track flight status and adjust pickup times</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="flight_number" class="form-label">
                            <i class="fas fa-ticket-alt me-1"></i>Flight Number
                        </label>
                        <input type="text" class="form-control" id="flight_number" name="flight_number"
                               value="{{ old('flight_number', $booking->flight_number) }}"
                               placeholder="e.g., BA123, EK456" maxlength="20">
                        <small class="text-muted">Enter flight number for real-time tracking</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="airline" class="form-label">
                            <i class="fas fa-building me-1"></i>Airline
                        </label>
                        <input type="text" class="form-control" id="airline" name="airline"
                               value="{{ old('airline', $booking->airline) }}"
                               placeholder="e.g., British Airways, Emirates" maxlength="100">
                        <small class="text-muted">Airline name or code</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="departure_time" class="form-label">
                            <i class="fas fa-clock me-1"></i>Departure Time
                        </label>
                        <input type="datetime-local" class="form-control" id="departure_time" name="departure_time"
                               value="{{ old('departure_time', $booking->departure_time ? $booking->departure_time->format('Y-m-d\TH:i') : '') }}">
                        <small class="text-muted">Scheduled departure time</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="arrival_time" class="form-label">
                            <i class="fas fa-clock me-1"></i>Arrival Time
                        </label>
                        <input type="datetime-local" class="form-control" id="arrival_time" name="arrival_time"
                               value="{{ old('arrival_time', $booking->arrival_time ? $booking->arrival_time->format('Y-m-d\TH:i') : '') }}">
                        <small class="text-muted">Scheduled arrival time</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="terminal" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>Terminal
                        </label>
                        <input type="text" class="form-control" id="terminal" name="terminal"
                               value="{{ old('terminal', $booking->terminal) }}"
                               placeholder="e.g., Terminal 1, T2" maxlength="50">
                        <small class="text-muted">Departure/arrival terminal</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="flight_status" class="form-label">
                            <i class="fas fa-info-circle me-1"></i>Flight Status
                        </label>
                        <select class="form-select" id="flight_status" name="flight_status">
                            <option value="">Select Status</option>
                            <option value="scheduled" {{ old('flight_status', $booking->flight_status) == 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                            <option value="delayed" {{ old('flight_status', $booking->flight_status) == 'delayed' ? 'selected' : '' }}>Delayed</option>
                            <option value="boarding" {{ old('flight_status', $booking->flight_status) == 'boarding' ? 'selected' : '' }}>Boarding</option>
                            <option value="departed" {{ old('flight_status', $booking->flight_status) == 'departed' ? 'selected' : '' }}>Departed</option>
                            <option value="arrived" {{ old('flight_status', $booking->flight_status) == 'arrived' ? 'selected' : '' }}>Arrived</option>
                            <option value="cancelled" {{ old('flight_status', $booking->flight_status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        <small class="text-muted">Current flight status (if known)</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 mb-3">
                        <label for="flight_notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>Flight Notes
                        </label>
                        <textarea class="form-control" id="flight_notes" name="flight_notes" rows="2"
                                  placeholder="Any additional flight information or special requirements..." maxlength="500">{{ old('flight_notes', $booking->flight_notes) }}</textarea>
                        <small class="text-muted">Additional flight-related information</small>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Flight Tracking:</strong> Providing flight details allows automatic monitoring of flight status
                    and adjustment of pickup times for delays. Drivers will be notified of any changes.
                </div>
            </div>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-success">Update Booking</button>
        <a href="{{ route('admin.bookings.show', $booking->id) }}" class="btn btn-secondary ms-2">Cancel</a>
    </form>
</div>
@endsection
