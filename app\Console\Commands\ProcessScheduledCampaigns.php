<?php

namespace App\Console\Commands;

use App\Models\EmailCampaign;
use App\Services\EmailQueueService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessScheduledCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:process-scheduled {--dry-run : Show what would be processed without actually processing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process scheduled email campaigns that are due to be sent';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing scheduled email campaigns...');

        // Get campaigns that are scheduled and due to be sent
        $campaigns = EmailCampaign::where('status', 'scheduled')
            ->where('scheduled_at', '<=', now())
            ->get();

        if ($campaigns->isEmpty()) {
            $this->info('No scheduled campaigns found.');
            return;
        }

        $this->info("Found {$campaigns->count()} scheduled campaign(s) ready to be sent.");

        foreach ($campaigns as $campaign) {
            $this->processCampaign($campaign);
        }

        $this->info('Scheduled campaigns processing completed.');
    }

    /**
     * Process a single campaign
     */
    private function processCampaign(EmailCampaign $campaign): void
    {
        $this->line("Processing campaign: {$campaign->name} (ID: {$campaign->id})");

        if ($this->option('dry-run')) {
            $this->comment("  [DRY RUN] Would queue campaign for sending");
            $this->comment("  - Scheduled for: {$campaign->scheduled_at}");
            $this->comment("  - Recipient type: {$campaign->recipient_type}");
            
            // Get target recipients count
            $targetRecipients = $campaign->getTargetRecipients();
            $this->comment("  - Target recipients: {$targetRecipients->count()}");
            
            return;
        }

        try {
            // Queue the campaign for sending
            $success = EmailQueueService::queueCampaign($campaign);

            if ($success) {
                $this->info("  ✓ Campaign queued successfully");
                
                Log::info('Scheduled campaign queued', [
                    'campaign_id' => $campaign->id,
                    'campaign_name' => $campaign->name,
                    'scheduled_at' => $campaign->scheduled_at,
                ]);
            } else {
                $this->error("  ✗ Failed to queue campaign");
                
                Log::error('Failed to queue scheduled campaign', [
                    'campaign_id' => $campaign->id,
                    'campaign_name' => $campaign->name,
                ]);
            }
        } catch (\Exception $e) {
            $this->error("  ✗ Error processing campaign: {$e->getMessage()}");
            
            Log::error('Error processing scheduled campaign', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
                'error' => $e->getMessage(),
            ]);

            // Mark campaign as failed
            $campaign->update(['status' => 'failed']);
        }
    }
}
