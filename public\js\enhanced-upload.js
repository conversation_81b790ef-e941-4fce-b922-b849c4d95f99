/**
 * Enhanced Upload System - YNR Cars
 * Provides real-time upload feedback, progress indicators, and improved UX
 */

class EnhancedUpload {
    constructor() {
        this.init();
    }

    init() {
        this.setupFileInputs();
        this.setupDragAndDrop();
        this.setupFormSubmission();
    }

    setupFileInputs() {
        // Enhance all file inputs
        document.querySelectorAll('input[type="file"]').forEach(input => {
            this.enhanceFileInput(input);
        });
    }

    enhanceFileInput(input) {
        // Add change event listener
        input.addEventListener('change', (e) => {
            this.handleFileSelection(e.target);
        });

        // Add visual feedback
        this.addUploadFeedback(input);
    }

    handleFileSelection(input) {
        const files = input.files;
        if (!files || files.length === 0) return;

        const file = files[0];
        
        // Validate file
        if (!this.validateFile(file, input)) {
            return;
        }

        // Show preview
        this.showFilePreview(file, input);
        
        // Show upload status
        this.showUploadStatus(input, 'ready');
    }

    validateFile(file, input) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        
        // Check file size
        if (file.size > maxSize) {
            this.showError(input, 'File size must be less than 10MB');
            return false;
        }

        // Check file type for image inputs
        if (input.accept && input.accept.includes('image/*')) {
            if (!allowedTypes.includes(file.type)) {
                this.showError(input, 'Please select a valid image file (JPEG, PNG, GIF, WebP)');
                return false;
            }
        }

        return true;
    }

    showFilePreview(file, input) {
        if (!file.type.startsWith('image/')) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            // Find or create preview element
            let preview = this.findPreviewElement(input);
            if (!preview) {
                preview = this.createPreviewElement(input);
            }

            preview.src = e.target.result;
            preview.style.display = 'block';
            preview.classList.add('upload-preview-active');

            // Add remove button
            this.addRemoveButton(preview, input);
        };
        reader.readAsDataURL(file);
    }

    findPreviewElement(input) {
        // Look for existing preview elements
        const previewId = input.id + '-preview';
        let preview = document.getElementById(previewId);
        
        if (!preview) {
            preview = input.parentElement.querySelector('.preview-image, #preview, img[id*="preview"]');
        }

        return preview;
    }

    createPreviewElement(input) {
        const preview = document.createElement('img');
        preview.id = input.id + '-preview';
        preview.className = 'preview-image upload-preview';
        preview.style.cssText = `
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
            border: 2px solid #ddd;
            display: none;
        `;

        // Insert after the input
        input.parentNode.insertBefore(preview, input.nextSibling);
        return preview;
    }

    addRemoveButton(preview, input) {
        // Remove existing button
        const existingBtn = preview.parentElement.querySelector('.remove-preview-btn');
        if (existingBtn) existingBtn.remove();

        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-sm btn-danger remove-preview-btn';
        removeBtn.innerHTML = '<i class="fas fa-times"></i> Remove';
        removeBtn.style.cssText = 'margin-top: 5px; margin-left: 5px;';
        
        removeBtn.onclick = () => {
            preview.style.display = 'none';
            preview.classList.remove('upload-preview-active');
            input.value = '';
            removeBtn.remove();
            this.showUploadStatus(input, 'empty');
        };

        preview.parentNode.insertBefore(removeBtn, preview.nextSibling);
    }

    addUploadFeedback(input) {
        // Create status container
        const statusContainer = document.createElement('div');
        statusContainer.className = 'upload-status-container';
        statusContainer.id = input.id + '-status';
        statusContainer.style.cssText = 'margin-top: 8px; font-size: 0.875rem;';

        input.parentNode.insertBefore(statusContainer, input.nextSibling);
    }

    showUploadStatus(input, status, message = '') {
        const statusContainer = document.getElementById(input.id + '-status');
        if (!statusContainer) return;

        const statusConfig = {
            empty: { icon: '', text: '', class: '' },
            ready: { icon: 'fas fa-check-circle', text: 'File selected - ready to upload', class: 'text-success' },
            uploading: { icon: 'fas fa-spinner fa-spin', text: 'Uploading...', class: 'text-primary' },
            success: { icon: 'fas fa-check-circle', text: 'Upload successful!', class: 'text-success' },
            error: { icon: 'fas fa-exclamation-circle', text: message || 'Upload failed', class: 'text-danger' }
        };

        const config = statusConfig[status] || statusConfig.empty;
        
        statusContainer.innerHTML = config.icon ? 
            `<i class="${config.icon}"></i> <span class="${config.class}">${config.text}</span>` : '';
    }

    showError(input, message) {
        this.showUploadStatus(input, 'error', message);
        
        // Clear the input
        input.value = '';
        
        // Hide any preview
        const preview = this.findPreviewElement(input);
        if (preview) {
            preview.style.display = 'none';
        }
    }

    setupDragAndDrop() {
        // Enhance drag and drop areas
        document.querySelectorAll('.image-upload-area, .upload-area').forEach(area => {
            this.enhanceDragAndDrop(area);
        });
    }

    enhanceDragAndDrop(area) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            area.addEventListener(eventName, () => this.highlight(area), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            area.addEventListener(eventName, () => this.unhighlight(area), false);
        });

        area.addEventListener('drop', (e) => this.handleDrop(e, area), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(area) {
        area.classList.add('dragover', 'border-primary');
        area.style.backgroundColor = '#f8f9ff';
    }

    unhighlight(area) {
        area.classList.remove('dragover', 'border-primary');
        area.style.backgroundColor = '';
    }

    handleDrop(e, area) {
        const files = e.dataTransfer.files;
        const fileInput = area.querySelector('input[type="file"]') || 
                         document.querySelector(`input[type="file"][data-target="${area.id}"]`);

        if (fileInput && files.length > 0) {
            fileInput.files = files;
            this.handleFileSelection(fileInput);
        }
    }

    setupFormSubmission() {
        // Enhance forms with file uploads
        document.querySelectorAll('form[enctype="multipart/form-data"]').forEach(form => {
            this.enhanceForm(form);
        });
    }

    enhanceForm(form) {
        form.addEventListener('submit', (e) => {
            this.handleFormSubmission(e, form);
        });
    }

    handleFormSubmission(e, form) {
        const fileInputs = form.querySelectorAll('input[type="file"]');
        let hasFiles = false;

        fileInputs.forEach(input => {
            if (input.files && input.files.length > 0) {
                hasFiles = true;
                this.showUploadStatus(input, 'uploading');
            }
        });

        if (hasFiles) {
            // Show loading state on submit button
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
            }
        }
    }

    // Utility method to refresh image after upload
    static refreshImage(imageElement) {
        if (imageElement && imageElement.src) {
            const url = new URL(imageElement.src);
            url.searchParams.set('v', Date.now());
            imageElement.src = url.toString();
        }
    }

    // Method to show success message after upload
    static showUploadSuccess(message = 'File uploaded successfully!') {
        // Create or update success message
        let successAlert = document.querySelector('.upload-success-alert');
        if (!successAlert) {
            successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success upload-success-alert';
            successAlert.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999;';
            document.body.appendChild(successAlert);
        }

        successAlert.innerHTML = `
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (successAlert.parentElement) {
                successAlert.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.enhancedUpload = new EnhancedUpload();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedUpload;
}
