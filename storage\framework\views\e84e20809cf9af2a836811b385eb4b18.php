<?php $__env->startSection('title', 'Book a Ride | YNR CARS'); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/booking.css')); ?>">
<style>
    /* Main booking container */
    .booking-container {
        background-color: #f8f9fa;
        padding: 60px 0;
    }

    /* Booking card */
    .booking-card {
        border-radius: 12px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        border: none;
    }

    .booking-card .card-header {
        background-color: var(--bs-primary);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .booking-card .card-body {
        padding: 30px;
    }

    /* Via Stops Styling */
    .via-stop-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        position: relative;
    }

    .via-stop-item:hover {
        background: #e3f2fd;
        border-color: #dc3545;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    }

    .via-stop-item .input-group-text {
        background: #dc3545;
        color: white;
        border: none;
        font-weight: 500;
    }

    .via-stop-item .form-control {
        border-left: none;
        padding-left: 15px;
    }

    .via-stop-item .form-control:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    }

    .via-stop-item .btn-outline-danger {
        border-color: #dc3545;
        color: #dc3545;
        transition: all 0.3s ease;
    }

    .via-stop-item .btn-outline-danger:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
        transform: scale(1.05);
    }

    #addViaStopBtn {
        background: linear-gradient(45deg, #dc3545, #dc3545);
        border: none;
        color: white;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    #addViaStopBtn:hover {
        background: linear-gradient(45deg, #dc3545, #dc3545);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
    }

    #addViaStopBtn:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    #viaStopsContainer:empty::before {
        content: "No via stops added yet. Click 'Add Stop' to add intermediate destinations.";
        color: #6c757d;
        font-style: italic;
        display: block;
        text-align: center;
        padding: 20px;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        background: #fafafa;
    }

    .via-stop-number {
        position: absolute;
        top: -8px;
        left: 15px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        z-index: 10;
    }

    /* Via Stops Summary Styling */
    #via-stops-summary .summary-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    #via-stops-summary .summary-item:last-child {
        border-bottom: none;
    }

    .via-stop-address {
        color: #495057;
        font-weight: 500;
    }

    /* Animation for adding/removing via stops */
    .via-stop-item {
        animation: slideInUp 0.3s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .via-stop-removing {
        animation: slideOutDown 0.3s ease-in forwards;
    }

    @keyframes slideOutDown {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-20px);
        }
    }

    /* Progress steps */
    .booking-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        position: relative;
    }

    .booking-steps::before {
        content: '';
        position: absolute;
        top: 24px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 1;
    }

    .step {
        position: relative;
        z-index: 2;
        text-align: center;
        width: 33.333%;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin: 0 auto 10px;
        transition: all 0.3s ease;
    }

    .step.active .step-number {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
        color: #fff;
    }

    .step.completed .step-number {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
    }

    .step-title {
        font-weight: 500;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .step.active .step-title {
        color: var(--bs-primary);
        font-weight: 600;
    }

    .step.completed .step-title {
        color: #28a745;
    }

    /* Step content */
    .step-content {
        display: none;
    }

    .step-content.active {
        display: block;
    }

    /* Navigation buttons */
    .step-buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        gap: 15px;
    }

    /* Vehicle selection */
    .vehicle-card {
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .vehicle-card:hover {
        border-color: var(--bs-primary);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .vehicle-card.selected {
        border-color: var(--bs-primary);
        background-color: rgba(238, 57, 61, 0.05);
    }

    /* Summary section */
    .booking-summary {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }

    .summary-section {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .summary-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .summary-title {
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--bs-primary);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .summary-item-label {
        font-weight: 500;
        color: #6c757d;
    }

    .summary-item-value {
        font-weight: 500;
    }

    /* Autocomplete styling */
    .address-autocomplete {
        position: relative;
        transition: all 0.3s ease;
    }

    .address-autocomplete.autocomplete-enhanced {
        border-left: 3px solid #28a745 !important;
    }

    .address-autocomplete.autocomplete-disabled {
        border-left: 3px solid #ffc107 !important;
        background-color: #fff9e6;
    }

    .address-autocomplete:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        border-color: #28a745;
    }

    /* Google Maps autocomplete dropdown styling */
    .pac-container {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid #dee2e6;
        margin-top: 2px;
        z-index: 9999;
    }

    .pac-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .pac-item:hover {
        background-color: #f8f9fa;
    }

    .pac-item-selected {
        background-color: #e3f2fd;
    }

    .pac-matched {
        font-weight: 600;
        color: #ee393d;
    }

    /* Loading indicator for autocomplete */
    .address-autocomplete.loading {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23007bff' viewBox='0 0 16 16'%3E%3Cpath d='M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM7 3a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0V3zm0 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 16px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .step-buttons {
            flex-direction: column;
        }

        .step-buttons button {
            width: 100%;
            margin-bottom: 10px;
        }

        .summary-item {
            flex-direction: column;
        }

        .summary-item-value {
            margin-top: 5px;
        }

        .pac-container {
            width: 100% !important;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="booking-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card booking-card" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="mb-0">Book Your Transportation</h3>
                    </div>
                    <div class="card-body">
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger mb-4">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <!-- Booking Steps Indicator -->
                        <div class="booking-steps">
                            <div class="step active" id="step1-indicator">
                                <div class="step-number">1</div>
                                <div class="step-title">Trip Details</div>
                            </div>
                            <div class="step" id="step2-indicator">
                                <div class="step-number">2</div>
                                <div class="step-title">Vehicle Selection</div>
                            </div>
                            <div class="step" id="step3-indicator">
                                <div class="step-number">3</div>
                                <div class="step-title">Review & Payment</div>
                            </div>
                        </div>

                        <form id="bookingForm" action="<?php echo e(route('booking.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="booking_type" id="bookingType" value="one_way">
                            <input type="hidden" name="vehicle_id" id="selectedVehicle">
                            <input type="hidden" name="amount" id="totalFare">
                            <input type="hidden" name="distance_value" id="distance_value">
                            <input type="hidden" name="duration_value" id="duration_value">
                            <input type="hidden" name="pickup_datetime" id="pickup_datetime">
                            <input type="hidden" name="return_datetime" id="return_datetime">
                            <input type="hidden" name="fare_details" id="fare_details">

                            <!-- Address coordinates -->
                            <input type="hidden" name="pickup_lat" id="pickup_lat">
                            <input type="hidden" name="pickup_lng" id="pickup_lng">
                            <input type="hidden" name="dropoff_lat" id="dropoff_lat">
                            <input type="hidden" name="dropoff_lng" id="dropoff_lng">

                            <!-- Airport transfer specific -->
                            <input type="hidden" name="airport_direction" id="airport_direction_hidden">
                            <input type="hidden" name="airport_id" id="airport_id_hidden">

                            <!-- Flight information -->
                            <input type="hidden" name="flight_number" id="flight_number_hidden">
                            <input type="hidden" name="airline" id="airline_hidden">
                            <input type="hidden" name="departure_time" id="departure_time_hidden">
                            <input type="hidden" name="arrival_time" id="arrival_time_hidden">
                            <input type="hidden" name="terminal" id="terminal_hidden">
                            <input type="hidden" name="flight_status" id="flight_status_hidden">
                            <input type="hidden" name="flight_notes" id="flight_notes_hidden">

                            <!-- Step 1: Trip Details -->
                            <div class="step-content active" id="step1-content">
                                <h4 class="mb-4">Trip Details</h4>

                                <!-- Booking Type Tabs -->
                                <ul class="nav nav-tabs mb-4" id="bookingTypeTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button style="color: #dc3545;" class="nav-link active" id="one-way-tab" data-bs-toggle="tab" data-bs-target="#one-way" type="button" role="tab" aria-controls="one-way" aria-selected="true">One Way</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button style="color: #dc3545;" class="nav-link" id="return-tab" data-bs-toggle="tab" data-bs-target="#return" type="button" role="tab" aria-controls="return" aria-selected="false">Return</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button style="color: #dc3545;" class="nav-link" id="airport-tab" data-bs-toggle="tab" data-bs-target="#airport" type="button" role="tab" aria-controls="airport" aria-selected="false">Airport Transfer</button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="bookingTypeTabContent">
                                    <!-- One Way Tab -->
                                    <div class="tab-pane fade show active" id="one-way" role="tabpanel" aria-labelledby="one-way-tab">
                                        <div class="row mb-3">
                                            <div class="col-md-6 mb-3">
                                                <label for="pickup_address" class="form-label">Pickup Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="pickup_address" name="pickup_address" placeholder="Enter pickup location" required data-lat-field="pickup_lat" data-lng-field="pickup_lng" value="<?php echo e($homeFormData['pickup_address'] ?? ''); ?>">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="dropoff_address" class="form-label">Dropoff Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="dropoff_address" name="dropoff_address" placeholder="Enter dropoff location" required data-lat-field="dropoff_lat" data-lng-field="dropoff_lng" value="<?php echo e($homeFormData['dropoff_address'] ?? ''); ?>">
                                            </div>
                                        </div>

                                        <!-- Via Stops Section -->
                                        <div class="mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <label class="form-label mb-0">
                                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                                    </label>
                                                    <div class="text-muted small mt-1">
                                                        Add intermediate destinations to your journey
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="addViaStopBtn">
                                                    <i class="fas fa-plus me-1"></i>Add Stop
                                                </button>
                                            </div>
                                            <div id="viaStopsContainer" class="via-stops-container">
                                                <!-- Via stops will be added here dynamically -->
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Maximum 5 via stops allowed. Each stop incurs an additional charge.
                                                </small>
                                                <small class="text-muted" id="viaStopCounter">
                                                    <span id="currentViaStops">0</span> / 5 stops
                                                </small>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-4 mb-3">
                                                <label for="pickup_date" class="form-label">Pickup Date</label>
                                                <input type="date" class="form-control" id="pickup_date" name="pickup_date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e($homeFormData['pickup_date'] ?: date('Y-m-d')); ?>" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="pickup_time" class="form-label">Pickup Time</label>
                                                <input type="time" class="form-control" id="pickup_time" name="pickup_time" value="<?php echo e($homeFormData['pickup_time'] ?: '12:00'); ?>" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="passengers" class="form-label">Passengers</label>
                                                <select class="form-select" id="passengers" name="passengers" required>
                                                    <option value="1" <?php echo e(($homeFormData['passengers'] ?? 2) == 1 ? 'selected' : ''); ?>>1 Passenger</option>
                                                    <option value="2" <?php echo e(($homeFormData['passengers'] ?? 2) == 2 ? 'selected' : ''); ?>>2 Passengers</option>
                                                    <option value="3" <?php echo e(($homeFormData['passengers'] ?? 2) == 3 ? 'selected' : ''); ?>>3 Passengers</option>
                                                    <option value="4" <?php echo e(($homeFormData['passengers'] ?? 2) == 4 ? 'selected' : ''); ?>>4 Passengers</option>
                                                    <option value="5" <?php echo e(($homeFormData['passengers'] ?? 2) == 5 ? 'selected' : ''); ?>>5 Passengers</option>
                                                    <option value="6" <?php echo e(($homeFormData['passengers'] ?? 2) == 6 ? 'selected' : ''); ?>>6 Passengers</option>
                                                    <option value="7" <?php echo e(($homeFormData['passengers'] ?? 2) == 7 ? 'selected' : ''); ?>>7 Passengers</option>
                                                    <option value="8" <?php echo e(($homeFormData['passengers'] ?? 2) == 8 ? 'selected' : ''); ?>>8 Passengers</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Extra Services -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Extra Services</h5>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="meet_and_greet" name="meet_and_greet" value="1"
                                                               <?php echo e(!$extraServicesSettings['meet_and_greet']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="meet_and_greet">
                                                            <i class="fas fa-handshake me-2"></i>Meet & Greet Service
                                                            <?php if($extraServicesSettings['meet_and_greet']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['meet_and_greet']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Driver will meet you with a name sign</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="child_seat" name="child_seat" value="1"
                                                               <?php echo e(!$extraServicesSettings['child_seat']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="child_seat">
                                                            <i class="fas fa-baby me-2"></i>Child Seat
                                                            <?php if($extraServicesSettings['child_seat']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['child_seat']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Child safety seat provided</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="wheelchair_accessible" name="wheelchair_accessible" value="1"
                                                               <?php echo e(!$extraServicesSettings['wheelchair_accessible']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="wheelchair_accessible">
                                                            <i class="fas fa-wheelchair me-2"></i>Wheelchair Accessible
                                                            <?php if($extraServicesSettings['wheelchair_accessible']['enabled']): ?>
                                                                <?php if($extraServicesSettings['wheelchair_accessible']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Vehicle suitable for wheelchair access</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="extra_luggage" name="extra_luggage" value="1"
                                                               <?php echo e(!$extraServicesSettings['extra_luggage']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="extra_luggage">
                                                            <i class="fas fa-suitcase me-2"></i>Extra Luggage Space
                                                            <?php if($extraServicesSettings['extra_luggage']['enabled']): ?>
                                                                <?php if($extraServicesSettings['extra_luggage']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['extra_luggage']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Additional luggage capacity required</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Flight Information for One Way -->
                                        <div class="mt-4" id="oneway-flight-info-section">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-plane me-2 text-primary"></i>Flight Information
                                                </h5>
                                                <small class="text-muted">Optional - helps us track your flight and provide better service</small>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_flight_number" class="form-label">
                                                        <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                                    </label>
                                                    <input type="text" class="form-control" id="oneway_flight_number" name="oneway_flight_number"
                                                           placeholder="e.g., BA123, EK456" maxlength="20">
                                                    <small class="text-muted">Enter flight number for real-time tracking</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_airline" class="form-label">
                                                        <i class="fas fa-building me-1"></i>Airline
                                                    </label>
                                                    <input type="text" class="form-control" id="oneway_airline" name="oneway_airline"
                                                           placeholder="e.g., British Airways, Emirates" maxlength="100">
                                                    <small class="text-muted">Airline name or code</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_departure_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Departure Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="oneway_departure_time" name="oneway_departure_time">
                                                    <small class="text-muted">Scheduled departure time</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_arrival_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Arrival Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="oneway_arrival_time" name="oneway_arrival_time">
                                                    <small class="text-muted">Scheduled arrival time</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_terminal" class="form-label">
                                                        <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                                    </label>
                                                    <input type="text" class="form-control" id="oneway_terminal" name="oneway_terminal"
                                                           placeholder="e.g., Terminal 1, T2" maxlength="50">
                                                    <small class="text-muted">Departure/arrival terminal</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="oneway_flight_status" class="form-label">
                                                        <i class="fas fa-info-circle me-1"></i>Flight Status
                                                    </label>
                                                    <select class="form-select" id="oneway_flight_status" name="oneway_flight_status">
                                                        <option value="">Select Status</option>
                                                        <option value="scheduled">Scheduled</option>
                                                        <option value="delayed">Delayed</option>
                                                        <option value="boarding">Boarding</option>
                                                        <option value="departed">Departed</option>
                                                        <option value="arrived">Arrived</option>
                                                        <option value="cancelled">Cancelled</option>
                                                    </select>
                                                    <small class="text-muted">Current flight status (if known)</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label for="oneway_flight_notes" class="form-label">
                                                        <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                                    </label>
                                                    <textarea class="form-control" id="oneway_flight_notes" name="oneway_flight_notes" rows="2"
                                                              placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                                    <small class="text-muted">Additional flight-related information</small>
                                                </div>
                                            </div>

                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Flight Tracking:</strong> Providing flight details allows us to monitor your flight status
                                                and adjust pickup times automatically for delays. Our drivers will be notified of any changes.
                                            </div>
                                        </div>

                                        <!-- Special Requests -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Special Requests</h5>
                                            <div class="mb-3">
                                                <label for="notes" class="form-label">Additional Notes</label>
                                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special requests or additional information..."></textarea>
                                                <small class="text-muted">Maximum 500 characters</small>
                                            </div>
                                        </div>

                                        <!-- Map Preview -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Route Preview</h5>
                                            <div id="map" style="height: 300px; width: 100%; border-radius: 10px; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"></div>
                                        </div>
                                    </div>

                                    <!-- Return Tab -->
                                    <div class="tab-pane fade" id="return" role="tabpanel" aria-labelledby="return-tab">
                                        <div class="row mb-3">
                                            <div class="col-md-6 mb-3">
                                                <label for="return_pickup_address" class="form-label">Pickup Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="return_pickup_address" name="return_pickup_address" placeholder="Enter pickup location" data-lat-field="return_pickup_lat" data-lng-field="return_pickup_lng" value="<?php echo e($homeFormData['pickup_address'] ?? ''); ?>">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="return_dropoff_address" class="form-label">Dropoff Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="return_dropoff_address" name="return_dropoff_address" placeholder="Enter dropoff location" data-lat-field="return_dropoff_lat" data-lng-field="return_dropoff_lng" value="<?php echo e($homeFormData['dropoff_address'] ?? ''); ?>">
                                            </div>
                                        </div>

                                        <!-- Via Stops Section for Return -->
                                        <div class="mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <label class="form-label mb-0">
                                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                                    </label>
                                                    <div class="text-muted small mt-1">
                                                        Add intermediate destinations for return journey
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="addReturnViaStopBtn">
                                                    <i class="fas fa-plus me-1"></i>Add Stop
                                                </button>
                                            </div>
                                            <div id="returnViaStopsContainer" class="via-stops-container">
                                                <!-- Return via stops will be added here dynamically -->
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Maximum 5 via stops allowed for return journey.
                                                </small>
                                                <small class="text-muted" id="returnViaStopCounter">
                                                    <span id="currentReturnViaStops">0</span> / 5 stops
                                                </small>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-4 mb-3">
                                                <label for="return_pickup_date" class="form-label">Pickup Date</label>
                                                <input type="date" class="form-control" id="return_pickup_date" name="return_pickup_date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e($homeFormData['pickup_date'] ?: date('Y-m-d')); ?>" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="return_pickup_time" class="form-label">Pickup Time</label>
                                                <input type="time" class="form-control" id="return_pickup_time" name="return_pickup_time" value="<?php echo e($homeFormData['pickup_time'] ?: '12:00'); ?>" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="return_passengers" class="form-label">Passengers</label>
                                                <select class="form-select" id="return_passengers" name="passengers" required>
                                                    <option value="1" <?php echo e(($homeFormData['passengers'] ?? 2) == 1 ? 'selected' : ''); ?>>1 Passenger</option>
                                                    <option value="2" <?php echo e(($homeFormData['passengers'] ?? 2) == 2 ? 'selected' : ''); ?>>2 Passengers</option>
                                                    <option value="3" <?php echo e(($homeFormData['passengers'] ?? 2) == 3 ? 'selected' : ''); ?>>3 Passengers</option>
                                                    <option value="4" <?php echo e(($homeFormData['passengers'] ?? 2) == 4 ? 'selected' : ''); ?>>4 Passengers</option>
                                                    <option value="5" <?php echo e(($homeFormData['passengers'] ?? 2) == 5 ? 'selected' : ''); ?>>5 Passengers</option>
                                                    <option value="6" <?php echo e(($homeFormData['passengers'] ?? 2) == 6 ? 'selected' : ''); ?>>6 Passengers</option>
                                                    <option value="7" <?php echo e(($homeFormData['passengers'] ?? 2) == 7 ? 'selected' : ''); ?>>7 Passengers</option>
                                                    <option value="8" <?php echo e(($homeFormData['passengers'] ?? 2) == 8 ? 'selected' : ''); ?>>8 Passengers</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6 mb-3">
                                                <label for="return_date" class="form-label">Return Date</label>
                                                <input type="date" class="form-control" id="return_date" name="return_date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e($homeFormData['return_date'] ?: date('Y-m-d', strtotime('+1 day'))); ?>" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="return_time" class="form-label">Return Time</label>
                                                <input type="time" class="form-control" id="return_time" name="return_time" value="<?php echo e($homeFormData['return_time'] ?: '12:00'); ?>" required>
                                            </div>
                                        </div>

                                        <!-- Extra Services for Return -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Extra Services</h5>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="return_meet_and_greet" name="meet_and_greet" value="1"
                                                               <?php echo e(!$extraServicesSettings['meet_and_greet']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="return_meet_and_greet">
                                                            <i class="fas fa-handshake me-2"></i>Meet & Greet Service
                                                            <?php if($extraServicesSettings['meet_and_greet']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['meet_and_greet']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Driver will meet you with a name sign</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="return_child_seat" name="child_seat" value="1"
                                                               <?php echo e(!$extraServicesSettings['child_seat']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="return_child_seat">
                                                            <i class="fas fa-baby me-2"></i>Child Seat
                                                            <?php if($extraServicesSettings['child_seat']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['child_seat']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Child safety seat provided</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="return_wheelchair_accessible" name="wheelchair_accessible" value="1"
                                                               <?php echo e(!$extraServicesSettings['wheelchair_accessible']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="return_wheelchair_accessible">
                                                            <i class="fas fa-wheelchair me-2"></i>Wheelchair Accessible
                                                            <?php if($extraServicesSettings['wheelchair_accessible']['enabled']): ?>
                                                                <?php if($extraServicesSettings['wheelchair_accessible']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Vehicle suitable for wheelchair access</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="return_extra_luggage" name="extra_luggage" value="1"
                                                               <?php echo e(!$extraServicesSettings['extra_luggage']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="return_extra_luggage">
                                                            <i class="fas fa-suitcase me-2"></i>Extra Luggage Space
                                                            <?php if($extraServicesSettings['extra_luggage']['enabled']): ?>
                                                                <?php if($extraServicesSettings['extra_luggage']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['extra_luggage']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Additional luggage capacity required</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Flight Information for Return -->
                                        <div class="mt-4" id="return-flight-info-section">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-plane me-2 text-primary"></i>Flight Information
                                                </h5>
                                                <small class="text-muted">Optional - helps us track your flight and provide better service</small>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_flight_number" class="form-label">
                                                        <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                                    </label>
                                                    <input type="text" class="form-control" id="return_flight_number" name="return_flight_number"
                                                           placeholder="e.g., BA123, EK456" maxlength="20">
                                                    <small class="text-muted">Enter flight number for real-time tracking</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_airline" class="form-label">
                                                        <i class="fas fa-building me-1"></i>Airline
                                                    </label>
                                                    <input type="text" class="form-control" id="return_airline" name="return_airline"
                                                           placeholder="e.g., British Airways, Emirates" maxlength="100">
                                                    <small class="text-muted">Airline name or code</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_departure_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Departure Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="return_departure_time" name="return_departure_time">
                                                    <small class="text-muted">Scheduled departure time</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_arrival_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Arrival Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="return_arrival_time" name="return_arrival_time">
                                                    <small class="text-muted">Scheduled arrival time</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_terminal" class="form-label">
                                                        <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                                    </label>
                                                    <input type="text" class="form-control" id="return_terminal" name="return_terminal"
                                                           placeholder="e.g., Terminal 1, T2" maxlength="50">
                                                    <small class="text-muted">Departure/arrival terminal</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="return_flight_status" class="form-label">
                                                        <i class="fas fa-info-circle me-1"></i>Flight Status
                                                    </label>
                                                    <select class="form-select" id="return_flight_status" name="return_flight_status">
                                                        <option value="">Select Status</option>
                                                        <option value="scheduled">Scheduled</option>
                                                        <option value="delayed">Delayed</option>
                                                        <option value="boarding">Boarding</option>
                                                        <option value="departed">Departed</option>
                                                        <option value="arrived">Arrived</option>
                                                        <option value="cancelled">Cancelled</option>
                                                    </select>
                                                    <small class="text-muted">Current flight status (if known)</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label for="return_flight_notes" class="form-label">
                                                        <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                                    </label>
                                                    <textarea class="form-control" id="return_flight_notes" name="return_flight_notes" rows="2"
                                                              placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                                    <small class="text-muted">Additional flight-related information</small>
                                                </div>
                                            </div>

                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Flight Tracking:</strong> Providing flight details allows us to monitor your flight status
                                                and adjust pickup times automatically for delays. Our drivers will be notified of any changes.
                                            </div>
                                        </div>

                                        <!-- Special Requests for Return -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Special Requests</h5>
                                            <div class="mb-3">
                                                <label for="return_notes" class="form-label">Additional Notes</label>
                                                <textarea class="form-control" id="return_notes" name="notes" rows="3" placeholder="Any special requests or additional information..."></textarea>
                                                <small class="text-muted">Maximum 500 characters</small>
                                            </div>
                                        </div>

                                        <!-- Map Preview for Return -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Route Preview</h5>
                                            <div id="return-map" style="height: 300px; width: 100%; border-radius: 10px; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"></div>
                                        </div>
                                    </div>

                                    <!-- Airport Tab -->
                                    <div class="tab-pane fade" id="airport" role="tabpanel" aria-labelledby="airport-tab">
                                        <div class="row mb-3">
                                            <div class="col-md-6 mb-3">
                                                <label for="airport_direction" class="form-label">Transfer Direction</label>
                                                <select class="form-select" id="airport_direction" name="airport_direction">
                                                    <option value="to_airport" <?php echo e(($homeFormData['airport_direction'] ?? 'to_airport') == 'to_airport' ? 'selected' : ''); ?>>To Airport</option>
                                                    <option value="from_airport" <?php echo e(($homeFormData['airport_direction'] ?? 'to_airport') == 'from_airport' ? 'selected' : ''); ?>>From Airport</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="airport_id" class="form-label">Airport</label>
                                                <select class="form-select" id="airport_id" name="airport_id">
                                                    <option value="">Select Airport</option>
                                                    <?php $__currentLoopData = $airports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $airport): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($airport->id); ?>"
                                                                data-code="<?php echo e($airport->code); ?>"
                                                                <?php echo e(($homeFormData['airport_id'] ?? null) == $airport->id ? 'selected' : ''); ?>

                                                                data-city="<?php echo e($airport->city); ?>"
                                                                data-lat="<?php echo e($airport->latitude); ?>"
                                                                data-lng="<?php echo e($airport->longitude); ?>">
                                                            <?php echo e($airport->name); ?> (<?php echo e($airport->code); ?>) - <?php echo e($airport->city); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-3 to-airport-field">
                                            <div class="col-md-12 mb-3">
                                                <label for="airport_pickup_address" class="form-label">Pickup Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="airport_pickup_address" name="airport_pickup_address" placeholder="Enter pickup location" data-lat-field="airport_pickup_lat" data-lng-field="airport_pickup_lng">
                                            </div>
                                        </div>

                                        <div class="row mb-3 from-airport-field" style="display: none;">
                                            <div class="col-md-12 mb-3">
                                                <label for="airport_dropoff_address" class="form-label">Dropoff Address</label>
                                                <input type="text" class="form-control address-autocomplete" id="airport_dropoff_address" name="airport_dropoff_address" placeholder="Enter dropoff location" data-lat-field="airport_dropoff_lat" data-lng-field="airport_dropoff_lng">
                                            </div>
                                        </div>

                                        <!-- Via Stops Section for Airport -->
                                        <div class="mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div>
                                                    <label class="form-label mb-0">
                                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                                    </label>
                                                    <div class="text-muted small mt-1">
                                                        Add intermediate destinations for airport transfer
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="addAirportViaStopBtn">
                                                    <i class="fas fa-plus me-1"></i>Add Stop
                                                </button>
                                            </div>
                                            <div id="airportViaStopsContainer" class="via-stops-container">
                                                <!-- Airport via stops will be added here dynamically -->
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Maximum 5 via stops allowed for airport transfers.
                                                </small>
                                                <small class="text-muted" id="airportViaStopCounter">
                                                    <span id="currentAirportViaStops">0</span> / 5 stops
                                                </small>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-4 mb-3">
                                                <label for="airport_pickup_date" class="form-label">Pickup Date</label>
                                                <input type="date" class="form-control" id="airport_pickup_date" name="airport_pickup_date" min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e(date('Y-m-d')); ?>" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="airport_pickup_time" class="form-label">Pickup Time</label>
                                                <input type="time" class="form-control" id="airport_pickup_time" name="airport_pickup_time" value="12:00" required>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="airport_passengers" class="form-label">Passengers</label>
                                                <select class="form-select" id="airport_passengers" name="passengers" required>
                                                    <option value="1">1 Passenger</option>
                                                    <option value="2" selected>2 Passengers</option>
                                                    <option value="3">3 Passengers</option>
                                                    <option value="4">4 Passengers</option>
                                                    <option value="5">5 Passengers</option>
                                                    <option value="6">6 Passengers</option>
                                                    <option value="7">7 Passengers</option>
                                                    <option value="8">8 Passengers</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Flight Information -->
                                        <div class="mt-4" id="flight-info-section">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-plane me-2 text-primary"></i>Flight Information
                                                </h5>
                                                <small class="text-muted">Optional - helps us track your flight and provide better service</small>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="flight_number" class="form-label">
                                                        <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                                    </label>
                                                    <input type="text" class="form-control" id="flight_number" name="flight_number"
                                                           placeholder="e.g., BA123, EK456" maxlength="20">
                                                    <small class="text-muted">Enter your flight number for real-time tracking</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="airline" class="form-label">
                                                        <i class="fas fa-building me-1"></i>Airline
                                                    </label>
                                                    <input type="text" class="form-control" id="airline" name="airline"
                                                           placeholder="e.g., British Airways, Emirates" maxlength="100">
                                                    <small class="text-muted">Airline name or code</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="departure_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Departure Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="departure_time" name="departure_time">
                                                    <small class="text-muted">Scheduled departure time</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="arrival_time" class="form-label">
                                                        <i class="fas fa-clock me-1"></i>Arrival Time
                                                    </label>
                                                    <input type="datetime-local" class="form-control" id="arrival_time" name="arrival_time">
                                                    <small class="text-muted">Scheduled arrival time</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="terminal" class="form-label">
                                                        <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                                    </label>
                                                    <input type="text" class="form-control" id="terminal" name="terminal"
                                                           placeholder="e.g., Terminal 1, T2" maxlength="50">
                                                    <small class="text-muted">Departure/arrival terminal</small>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="flight_status" class="form-label">
                                                        <i class="fas fa-info-circle me-1"></i>Flight Status
                                                    </label>
                                                    <select class="form-select" id="flight_status" name="flight_status">
                                                        <option value="">Select Status</option>
                                                        <option value="scheduled">Scheduled</option>
                                                        <option value="delayed">Delayed</option>
                                                        <option value="boarding">Boarding</option>
                                                        <option value="departed">Departed</option>
                                                        <option value="arrived">Arrived</option>
                                                        <option value="cancelled">Cancelled</option>
                                                    </select>
                                                    <small class="text-muted">Current flight status (if known)</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <label for="flight_notes" class="form-label">
                                                        <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                                    </label>
                                                    <textarea class="form-control" id="flight_notes" name="flight_notes" rows="2"
                                                              placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                                    <small class="text-muted">Additional flight-related information</small>
                                                </div>
                                            </div>

                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Flight Tracking:</strong> Providing flight details allows us to monitor your flight status
                                                and adjust pickup times automatically for delays. Our drivers will be notified of any changes.
                                            </div>
                                        </div>

                                        <!-- Extra Services for Airport -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Extra Services</h5>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="airport_meet_and_greet" name="meet_and_greet" value="1"
                                                               <?php echo e(!$extraServicesSettings['meet_and_greet']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="airport_meet_and_greet">
                                                            <i class="fas fa-handshake me-2"></i>Meet & Greet Service
                                                            <?php if($extraServicesSettings['meet_and_greet']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['meet_and_greet']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Driver will meet you with a name sign</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="airport_child_seat" name="child_seat" value="1"
                                                               <?php echo e(!$extraServicesSettings['child_seat']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="airport_child_seat">
                                                            <i class="fas fa-baby me-2"></i>Child Seat
                                                            <?php if($extraServicesSettings['child_seat']['enabled']): ?>
                                                                <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['child_seat']['fee'], 2)); ?></span>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Child safety seat provided</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="airport_wheelchair_accessible" name="wheelchair_accessible" value="1"
                                                               <?php echo e(!$extraServicesSettings['wheelchair_accessible']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="airport_wheelchair_accessible">
                                                            <i class="fas fa-wheelchair me-2"></i>Wheelchair Accessible
                                                            <?php if($extraServicesSettings['wheelchair_accessible']['enabled']): ?>
                                                                <?php if($extraServicesSettings['wheelchair_accessible']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Vehicle suitable for wheelchair access</small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="airport_extra_luggage" name="extra_luggage" value="1"
                                                               <?php echo e(!$extraServicesSettings['extra_luggage']['enabled'] ? 'disabled' : ''); ?>>
                                                        <label class="form-check-label" for="airport_extra_luggage">
                                                            <i class="fas fa-suitcase me-2"></i>Extra Luggage Space
                                                            <?php if($extraServicesSettings['extra_luggage']['enabled']): ?>
                                                                <?php if($extraServicesSettings['extra_luggage']['fee'] > 0): ?>
                                                                    <span class="badge bg-primary ms-2"><?php echo e($currencySymbol); ?><?php echo e(number_format($extraServicesSettings['extra_luggage']['fee'], 2)); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success ms-2">Free</span>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <small class="text-muted d-block">Additional luggage capacity required</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Special Requests for Airport -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Special Requests</h5>
                                            <div class="mb-3">
                                                <label for="airport_notes" class="form-label">Additional Notes</label>
                                                <textarea class="form-control" id="airport_notes" name="notes" rows="3" placeholder="Any special requests or additional information..."></textarea>
                                                <small class="text-muted">Maximum 500 characters</small>
                                            </div>
                                        </div>

                                        <!-- Map Preview for Airport -->
                                        <div class="mt-4">
                                            <h5 class="mb-3">Route Preview</h5>
                                            <div id="airport-map" style="height: 300px; width: 100%; border-radius: 10px; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 1 Navigation -->
                                <div class="step-buttons">
                                    <div></div> <!-- Empty div for spacing -->
                                    <button type="button" class="btn btn-primary next-step" id="nextToVehicleBtn" data-step="1">
                                        <span class="button-text">Select Vehicle</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Vehicle Selection -->
                            <div class="step-content" id="step2-content">
                                <h4 class="mb-4">Select Your Vehicle</h4>

                                <div class="row" id="vehicleContainer">
                                    <?php $__currentLoopData = $vehicles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="vehicle-card" data-vehicle-id="<?php echo e($vehicle->id); ?>">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <?php if($vehicle->image): ?>
                                                        <img src="<?php echo e(asset('storage/' . $vehicle->image)); ?>" alt="<?php echo e($vehicle->name); ?>" style="width: 120px; height: 80px; object-fit: cover; border-radius: 5px;">
                                                    <?php else: ?>
                                                        <div style="width: 120px; height: 80px; background-color: #f8f9fa; border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="fas fa-car fa-2x text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <h5 class="vehicle-name mb-1"><?php echo e($vehicle->name); ?></h5>
                                                    <p class="text-muted mb-2"><?php echo e($vehicle->type); ?></p>
                                                    <div class="vehicle-details">
                                                        <div class="vehicle-detail me-3">
                                                            <i class="fas fa-users me-1"></i>
                                                            <p class="mb-0"><?php echo e($vehicle->seats); ?> passengers</p>
                                                        </div>
                                                        <div class="vehicle-detail">
                                                            <i class="fas fa-suitcase me-1"></i>
                                                            <p class="mb-0"><?php echo e($vehicle->luggage_capacity); ?> luggage</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <div>
                                                    <span class="fw-bold"><?php echo e($currencySymbol); ?><?php echo e(number_format($vehicle->base_fare, 2)); ?></span>
                                                    <span class="text-muted">base fare</span>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm select-vehicle-btn">
                                                    <i class="fas fa-check me-1"></i>Select
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                                <div class="mt-4">
                                    <div id="fareCalculationStatus" class="alert alert-info d-none">
                                        <div class="d-flex align-items-center">
                                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <div>Calculating fare...</div>
                                        </div>
                                    </div>

                                    <div id="fareResults" class="d-none">
                                        <div class="alert alert-success">
                                            <h6 class="alert-heading"><i class="fas fa-check-circle me-2"></i>Fare Calculated</h6>
                                            <p class="mb-0">Fares are automatically calculated when you select a vehicle.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 2 Navigation -->
                                <div class="step-buttons">
                                    <button type="button" class="btn btn-outline-secondary prev-step" id="backToTripBtn" data-step="2">
                                        <span class="button-text">Back to Trip Details</span>
                                    </button>
                                    <button type="button" class="btn btn-primary next-step d-none" id="toReviewBtn" data-step="2">
                                        <span class="button-text">Review Book</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Review & Payment -->
                            <div class="step-content" id="step3-content">
                                <h4 class="mb-4">Review & Payment</h4>

                                <div class="booking-summary">
                                    <div class="summary-section">
                                        <h5 class="summary-title">Trip Details</h5>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Trip Type:</span>
                                            <span class="summary-item-value" id="summary-trip-type">One Way</span>
                                        </div>
                                        <div class="summary-item" id="summary-airport-direction-container" style="display: none;">
                                            <span class="summary-item-label">Direction:</span>
                                            <span class="summary-item-value" id="summary-airport-direction">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Pickup Address:</span>
                                            <span class="summary-item-value" id="summary-pickup">-</span>
                                        </div>
                                        <div class="summary-item" id="summary-dropoff-container">
                                            <span class="summary-item-label">Dropoff Address:</span>
                                            <span class="summary-item-value" id="summary-dropoff">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Pickup Date & Time:</span>
                                            <span class="summary-item-value" id="summary-datetime">-</span>
                                        </div>
                                        <div class="summary-item" id="summary-return-container" style="display: none;">
                                            <span class="summary-item-label">Return Date & Time:</span>
                                            <span class="summary-item-value" id="summary-return-datetime">-</span>
                                        </div>
                                        <div class="summary-item" id="summary-duration-container" style="display: none;">
                                            <span class="summary-item-label">Duration:</span>
                                            <span class="summary-item-value" id="summary-duration">-</span>
                                        </div>
                                    </div>

                                    <div class="summary-section">
                                        <h5 class="summary-title">Vehicle Details</h5>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Vehicle:</span>
                                            <span class="summary-item-value" id="summary-vehicle">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Type:</span>
                                            <span class="summary-item-value" id="summary-vehicle-type">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Capacity (Passengers / Luggage):</span>
                                            <span class="summary-item-value" id="summary-capacity">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Passengers:</span>
                                            <span class="summary-item-value" id="summary-passengers">-</span>
                                        </div>
                                    </div>

                                    <div class="summary-section" id="via-stops-summary" style="display: none;">
                                        <h5 class="summary-title">Via Stops</h5>
                                        <!-- Via stops will be dynamically populated here -->
                                    </div>

                                    <div class="summary-section" id="extra-services-summary" style="display: none;">
                                        <h5 class="summary-title">Extra Services</h5>
                                        <div class="summary-item" id="summary-meet-greet-row" style="display: none;">
                                            <span class="summary-item-label">Meet & Greet Service:</span>
                                            <span class="summary-item-value">✓ Included</span>
                                        </div>
                                        <div class="summary-item" id="summary-child-seat-row" style="display: none;">
                                            <span class="summary-item-label">Child Seat:</span>
                                            <span class="summary-item-value">✓ Included</span>
                                        </div>
                                        <div class="summary-item" id="summary-wheelchair-row" style="display: none;">
                                            <span class="summary-item-label">Wheelchair Accessible:</span>
                                            <span class="summary-item-value">✓ Required</span>
                                        </div>
                                        <div class="summary-item" id="summary-extra-luggage-row" style="display: none;">
                                            <span class="summary-item-label">Extra Luggage Space:</span>
                                            <span class="summary-item-value">✓ Required</span>
                                        </div>
                                    </div>

                                    <div class="summary-section" id="flight-info-summary" style="display: none;">
                                        <h5 class="summary-title">Flight Information</h5>
                                        <div class="summary-item" id="summary-flight-number-row" style="display: none;">
                                            <span class="summary-item-label">Flight Number:</span>
                                            <span class="summary-item-value" id="summary-flight-number">-</span>
                                        </div>
                                        <div class="summary-item" id="summary-airline-row" style="display: none;">
                                            <span class="summary-item-label">Airline:</span>
                                            <span class="summary-item-value" id="summary-airline">-</span>
                                        </div>
                                    </div>

                                    <div class="summary-section" id="special-requests-summary" style="display: none;">
                                        <h5 class="summary-title">Special Requests</h5>
                                        <div class="summary-item">
                                            <span class="summary-item-label">Notes:</span>
                                            <span class="summary-item-value" id="summary-notes">-</span>
                                        </div>
                                    </div>

                                    <div class="summary-section">
                                        <h5 class="summary-title">Fare Details</h5>
                                        <div class="fare-breakdown">
                                            <div class="summary-item">
                                                <span class="summary-item-label">Distance:</span>
                                                <span class="summary-item-value" id="summary-distance">-</span>
                                            </div>
                                            <div class="summary-item">
                                                <span class="summary-item-label">Base Fare:</span>
                                                <span class="summary-item-value" id="summary-base-fare">-</span>
                                            </div>
                                            <div class="summary-item">
                                                <span class="summary-item-label">Distance Fare:</span>
                                                <span class="summary-item-value" id="summary-distance-fare">-</span>
                                            </div>
                                            <div class="summary-item">
                                                <span class="summary-item-label">Booking Fee:</span>
                                                <span class="summary-item-value" id="summary-booking-fee">-</span>
                                            </div>
                                            <div class="summary-item" id="summary-airport-surcharge-row" style="display: none;">
                                                <span class="summary-item-label">Airport Surcharge:</span>
                                                <span class="summary-item-value" id="summary-airport-surcharge">-</span>
                                            </div>
                                            <div class="summary-item" id="summary-weekend-surcharge-row" style="display: none;">
                                                <span class="summary-item-label">Weekend Surcharge:</span>
                                                <span class="summary-item-value" id="summary-weekend-surcharge">-</span>
                                            </div>
                                            <div class="summary-item" id="summary-night-surcharge-row" style="display: none;">
                                                <span class="summary-item-label">Night Surcharge:</span>
                                                <span class="summary-item-value" id="summary-night-surcharge">-</span>
                                            </div>
                                            <div class="summary-item" id="summary-via-charges-row" style="display: none;">
                                                <span class="summary-item-label">Via Stops (<span id="summary-via-count">0</span> stops):</span>
                                                <span class="summary-item-value" id="summary-via-charges">-</span>
                                            </div>

                                            <!-- Extra Services Container (dynamically populated) -->
                                            <div id="fare-extra-services-container" style="display: none;"></div>

                                            <div class="summary-item" id="summary-tax-row" style="display: none;">
                                                <span class="summary-item-label">Tax (<span id="summary-tax-rate">0.00</span>%):</span>
                                                <span class="summary-item-value" id="summary-tax-amount">-</span>
                                            </div>
                                            <div class="summary-item border-top" style="font-weight: 700; margin-top: 10px; padding-top: 10px;">
                                                <span class="summary-item-label">Total Fare:</span>
                                                <span class="summary-item-value" id="summary-total-fare">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 3 Navigation -->
                                <div class="step-buttons">
                                    <button type="button" class="btn btn-outline-secondary prev-step" id="backToVehicleBtn" data-step="3">
                                        <span class="button-text">Back to Vehicle Selection</span>
                                    </button>
                                    <button type="submit" id="bookNowBtn" class="btn btn-success">
                                        <span class="button-text"><i class="fas fa-check-circle me-2"></i>Book Now</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Google Maps JavaScript API -->
<script>
    function initGoogleMapsApi() {
        console.log('Google Maps API loaded successfully');

        // Check if all required Google Maps services are available
        if (typeof google !== 'undefined' &&
            typeof google.maps !== 'undefined' &&
            typeof google.maps.places !== 'undefined') {

            console.log('All Google Maps services are available');
            // Dispatch event to notify other scripts that Google Maps API is loaded
            window.dispatchEvent(new Event('google-maps-loaded'));
        } else {
            console.error('Google Maps API loaded but some services are missing');
            if (typeof showApiKeyError === 'function') {
                showApiKeyError();
            }
        }
    }
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e($googleMapsApiKey); ?>&libraries=places&callback=initGoogleMapsApi" async defer></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Custom JS -->
<script>
    // Global Google Maps API error handler
    window.gm_authFailure = function() {
        console.error('Google Maps API authentication failed. Please check your API key and billing settings.');
        Swal.fire({
            icon: 'error',
            title: 'Google Maps Error',
            text: 'Google Maps API authentication failed. Please contact support if this issue persists.',
            confirmButtonColor: '#ee393d'
        });
    };

    // Handle other Google Maps API errors
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('Google Maps')) {
            console.error('Google Maps API Error:', e.message);
        }
    });

    // Pass PHP variables to JavaScript as window properties to avoid redeclaration issues
    window.currencySymbol = "<?php echo e($currencySymbol); ?>";
    window.currencyCode = "<?php echo e($currencyCode); ?>";
    window.countryCode = "<?php echo e($countryCode); ?>";
    window.distanceUnit = "<?php echo e($distanceUnit); ?>";

    // Pass autocomplete settings
    window.autocompleteSettings = {
        enabled: <?php echo e($autocompleteSettings['enabled'] ? 'true' : 'false'); ?>,
        restrict_country: <?php echo e($autocompleteSettings['restrict_country'] ? 'true' : 'false'); ?>,
        country: "<?php echo e($autocompleteSettings['country']); ?>",
        types: "<?php echo e($autocompleteSettings['types']); ?>",
        bias_radius: <?php echo e($autocompleteSettings['bias_radius']); ?>,
        use_strict_bounds: <?php echo e($autocompleteSettings['use_strict_bounds'] ? 'true' : 'false'); ?>,
        fields: "<?php echo e($autocompleteSettings['fields']); ?>"
    };

    // Pass extra services settings
    window.extraServicesSettings = {
        meet_and_greet: {
            enabled: <?php echo e($extraServicesSettings['meet_and_greet']['enabled'] ? 'true' : 'false'); ?>,
            fee: <?php echo e($extraServicesSettings['meet_and_greet']['fee']); ?>,
            label: "<?php echo e($extraServicesSettings['meet_and_greet']['label']); ?>",
            description: "<?php echo e($extraServicesSettings['meet_and_greet']['description']); ?>"
        },
        child_seat: {
            enabled: <?php echo e($extraServicesSettings['child_seat']['enabled'] ? 'true' : 'false'); ?>,
            fee: <?php echo e($extraServicesSettings['child_seat']['fee']); ?>,
            label: "<?php echo e($extraServicesSettings['child_seat']['label']); ?>",
            description: "<?php echo e($extraServicesSettings['child_seat']['description']); ?>"
        },
        wheelchair_accessible: {
            enabled: <?php echo e($extraServicesSettings['wheelchair_accessible']['enabled'] ? 'true' : 'false'); ?>,
            fee: <?php echo e($extraServicesSettings['wheelchair_accessible']['fee']); ?>,
            label: "<?php echo e($extraServicesSettings['wheelchair_accessible']['label']); ?>",
            description: "<?php echo e($extraServicesSettings['wheelchair_accessible']['description']); ?>"
        },
        extra_luggage: {
            enabled: <?php echo e($extraServicesSettings['extra_luggage']['enabled'] ? 'true' : 'false'); ?>,
            fee: <?php echo e($extraServicesSettings['extra_luggage']['fee']); ?>,
            label: "<?php echo e($extraServicesSettings['extra_luggage']['label']); ?>",
            description: "<?php echo e($extraServicesSettings['extra_luggage']['description']); ?>"
        }
    };

    // Home form data integration
    const homeFormData = <?php echo json_encode($homeFormData ?? [], 15, 512) ?>;

    // Initialize booking form with home form data
    document.addEventListener('DOMContentLoaded', function() {
        if (homeFormData.has_data) {
            console.log('Home form data detected:', homeFormData);

            // Set the correct booking type tab
            const bookingType = homeFormData.booking_type || 'one_way';

            // Update the hidden booking type field
            document.getElementById('bookingType').value = bookingType;

            // Activate the correct tab
            let tabToActivate = 'one-way-tab';
            let tabPaneToActivate = 'one-way';

            if (bookingType === 'return') {
                tabToActivate = 'return-tab';
                tabPaneToActivate = 'return';
            } else if (bookingType === 'airport_transfer') {
                tabToActivate = 'airport-tab';
                tabPaneToActivate = 'airport';
            }

            // Deactivate all tabs
            document.querySelectorAll('#bookingTypeTab .nav-link').forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });

            document.querySelectorAll('#bookingTypeTabContent .tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // Activate the correct tab
            const targetTab = document.getElementById(tabToActivate);
            const targetPane = document.getElementById(tabPaneToActivate);

            if (targetTab && targetPane) {
                targetTab.classList.add('active');
                targetTab.setAttribute('aria-selected', 'true');
                targetPane.classList.add('show', 'active');
            }

            // Pre-select vehicle if specified
            if (homeFormData.vehicle_id) {
                // This will be handled when the vehicle selection step is reached
                window.preSelectedVehicleId = homeFormData.vehicle_id;
            }

            // Show success message
            if (homeFormData.pickup_address || homeFormData.dropoff_address) {
                // Create a subtle notification
                const notification = document.createElement('div');
                notification.className = 'alert alert-info alert-dismissible fade show';
                notification.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Form Pre-filled:</strong> Your booking details have been transferred from the home page.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // Insert the notification at the top of the form
                const cardBody = document.querySelector('.booking-card .card-body');
                if (cardBody) {
                    cardBody.insertBefore(notification, cardBody.firstChild);

                    // Auto-dismiss after 5 seconds
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 5000);
                }
            }
        }

        // Enhanced autocomplete initialization
        // Listen for Google Maps API loaded event
        window.addEventListener('google-maps-loaded', function() {
            console.log('Google Maps API loaded, enhancing autocomplete functionality');
            enhanceAutocompleteFeatures();
        });

        // Check if Google Maps API is already loaded
        if (typeof google !== 'undefined' && typeof google.maps !== 'undefined' && typeof google.maps.places !== 'undefined') {
            console.log('Google Maps API already loaded, enhancing autocomplete functionality');
            enhanceAutocompleteFeatures();
        }
    });

    // Enhanced autocomplete features
    function enhanceAutocompleteFeatures() {
        try {
            // Add enhanced styling and behavior to autocomplete inputs
            const addressInputs = document.querySelectorAll('.address-autocomplete');

            addressInputs.forEach(input => {
                if (input && !input.classList.contains('autocomplete-enhanced')) {
                    // Mark as enhanced to avoid duplicate processing
                    input.classList.add('autocomplete-enhanced');

                    // Add visual indicators
                    input.style.borderLeft = '3px solid #28a745';
                    input.title = 'Autocomplete enabled - start typing to see suggestions';

                    // Add loading indicator functionality
                    input.addEventListener('input', function() {
                        if (this.value.length > 2) {
                            this.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' fill=\'%23007bff\' viewBox=\'0 0 16 16\'%3E%3Cpath d=\'M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM7 3a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0V3zm0 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0z\'/%3E%3C/svg%3E")';
                            this.style.backgroundRepeat = 'no-repeat';
                            this.style.backgroundPosition = 'right 10px center';
                            this.style.backgroundSize = '16px';
                        } else {
                            this.style.backgroundImage = '';
                        }
                    });

                    // Clear loading indicator when place is selected
                    input.addEventListener('blur', function() {
                        this.style.backgroundImage = '';
                    });
                }
            });

            console.log('Enhanced autocomplete features applied to', addressInputs.length, 'inputs');

        } catch (error) {
            console.error('Error enhancing autocomplete features:', error);
        }
    }
</script>
<script src="<?php echo e(asset('js/booking.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/booking/index.blade.php ENDPATH**/ ?>