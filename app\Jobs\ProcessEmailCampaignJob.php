<?php

namespace App\Jobs;

use App\Models\EmailCampaign;
use App\Services\EmailQueueService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessEmailCampaignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected EmailCampaign $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(EmailCampaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing email campaign', [
                'campaign_id' => $this->campaign->id,
                'campaign_name' => $this->campaign->name,
            ]);

            // Update campaign status
            $this->campaign->update([
                'status' => 'sending',
                'sent_at' => now(),
            ]);

            // Process recipients in batches
            EmailQueueService::processCampaignBatch($this->campaign, 50);

            // Schedule next batch if there are more pending recipients
            $pendingCount = $this->campaign->recipients()
                ->where('status', 'pending')
                ->count();

            if ($pendingCount > 0) {
                // Delay next batch by 1 minute to avoid overwhelming the email service
                ProcessEmailCampaignJob::dispatch($this->campaign)->delay(now()->addMinute());
            }

        } catch (\Exception $e) {
            Log::error('Failed to process email campaign', [
                'campaign_id' => $this->campaign->id,
                'error' => $e->getMessage(),
            ]);

            $this->campaign->update(['status' => 'failed']);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Email campaign processing job failed', [
            'campaign_id' => $this->campaign->id,
            'error' => $exception->getMessage(),
        ]);

        $this->campaign->update(['status' => 'failed']);
    }
}
