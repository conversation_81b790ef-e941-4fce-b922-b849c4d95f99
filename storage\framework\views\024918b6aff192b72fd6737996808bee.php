<?php $__env->startSection('title', 'Call Analytics'); ?>

<?php $__env->startPush('styles'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Call Analytics</h1>
                <div class="btn-group">
                    <a href="<?php echo e(route('admin.calls.dashboard')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <button class="btn btn-outline-secondary" onclick="exportAnalytics()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Date From</label>
                    <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from', now()->subDays(30)->format('Y-m-d'))); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date To</label>
                    <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to', now()->format('Y-m-d'))); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Period</label>
                    <select name="period" class="form-select">
                        <option value="day" <?php echo e(request('period') === 'day' ? 'selected' : ''); ?>>Daily</option>
                        <option value="week" <?php echo e(request('period') === 'week' ? 'selected' : ''); ?>>Weekly</option>
                        <option value="month" <?php echo e(request('period') === 'month' ? 'selected' : ''); ?>>Monthly</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="fas fa-chart-line"></i> Update Analytics
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="total-calls"><?php echo e($analytics['summary']['total_calls']); ?></h4>
                            <p class="card-text">Total Calls</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e($analytics['summary']['answered_calls']); ?></h4>
                            <p class="card-text">Answered (<?php echo e($analytics['summary']['answer_rate']); ?>%)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone-volume fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e($analytics['summary']['missed_calls']); ?></h4>
                            <p class="card-text">Missed (<?php echo e($analytics['summary']['miss_rate']); ?>%)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-phone-slash fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo e($analytics['summary']['identified_calls']); ?></h4>
                            <p class="card-text">Identified (<?php echo e($analytics['summary']['identification_rate']); ?>%)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Call Volume Chart -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Call Volume Over Time</h5>
                </div>
                <div class="card-body">
                    <canvas id="callVolumeChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Call Status Distribution -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Call Status Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="callStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Peak Hours -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Peak Call Hours</h5>
                </div>
                <div class="card-body">
                    <canvas id="peakHoursChart" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Callers -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Frequent Callers</h5>
                </div>
                <div class="card-body">
                    <?php if(count($analytics['top_callers']) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Caller</th>
                                        <th>Calls</th>
                                        <th>Last Call</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $analytics['top_callers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $caller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($caller['client_name']): ?>
                                                <strong><?php echo e($caller['client_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo e($caller['caller_number']); ?></small>
                                            <?php else: ?>
                                                <?php echo e($caller['caller_number']); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($caller['call_count']); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e(\Carbon\Carbon::parse($caller['last_call'])->diffForHumans()); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No call data available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Response Time Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Call Handling Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo e($analytics['performance']['avg_response_time']); ?></h4>
                                <p class="text-muted">Avg Response Time</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success"><?php echo e($analytics['performance']['calls_handled_today']); ?></h4>
                                <p class="text-muted">Handled Today</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning"><?php echo e($analytics['performance']['pending_follow_ups']); ?></h4>
                                <p class="text-muted">Pending Follow-ups</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo e($analytics['performance']['client_conversion_rate']); ?>%</h4>
                                <p class="text-muted">Client Conversion</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Chart data from backend
const chartData = <?php echo json_encode($analytics['charts'], 15, 512) ?>;

// Call Volume Chart
const callVolumeCtx = document.getElementById('callVolumeChart').getContext('2d');
new Chart(callVolumeCtx, {
    type: 'line',
    data: {
        labels: chartData.volume.labels,
        datasets: [{
            label: 'Total Calls',
            data: chartData.volume.total,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'Answered',
            data: chartData.volume.answered,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }, {
            label: 'Missed',
            data: chartData.volume.missed,
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Call Status Chart
const callStatusCtx = document.getElementById('callStatusChart').getContext('2d');
new Chart(callStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Answered', 'Missed', 'Handled'],
        datasets: [{
            data: [
                chartData.status.answered,
                chartData.status.missed,
                chartData.status.handled
            ],
            backgroundColor: ['#28a745', '#ffc107', '#007bff']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true
    }
});

// Peak Hours Chart
const peakHoursCtx = document.getElementById('peakHoursChart').getContext('2d');
new Chart(peakHoursCtx, {
    type: 'bar',
    data: {
        labels: chartData.peak_hours.labels,
        datasets: [{
            label: 'Calls per Hour',
            data: chartData.peak_hours.data,
            backgroundColor: 'rgba(0, 123, 255, 0.8)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function exportAnalytics() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '<?php echo e(route("admin.calls.analytics")); ?>?' + params.toString();
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/calls/analytics.blade.php ENDPATH**/ ?>