<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Airport;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class ExtraServicesBookingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vehicle;
    protected $airport;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'client',
            'email_verified_at' => now(),
        ]);

        // Create test vehicle
        $this->vehicle = Vehicle::factory()->create([
            'is_active' => true,
        ]);

        // Create test airport
        $this->airport = Airport::factory()->create();

        // Initialize extra services settings
        $this->initializeExtraServicesSettings();

        // Mock the services to avoid configuration issues
        $this->mock(\App\Services\PayPalService::class);
        $this->mock(\App\Services\PayPalCardService::class);
        
        // Mock GoogleMapsService with proper expectations
        $this->mock(\App\Services\GoogleMapsService::class, function ($mock) {
            $mock->shouldReceive('geocodeAddress')
                ->andReturn([
                    'lat' => 51.5074,
                    'lng' => -0.1278,
                    'formatted_address' => 'Test Address'
                ]);
        });
    }

    private function initializeExtraServicesSettings()
    {
        $settings = [
            ['key' => 'meet_and_greet_fee', 'value' => '10.00', 'group' => 'extra_services'],
            ['key' => 'child_seat_fee', 'value' => '15.00', 'group' => 'extra_services'],
            ['key' => 'wheelchair_fee', 'value' => '0.00', 'group' => 'extra_services'],
            ['key' => 'extra_luggage_fee', 'value' => '5.00', 'group' => 'extra_services'],
            ['key' => 'meet_and_greet_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'child_seat_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'wheelchair_enabled', 'value' => 'true', 'group' => 'extra_services'],
            ['key' => 'extra_luggage_enabled', 'value' => 'true', 'group' => 'extra_services'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /** @test */
    public function booking_form_displays_extra_services_with_pricing()
    {
        $response = $this->actingAs($this->user)
            ->get(route('booking.index'));

        $response->assertStatus(200);
        
        // Check that extra services are displayed
        $response->assertSee('Extra Services');
        $response->assertSee('Meet & Greet Service');
        $response->assertSee('Child Seat');
        $response->assertSee('Wheelchair Accessible');
        $response->assertSee('Extra Luggage Space');
        
        // Check that pricing is displayed
        $response->assertSee('$10.00'); // Meet & Greet fee
        $response->assertSee('$15.00'); // Child Seat fee
        $response->assertSee('Free');   // Wheelchair (free)
        $response->assertSee('$5.00');  // Extra Luggage fee
    }

    /** @test */
    public function can_create_booking_with_extra_services()
    {
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00,
            'distance_value' => '10.5',
            'duration_value' => 20,
            
            // Extra services
            'meet_and_greet' => '1',
            'child_seat' => '1',
            'wheelchair_accessible' => '1',
            'extra_luggage' => '1',
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302); // Redirect to payment

        // Verify booking was created with extra services
        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);
        
        // Check extra services are saved
        $this->assertTrue($booking->meet_and_greet);
        $this->assertTrue($booking->child_seat);
        $this->assertTrue($booking->wheelchair_accessible);
        $this->assertTrue($booking->extra_luggage);
    }

    /** @test */
    public function can_create_booking_without_extra_services()
    {
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00,
            'distance_value' => '10.5',
            'duration_value' => 20,
            // No extra services selected
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.store'), $bookingData);

        $response->assertStatus(302); // Redirect to payment

        // Verify booking was created without extra services
        $booking = Booking::where('user_id', $this->user->id)->first();
        $this->assertNotNull($booking);
        
        // Check extra services are false
        $this->assertFalse($booking->meet_and_greet);
        $this->assertFalse($booking->child_seat);
        $this->assertFalse($booking->wheelchair_accessible);
        $this->assertFalse($booking->extra_luggage);
    }

    /** @test */
    public function extra_services_are_included_in_fare_calculation()
    {
        $extraServices = [
            'meet_and_greet' => true,
            'child_seat' => true,
            'wheelchair_accessible' => true,
            'extra_luggage' => true,
        ];

        $response = $this->actingAs($this->user)
            ->post(route('booking.calculate-fare'), [
                'booking_type' => 'one_way',
                'pickup_address' => '123 Test Street',
                'dropoff_address' => '456 Test Avenue',
                'pickup_date' => Carbon::now()->addDays(1)->format('Y-m-d'),
                'pickup_time' => '10:00',
                'vehicle_id' => $this->vehicle->id,
                'extra_services' => $extraServices,
                'passengers' => 2,
            ]);

        $response->assertStatus(200);
        $fareData = $response->json();



        // Check that extra services are included in fare breakdown
        $this->assertArrayHasKey('fare_details', $fareData);
        $this->assertArrayHasKey('extra_services_total', $fareData['fare_details']);
        $this->assertArrayHasKey('extra_services', $fareData['fare_details']);

        // Expected total: 10.00 + 15.00 + 0.00 + 5.00 = 30.00 (based on actual system values)
        $this->assertEquals(30.00, $fareData['fare_details']['extra_services_total']);

        // Check individual service fees (based on actual system values)
        $this->assertEquals(10.00, $fareData['fare_details']['extra_services']['meet_and_greet']);
        $this->assertEquals(15.00, $fareData['fare_details']['extra_services']['child_seat']);
        $this->assertEquals(0.00, $fareData['fare_details']['extra_services']['wheelchair_accessible']);
        $this->assertEquals(5.00, $fareData['fare_details']['extra_services']['extra_luggage']);
    }

    /** @test */
    public function guest_booking_stores_extra_services_in_session()
    {
        $bookingData = [
            'booking_type' => 'one_way',
            'pickup_address' => '123 Test Street, Test City',
            'dropoff_address' => '456 Test Avenue, Test City',
            'pickup_datetime' => Carbon::now()->addDays(1)->format('Y-m-d H:i'),
            'vehicle_id' => $this->vehicle->id,
            'amount' => 50.00,
            'distance_value' => '10.5',
            'duration_value' => 20,
            
            // Extra services
            'meet_and_greet' => '1',
            'child_seat' => '1',
            'extra_luggage' => '1',
        ];

        // Make request as guest (not authenticated)
        $response = $this->post(route('booking.store'), $bookingData);

        $response->assertStatus(302); // Redirect to guest review

        // Check that extra services are stored in session
        $guestBooking = session('guest_booking');
        $this->assertNotNull($guestBooking);
        $this->assertEquals(1, $guestBooking['meet_and_greet']);
        $this->assertEquals(1, $guestBooking['child_seat']);
        $this->assertEquals(0, $guestBooking['wheelchair_accessible']); // Not selected
        $this->assertEquals(1, $guestBooking['extra_luggage']);
    }

    /** @test */
    public function disabled_extra_services_are_not_available()
    {
        // Disable meet and greet service
        Setting::where('key', 'meet_and_greet_enabled')->update(['value' => 'false']);

        $response = $this->actingAs($this->user)
            ->get(route('booking.index'));

        $response->assertStatus(200);
        
        // Check that disabled service has disabled attribute
        $response->assertSee('disabled', false); // Check for disabled attribute in HTML
    }
}
