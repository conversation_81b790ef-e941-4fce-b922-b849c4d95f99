<?php $__env->startSection('title', 'Call Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Call Details</h1>
                <div class="btn-group">
                    <a href="<?php echo e(route('admin.calls.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Calls
                    </a>
                    <a href="<?php echo e(route('admin.calls.dashboard')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Call Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Call Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Call ID:</strong></td>
                                    <td><?php echo e($call->call_id); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date/Time:</strong></td>
                                    <td>
                                        <?php echo e($call->created_at->format('M j, Y \a\t H:i:s')); ?><br>
                                        <small class="text-muted"><?php echo e($call->created_at->diffForHumans()); ?></small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Caller Number:</strong></td>
                                    <td>
                                        <strong><?php echo e($call->caller_number); ?></strong>
                                        <?php if($call->caller_number_e164 && $call->caller_number_e164 !== $call->caller_number): ?>
                                            <br><small class="text-muted">E164: <?php echo e($call->caller_number_e164); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Dialled Number:</strong></td>
                                    <td><?php echo e($call->dialled_number ?? 'N/A'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Event Type:</strong></td>
                                    <td>
                                        <span class="badge <?php echo e($call->event_type_badge_class); ?> text-white">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $call->event_type))); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge <?php echo e($call->status_badge_class); ?> text-white">
                                            <?php echo e(ucfirst($call->status)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Handled By:</strong></td>
                                    <td>
                                        <?php if($call->handler): ?>
                                            <?php echo e($call->handler->name); ?><br>
                                            <small class="text-muted"><?php echo e($call->handled_at?->format('M j, Y H:i')); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">Not handled</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Follow-up:</strong></td>
                                    <td>
                                        <?php if($call->follow_up_required): ?>
                                            <span class="text-warning">
                                                <i class="fas fa-flag"></i> Required
                                            </span>
                                            <?php if($call->follow_up_at): ?>
                                                <br><small class="text-muted"><?php echo e($call->follow_up_at->format('M j, Y H:i')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not required</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Client Information</h5>
                    <?php if(!$call->client): ?>
                        <button class="btn btn-sm btn-primary" onclick="identifyClient()">
                            <i class="fas fa-user-plus"></i> Identify Client
                        </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if($call->client): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td><?php echo e($call->client->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo e($call->client->email); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo e($call->client->phone); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td><?php echo e($call->client->address ?? 'N/A'); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Member Since:</strong></td>
                                        <td><?php echo e($call->client->created_at->format('M j, Y')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Bookings:</strong></td>
                                        <td><?php echo e($call->client->bookings()->count()); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Booking:</strong></td>
                                        <td>
                                            <?php $lastBooking = $call->client->bookings()->latest()->first(); ?>
                                            <?php echo e($lastBooking ? $lastBooking->created_at->format('M j, Y') : 'None'); ?>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Spent:</strong></td>
                                        <td>£<?php echo e(number_format($call->client->bookings()->where('status', 'completed')->sum('amount'), 2)); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="<?php echo e(route('admin.users.show', $call->client)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-user"></i> View Client Profile
                            </a>
                            <a href="<?php echo e(route('admin.bookings.create', ['client_id' => $call->client->id])); ?>" class="btn btn-outline-success">
                                <i class="fas fa-plus"></i> Create Booking
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-question fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Client Not Identified</h6>
                            <p class="text-muted">This caller has not been identified as an existing client.</p>
                            <button class="btn btn-primary" onclick="identifyClient()">
                                <i class="fas fa-user-plus"></i> Identify Client
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Call History -->
            <?php if($clientCallHistory && $clientCallHistory->count() > 0): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Client's Call History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Number</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $clientCallHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $historyCall): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($historyCall->created_at->format('M j, Y H:i')); ?></td>
                                    <td><?php echo e($historyCall->caller_number); ?></td>
                                    <td>
                                        <span class="badge <?php echo e($historyCall->event_type_badge_class); ?> text-white">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $historyCall->event_type))); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($historyCall->status_badge_class); ?> text-white">
                                            <?php echo e(ucfirst($historyCall->status)); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Actions & Notes -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <?php if($call->status !== 'handled'): ?>
                        <button class="btn btn-success w-100 mb-2" onclick="markAsHandled()">
                            <i class="fas fa-check"></i> Mark as Handled
                        </button>
                    <?php endif; ?>
                    
                    <button class="btn btn-warning w-100 mb-2" onclick="toggleFollowUp()">
                        <i class="fas fa-flag"></i> 
                        <?php echo e($call->follow_up_required ? 'Remove Follow-up' : 'Require Follow-up'); ?>

                    </button>
                    
                    <?php if($call->client): ?>
                        <a href="tel:<?php echo e($call->client->phone); ?>" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-phone"></i> Call Back
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Notes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Notes</h5>
                </div>
                <div class="card-body">
                    <form id="notesForm">
                        <div class="mb-3">
                            <textarea name="notes" class="form-control" rows="6" placeholder="Add notes about this call..."><?php echo e($call->notes); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save"></i> Save Notes
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Client Identification Modal -->
<div class="modal fade" id="identifyClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Identify Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="identifyClientForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Search and Select Client</label>
                        <select name="client_id" class="form-select" required>
                            <option value="">Choose a client...</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Identify Client</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Ensure functions are in global scope
window.identifyClient = function() {
    // Load clients for selection
    $.get('/admin/users', { role: 'client' })
    .done(function(clients) {
        const select = $('#identifyClientModal select[name="client_id"]');
        select.empty().append('<option value="">Choose a client...</option>');

        clients.forEach(client => {
            select.append(`<option value="${client.id}">${client.name} - ${client.email} - ${client.phone}</option>`);
        });

        $('#identifyClientModal').modal('show');
    });
};

window.markAsHandled = function() {
    if (confirm('Mark this call as handled?')) {
        updateCall({ status: 'handled' });
    }
};

window.toggleFollowUp = function() {
    const required = <?php echo e($call->follow_up_required ? 'false' : 'true'); ?>;
    updateCall({
        follow_up_required: required,
        follow_up_at: required ? new Date().toISOString() : null
    });
};

function updateCall(data) {
    $.ajax({
        url: '<?php echo e(route("admin.calls.update", $call)); ?>',
        method: 'PUT',
        data: {
            ...data,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function() {
            location.reload();
        },
        error: function() {
            alert('Failed to update call');
        }
    });
}

$('#identifyClientForm').on('submit', function(e) {
    e.preventDefault();
    
    const clientId = $(this).find('select[name="client_id"]').val();
    
    if (!clientId) {
        alert('Please select a client');
        return;
    }
    
    $.ajax({
        url: '<?php echo e(route("admin.calls.identify-client", $call)); ?>',
        method: 'POST',
        data: {
            client_id: clientId,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function() {
            $('#identifyClientModal').modal('hide');
            location.reload();
        },
        error: function() {
            alert('Failed to identify client');
        }
    });
});

$('#notesForm').on('submit', function(e) {
    e.preventDefault();
    
    const notes = $(this).find('textarea[name="notes"]').val();
    
    updateCall({ notes: notes });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/calls/show.blade.php ENDPATH**/ ?>