<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\EmailService;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class EmailController extends Controller
{
    /**
     * Display email settings and management page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $emailSettings = SettingsService::getEmailSettings();
        $queueStatus = EmailService::getEmailQueueStatus();

        // Get email statistics for dashboard
        $emailStats = $this->getEmailStatistics();

        return view('admin.email.index', compact('emailSettings', 'queueStatus', 'emailStats'));
    }

    /**
     * Display sent emails only
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function sentEmails(Request $request)
    {
        $query = \App\Models\EmailLog::sent();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('to_email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $sentEmails = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.email.sent', compact('sentEmails'));
    }

    /**
     * Display outgoing email queue and management
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function outgoingEmails(Request $request)
    {
        $query = \App\Models\EmailLog::sent();

        // Apply filters for outgoing emails
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('to_email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%");
            });
        }

        $outgoingEmails = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get email types for filter dropdown
        $emailTypes = \App\Models\EmailLog::sent()
            ->select('type')
            ->distinct()
            ->pluck('type')
            ->filter()
            ->sort()
            ->values();

        return view('admin.email.outgoing', compact('outgoingEmails', 'emailTypes'));
    }

    /**
     * Get email statistics for dashboard
     *
     * @return array
     */
    private function getEmailStatistics(): array
    {
        return [
            'sent_today' => \App\Models\EmailLog::sent()
                ->whereDate('created_at', today())
                ->count(),
            'sent_this_week' => \App\Models\EmailLog::sent()
                ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
            'sent_this_month' => \App\Models\EmailLog::sent()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'failed_today' => \App\Models\EmailLog::sent()
                ->whereIn('status', ['failed', 'bounced'])
                ->whereDate('created_at', today())
                ->count(),
            'pending_queue' => \App\Models\EmailLog::where('status', 'pending')->count(),
            'delivery_rate' => $this->calculateDeliveryRate(),
        ];
    }

    /**
     * Calculate email delivery rate
     *
     * @return float
     */
    private function calculateDeliveryRate(): float
    {
        $totalSent = \App\Models\EmailLog::sent()
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->count();

        if ($totalSent === 0) {
            return 100.0;
        }

        $delivered = \App\Models\EmailLog::sent()
            ->whereIn('status', ['sent', 'delivered'])
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->count();

        return round(($delivered / $totalSent) * 100, 2);
    }

    /**
     * Update email settings
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_driver' => 'required|in:smtp,sendmail,mailgun,ses,postmark,log',
            'mail_host' => 'required_if:mail_driver,smtp|nullable|string',
            'mail_port' => 'required_if:mail_driver,smtp|nullable|integer|min:1|max:65535',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update settings
            $settings = [
                'mail_driver' => $request->mail_driver,
                'mail_host' => $request->mail_host,
                'mail_port' => $request->mail_port,
                'mail_username' => $request->mail_username,
                'mail_encryption' => $request->mail_encryption,
                'mail_from_address' => $request->mail_from_address,
                'mail_from_name' => $request->mail_from_name,
            ];

            // Only update password if provided
            if ($request->filled('mail_password')) {
                $settings['mail_password'] = $request->mail_password;
            }

            foreach ($settings as $key => $value) {
                SettingsService::set($key, $value);
            }

            // Apply settings immediately
            SettingsService::applyEmailSettings();

            return redirect()->back()->with('success', 'Email settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update email settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Test email configuration
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'test_email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid email address.',
            ], 422);
        }

        try {
            $success = EmailService::testEmailConfiguration($request->test_email);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Test email sent successfully! Please check your inbox.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send test email. Please check your email configuration.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Email test failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send custom email
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendCustomEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'to_email' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'send_copy' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $recipients = [$request->to_email];

            // Add admin email if send_copy is checked
            if ($request->send_copy) {
                $adminEmail = SettingsService::getCompanyEmail();
                if ($adminEmail && $adminEmail !== $request->to_email) {
                    $recipients[] = $adminEmail;
                }
            }

            $successCount = 0;
            $errors = [];

            foreach ($recipients as $email) {
                $emailData = [
                    'subject' => $request->subject,
                    'message' => $request->message,
                    'sent_at' => now()->format('Y-m-d H:i:s'),
                ];

                Mail::to($email)->send(new \App\Mail\CustomMail($emailData));
                $successCount++;
            }

            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully to ' . $successCount . ' recipient(s): ' . implode(', ', $recipients),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get email templates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplates()
    {
        $templates = [
            'booking_confirmation' => [
                'name' => 'Booking Confirmation',
                'subject' => 'Booking Confirmation - #{booking_number}',
                'content' => 'Dear {client_name}, your booking #{booking_number} has been confirmed...',
            ],
            'booking_reminder' => [
                'name' => 'Booking Reminder',
                'subject' => 'Booking Reminder - Tomorrow',
                'content' => 'Dear {client_name}, this is a reminder about your booking tomorrow...',
            ],
            'payment_receipt' => [
                'name' => 'Payment Receipt',
                'subject' => 'Payment Receipt - #{booking_number}',
                'content' => 'Dear {client_name}, thank you for your payment...',
            ],
        ];

        return response()->json([
            'success' => true,
            'templates' => $templates,
        ]);
    }



    /**
     * Get queue status for AJAX requests
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQueueStatus()
    {
        try {
            $queueStatus = EmailService::getEmailQueueStatus();

            return response()->json([
                'success' => true,
                'data' => $queueStatus,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get queue status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show email log details
     *
     * @param \App\Models\EmailLog $emailLog
     * @return \Illuminate\Http\JsonResponse
     */
    public function showEmailLog(\App\Models\EmailLog $emailLog)
    {
        try {
            return response()->json([
                'success' => true,
                'email' => [
                    'id' => $emailLog->id,
                    'to_email' => $emailLog->to_email,
                    'to_name' => $emailLog->to_name,
                    'subject' => $emailLog->subject,
                    'content' => $emailLog->content,
                    'type' => $emailLog->type,
                    'status' => $emailLog->status,
                    'error_message' => $emailLog->error_message,
                    'sent_at' => $emailLog->sent_at?->toISOString(),
                    'delivered_at' => $emailLog->delivered_at?->toISOString(),
                    'opened_at' => $emailLog->opened_at?->toISOString(),
                    'clicked_at' => $emailLog->clicked_at?->toISOString(),
                    'campaign_id' => $emailLog->campaign_id,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load email details: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Retry failed email
     *
     * @param \App\Models\EmailLog $emailLog
     * @return \Illuminate\Http\JsonResponse
     */
    public function retryEmail(\App\Models\EmailLog $emailLog)
    {
        try {
            if (!in_array($emailLog->status, ['failed', 'bounced'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only failed or bounced emails can be retried.',
                ], 400);
            }

            // Reset status and queue for retry
            $emailLog->update([
                'status' => 'pending',
                'error_message' => null,
                'retry_count' => ($emailLog->retry_count ?? 0) + 1,
            ]);

            // Queue the email for sending
            \App\Services\EmailQueueService::queueSingleEmail($emailLog);

            return response()->json([
                'success' => true,
                'message' => 'Email queued for retry successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retry email: ' . $e->getMessage(),
            ], 500);
        }
    }
}
