<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'subject',
        'content',
        'variables',
        'is_active',
        'is_system',
        'description',
        'category',
        'preview_data',
    ];

    protected $casts = [
        'variables' => 'array',
        'preview_data' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
    ];

    /**
     * Get the status badge class for display
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->is_active ? 'bg-success' : 'bg-secondary';
    }

    /**
     * Get the type badge class for display
     */
    public function getTypeBadgeClassAttribute(): string
    {
        return match($this->type) {
            'booking' => 'bg-primary',
            'payment' => 'bg-success',
            'notification' => 'bg-info',
            'marketing' => 'bg-warning',
            'system' => 'bg-dark',
            default => 'bg-secondary'
        };
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for system templates
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope for custom templates
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for specific category
     */
    public function scopeOfCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Replace variables in content
     */
    public function renderContent(array $data = []): string
    {
        $content = $this->content;
        
        // Replace variables with actual data
        foreach ($data as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        return $content;
    }

    /**
     * Replace variables in subject
     */
    public function renderSubject(array $data = []): string
    {
        $subject = $this->subject;
        
        // Replace variables with actual data
        foreach ($data as $key => $value) {
            $subject = str_replace('{{' . $key . '}}', $value, $subject);
            $subject = str_replace('{' . $key . '}', $value, $subject);
        }

        return $subject;
    }

    /**
     * Get available variables for this template
     */
    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Get default template data for preview
     */
    public function getPreviewData(): array
    {
        return $this->preview_data ?? [
            'company_name' => 'YNR Cars',
            'client_name' => 'John Doe',
            'booking_number' => 'YNR20240101001',
            'pickup_date' => '2024-01-15 10:00 AM',
            'pickup_address' => '123 Main Street, London',
            'dropoff_address' => 'Heathrow Airport',
            'amount' => '£45.00',
            'driver_name' => 'Mike Johnson',
            'vehicle_name' => 'Mercedes E-Class',
        ];
    }

    /**
     * Create default system templates
     */
    public static function createDefaultTemplates(): void
    {
        $templates = [
            [
                'name' => 'Booking Confirmation',
                'slug' => 'booking_confirmation',
                'type' => 'booking',
                'category' => 'client',
                'subject' => 'Booking Confirmation - #{booking_number}',
                'content' => 'Dear {client_name}, your booking #{booking_number} has been confirmed...',
                'variables' => ['client_name', 'booking_number', 'pickup_date', 'pickup_address', 'dropoff_address', 'amount'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients when a booking is confirmed',
            ],
            [
                'name' => 'Booking Reminder',
                'slug' => 'booking_reminder',
                'type' => 'booking',
                'category' => 'client',
                'subject' => 'Booking Reminder - Tomorrow',
                'content' => 'Dear {client_name}, this is a reminder about your booking tomorrow...',
                'variables' => ['client_name', 'booking_number', 'pickup_date', 'pickup_address'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients as a reminder before their booking',
            ],
            [
                'name' => 'Payment Receipt',
                'slug' => 'payment_receipt',
                'type' => 'payment',
                'category' => 'client',
                'subject' => 'Payment Receipt - #{booking_number}',
                'content' => 'Dear {client_name}, thank you for your payment...',
                'variables' => ['client_name', 'booking_number', 'amount', 'payment_method'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to clients after successful payment',
            ],
            [
                'name' => 'Driver Assignment',
                'slug' => 'driver_assignment',
                'type' => 'booking',
                'category' => 'driver',
                'subject' => 'New Ride Assignment - #{booking_number}',
                'content' => 'Dear {driver_name}, you have been assigned a new ride...',
                'variables' => ['driver_name', 'booking_number', 'pickup_date', 'pickup_address', 'client_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to drivers when assigned to a booking',
            ],
            [
                'name' => 'Welcome Client',
                'slug' => 'welcome_client',
                'type' => 'notification',
                'category' => 'client',
                'subject' => 'Welcome to {company_name}',
                'content' => 'Dear {client_name}, welcome to our transportation service...',
                'variables' => ['client_name', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to new clients upon registration',
            ],
            [
                'name' => 'Welcome Driver',
                'slug' => 'welcome_driver',
                'type' => 'notification',
                'category' => 'driver',
                'subject' => 'Welcome to Our Driver Team',
                'content' => 'Dear {driver_name}, welcome to our driver team...',
                'variables' => ['driver_name', 'company_name'],
                'is_system' => true,
                'is_active' => true,
                'description' => 'Sent to new drivers upon account creation',
            ],
        ];

        foreach ($templates as $template) {
            self::updateOrCreate(
                ['slug' => $template['slug']],
                $template
            );
        }
    }


}
