<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update all users with 'customer' role to 'client' role
        $updatedCount = DB::table('users')
            ->where('role', 'customer')
            ->update(['role' => 'client']);

        // Log the change
        \Log::info("Updated {$updatedCount} users from 'customer' role to 'client' role");

        echo "✅ Updated {$updatedCount} users from 'customer' role to 'client' role\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: This is a destructive operation and should be used carefully
        // We're not reversing this as it would change all clients back to customers
        // which may not be desired in production

        \Log::info("Migration rollback requested but not executed to prevent data loss");
        echo "⚠️  Migration rollback not executed to prevent unintended role changes\n";
    }
};
