<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\User;
use App\Services\EmailService;
use Carbon\Carbon;

class SendAdminRevenueReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:revenue-report {--period=weekly : Period for the report (weekly, monthly, quarterly)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send revenue report to administrators';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $period = $this->option('period');
        $this->info("Generating {$period} revenue report...");

        $revenueData = $this->generateRevenueData($period);
        $periodLabel = ucfirst($period) . ' Revenue Report';

        try {
            EmailService::sendAdminRevenueReport($revenueData, $period, $periodLabel);
            $this->info("✅ {$periodLabel} sent successfully!");

            $this->table(['Metric', 'Value'], [
                ['Period', ucfirst($period)],
                ['Total Revenue', '£' . number_format($revenueData['total_revenue'], 2)],
                ['Total Bookings', $revenueData['total_bookings']],
                ['Completion Rate', number_format($revenueData['completion_rate'], 1) . '%'],
                ['Profit Margin', number_format($revenueData['profit_margin'], 1) . '%'],
                ['Average Rating', number_format($revenueData['avg_rating'], 1) . '/5.0'],
            ]);

        } catch (\Exception $e) {
            $this->error("❌ Failed to send revenue report: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Generate revenue data for the specified period
     */
    private function generateRevenueData(string $period): array
    {
        [$startDate, $endDate, $previousStartDate, $previousEndDate] = $this->getPeriodDates($period);

        // Current period bookings
        $currentBookings = Booking::whereBetween('pickup_date', [$startDate, $endDate])->get();
        $previousBookings = Booking::whereBetween('pickup_date', [$previousStartDate, $previousEndDate])->get();

        // Revenue calculations
        $totalRevenue = $currentBookings->where('status', 'completed')->sum('amount');
        $previousRevenue = $previousBookings->where('status', 'completed')->sum('amount');

        // Commission calculations (assuming 20% commission)
        $commissionRate = 20;
        $driverCommissions = $totalRevenue * ($commissionRate / 100);
        $processingFees = $totalRevenue * 0.03; // 3% processing fees
        $netRevenue = $totalRevenue - $driverCommissions - $processingFees;
        $profitMargin = $totalRevenue > 0 ? ($netRevenue / $totalRevenue) * 100 : 0;

        // Booking statistics
        $totalBookings = $currentBookings->count();
        $completedBookings = $currentBookings->where('status', 'completed')->count();
        $completionRate = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;
        $avgBookingValue = $completedBookings > 0 ? $totalRevenue / $completedBookings : 0;
        $avgRating = $currentBookings->where('status', 'completed')->avg('client_rating') ?? 0;

        // Growth calculations
        $revenueGrowth = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
        $bookingGrowth = $previousBookings->count() > 0 ? (($totalBookings - $previousBookings->count()) / $previousBookings->count()) * 100 : 0;

        // Client growth
        $currentClients = User::where('role', 'client')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        $previousClients = User::where('role', 'client')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();
        $clientGrowth = $previousClients > 0 ? (($currentClients - $previousClients) / $previousClients) * 100 : 0;

        // Top revenue sources (by vehicle type or service type)
        $topRevenueSources = $currentBookings->where('status', 'completed')
            ->groupBy('vehicle.name')
            ->map(function($bookings, $vehicleName) use ($totalRevenue) {
                $revenue = $bookings->sum('amount');
                return [
                    'name' => $vehicleName ?: 'Standard Vehicle',
                    'revenue' => $revenue,
                    'percentage' => $totalRevenue > 0 ? ($revenue / $totalRevenue) * 100 : 0,
                ];
            })
            ->sortByDesc('revenue')
            ->take(5)
            ->values()
            ->toArray();

        // Daily breakdown
        $dailyBreakdown = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dayBookings = $currentBookings->filter(function($booking) use ($currentDate) {
                return Carbon::parse($booking->pickup_date)->isSameDay($currentDate);
            });

            $dayName = $currentDate->format($period === 'weekly' ? 'l' : 'M j');
            $dailyBreakdown[$dayName] = [
                'bookings' => $dayBookings->count(),
                'revenue' => $dayBookings->where('status', 'completed')->sum('amount'),
            ];

            $currentDate->addDay();
        }

        // Generate insights
        $insights = [];
        if ($revenueGrowth > 10) {
            $insights[] = "Strong revenue growth of " . number_format($revenueGrowth, 1) . "% compared to previous period";
        } elseif ($revenueGrowth < -10) {
            $insights[] = "Revenue declined by " . number_format(abs($revenueGrowth), 1) . "% - investigate causes";
        }

        if ($completionRate > 95) {
            $insights[] = "Excellent completion rate of " . number_format($completionRate, 1) . "%";
        } elseif ($completionRate < 80) {
            $insights[] = "Low completion rate of " . number_format($completionRate, 1) . "% - review cancellation reasons";
        }

        if ($avgRating > 4.5) {
            $insights[] = "Outstanding client satisfaction with " . number_format($avgRating, 1) . "/5.0 rating";
        }

        if ($profitMargin > 70) {
            $insights[] = "Healthy profit margin of " . number_format($profitMargin, 1) . "%";
        } elseif ($profitMargin < 50) {
            $insights[] = "Low profit margin of " . number_format($profitMargin, 1) . "% - review cost structure";
        }

        return [
            'total_revenue' => $totalRevenue,
            'gross_revenue' => $totalRevenue,
            'driver_commissions' => $driverCommissions,
            'processing_fees' => $processingFees,
            'profit_margin' => $profitMargin,
            'total_bookings' => $totalBookings,
            'completed_bookings' => $completedBookings,
            'avg_booking_value' => $avgBookingValue,
            'completion_rate' => $completionRate,
            'avg_rating' => $avgRating,
            'growth_comparison' => [
                'revenue_growth' => $revenueGrowth,
                'booking_growth' => $bookingGrowth,
                'client_growth' => $clientGrowth,
            ],
            'top_revenue_sources' => $topRevenueSources,
            'daily_breakdown' => $dailyBreakdown,
            'insights' => $insights,
        ];
    }

    /**
     * Get start and end dates for the specified period
     */
    private function getPeriodDates(string $period): array
    {
        $now = Carbon::now();

        switch ($period) {
            case 'weekly':
                $startDate = $now->copy()->startOfWeek();
                $endDate = $now->copy()->endOfWeek();
                $previousStartDate = $startDate->copy()->subWeek();
                $previousEndDate = $endDate->copy()->subWeek();
                break;

            case 'monthly':
                $startDate = $now->copy()->startOfMonth();
                $endDate = $now->copy()->endOfMonth();
                $previousStartDate = $startDate->copy()->subMonth();
                $previousEndDate = $endDate->copy()->subMonth();
                break;

            case 'quarterly':
                $startDate = $now->copy()->startOfQuarter();
                $endDate = $now->copy()->endOfQuarter();
                $previousStartDate = $startDate->copy()->subQuarter();
                $previousEndDate = $endDate->copy()->subQuarter();
                break;

            default:
                throw new \InvalidArgumentException("Invalid period: {$period}");
        }

        return [$startDate, $endDate, $previousStartDate, $previousEndDate];
    }
}
