<?php $__env->startSection('title', 'Email Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Email Management</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Email Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Email Statistics Dashboard -->
    <?php if(isset($emailStats)): ?>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($emailStats['sent_today'] ?? 0); ?></h4>
                            <p class="mb-0">Sent Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-paper-plane fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($emailStats['sent_this_week'] ?? 0); ?></h4>
                            <p class="mb-0">This Week</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($emailStats['pending_queue'] ?? 0); ?></h4>
                            <p class="mb-0">In Queue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo e($emailStats['delivery_rate'] ?? 100); ?>%</h4>
                            <p class="mb-0">Delivery Rate</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Email Management Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📧 Email Management</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Email Templates -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">📄 Templates</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text small">Create and manage email templates for automated communications.</p>
                                    <div class="d-grid gap-2">
                                        <a href="<?php echo e(route('admin.email.templates.index')); ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-file-alt"></i> Manage Templates
                                        </a>
                                        <a href="<?php echo e(route('admin.email.templates.create')); ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus"></i> New Template
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email Campaigns -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">📢 Campaigns</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text small">Create and send bulk email campaigns to targeted user groups.</p>
                                    <div class="d-grid gap-2">
                                        <a href="<?php echo e(route('admin.email.campaigns.index')); ?>" class="btn btn-success btn-sm">
                                            <i class="fas fa-bullhorn"></i> View Campaigns
                                        </a>
                                        <a href="<?php echo e(route('admin.email.campaigns.create')); ?>" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-plus"></i> New Campaign
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Outgoing Emails -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">📤 Outgoing</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text small">Monitor sent emails, delivery status, and email logs.</p>
                                    <div class="d-grid gap-2">
                                        <a href="<?php echo e(route('admin.email.outgoing')); ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-paper-plane"></i> Email Logs
                                        </a>
                                        <a href="#customEmailSection" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-edit"></i> Send Custom
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Incoming Emails -->
                        <div class="col-md-3 mb-3">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">📨 Incoming</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text small">View contact forms, inquiries, and incoming messages.</p>
                                    <div class="d-grid gap-2">
                                        <a href="<?php echo e(route('admin.email-submissions.incoming')); ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-inbox"></i> View Incoming
                                        </a>
                                        <a href="<?php echo e(route('admin.email-submissions.contact')); ?>" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-envelope"></i> Contact Forms
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Email Settings -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Email Configuration</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.email.update-settings')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mail_driver" class="form-label">Mail Driver</label>
                                <select class="form-select <?php $__errorArgs = ['mail_driver'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="mail_driver" name="mail_driver" required>
                                    <option value="smtp" <?php echo e(($emailSettings['mail_driver'] ?? 'smtp') == 'smtp' ? 'selected' : ''); ?>>SMTP</option>
                                    <option value="sendmail" <?php echo e(($emailSettings['mail_driver'] ?? '') == 'sendmail' ? 'selected' : ''); ?>>Sendmail</option>
                                    <option value="mailgun" <?php echo e(($emailSettings['mail_driver'] ?? '') == 'mailgun' ? 'selected' : ''); ?>>Mailgun</option>
                                    <option value="ses" <?php echo e(($emailSettings['mail_driver'] ?? '') == 'ses' ? 'selected' : ''); ?>>Amazon SES</option>
                                    <option value="postmark" <?php echo e(($emailSettings['mail_driver'] ?? '') == 'postmark' ? 'selected' : ''); ?>>Postmark</option>
                                    <option value="log" <?php echo e(($emailSettings['mail_driver'] ?? '') == 'log' ? 'selected' : ''); ?>>Log (Testing)</option>
                                </select>
                                <?php $__errorArgs = ['mail_driver'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="mail_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['mail_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_host" name="mail_host"
                                       value="<?php echo e(old('mail_host', $emailSettings['mail_host'] ?? '')); ?>"
                                       placeholder="smtp.gmail.com">
                                <?php $__errorArgs = ['mail_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mail_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['mail_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_port" name="mail_port"
                                       value="<?php echo e(old('mail_port', $emailSettings['mail_port'] ?? 587)); ?>"
                                       placeholder="587">
                                <?php $__errorArgs = ['mail_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="mail_encryption" class="form-label">Encryption</label>
                                <select class="form-select <?php $__errorArgs = ['mail_encryption'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="mail_encryption" name="mail_encryption">
                                    <option value="">None</option>
                                    <option value="tls" <?php echo e(($emailSettings['mail_encryption'] ?? 'tls') == 'tls' ? 'selected' : ''); ?>>TLS</option>
                                    <option value="ssl" <?php echo e(($emailSettings['mail_encryption'] ?? '') == 'ssl' ? 'selected' : ''); ?>>SSL</option>
                                </select>
                                <?php $__errorArgs = ['mail_encryption'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mail_username" class="form-label">Username</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['mail_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_username" name="mail_username"
                                       value="<?php echo e(old('mail_username', $emailSettings['mail_username'] ?? '')); ?>"
                                       placeholder="<EMAIL>">
                                <?php $__errorArgs = ['mail_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="mail_password" class="form-label">Password</label>
                                <input type="password" class="form-control <?php $__errorArgs = ['mail_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_password" name="mail_password"
                                       placeholder="Leave blank to keep current password">
                                <?php $__errorArgs = ['mail_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mail_from_address" class="form-label">From Email Address</label>
                                <input type="email" class="form-control <?php $__errorArgs = ['mail_from_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_from_address" name="mail_from_address"
                                       value="<?php echo e(old('mail_from_address', $emailSettings['mail_from_address'] ?? '')); ?>"
                                       placeholder="<EMAIL>" required>
                                <?php $__errorArgs = ['mail_from_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="mail_from_name" class="form-label">From Name</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['mail_from_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="mail_from_name" name="mail_from_name"
                                       value="<?php echo e(old('mail_from_name', $emailSettings['mail_from_name'] ?? '')); ?>"
                                       placeholder="YNR Cars" required>
                                <?php $__errorArgs = ['mail_from_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Email Testing & Tools -->
        <div class="col-lg-4">
            <!-- Test Email -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test Email Configuration</h5>
                </div>
                <div class="card-body">
                    <form id="testEmailForm">
                        <div class="mb-3">
                            <label for="test_email" class="form-label">Test Email Address</label>
                            <input type="email" class="form-control" id="test_email" name="test_email"
                                   placeholder="<EMAIL>" required>
                        </div>
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-paper-plane me-1"></i> Send Test Email
                        </button>
                    </form>
                    <div id="testEmailResult" class="mt-3"></div>
                </div>
            </div>

            <!-- Queue Status -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Email Queue Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Status:</span>
                        <span class="badge bg-<?php echo e($queueStatus['status'] === 'operational' ? 'success' : 'danger'); ?>">
                            <?php echo e(ucfirst($queueStatus['status'])); ?>

                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Pending Jobs:</span>
                        <span class="badge bg-info"><?php echo e($queueStatus['pending_jobs'] ?? 0); ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Failed Jobs:</span>
                        <span class="badge bg-warning"><?php echo e($queueStatus['failed_jobs'] ?? 0); ?></span>
                    </div>
                </div>
            </div>

            <!-- Send Custom Email -->
            <div class="card" id="customEmailSection">
                <div class="card-header">
                    <h5 class="card-title mb-0">Send Custom Email</h5>
                </div>
                <div class="card-body">
                    <form id="customEmailForm">
                        <div class="mb-3">
                            <label for="to_email" class="form-label">To Email</label>
                            <input type="email" class="form-control" id="to_email" name="to_email" required>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_copy" name="send_copy">
                                <label class="form-check-label" for="send_copy">
                                    Send copy to admin email
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-envelope me-1"></i> Send Email
                        </button>
                    </form>
                    <div id="customEmailResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Setup Guide -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📧 Email Setup Guide</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">🔧 Quick Setup Instructions</h6>
                            <ol class="small">
                                <li><strong>Configure Email Settings:</strong> Fill in your SMTP details above</li>
                                <li><strong>Test Configuration:</strong> Use the test email feature to verify settings</li>
                                <li><strong>Enable Queue:</strong> Set <code>QUEUE_CONNECTION=database</code> in .env</li>
                                <li><strong>Run Queue Worker:</strong> <code>php artisan queue:work</code></li>
                                <li><strong>Schedule Reminders:</strong> Add cron job for automated reminders</li>
                            </ol>

                            <h6 class="text-primary mt-3">📧 Popular Email Providers</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Provider</th>
                                            <th>Host</th>
                                            <th>Port</th>
                                            <th>Encryption</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Gmail</td>
                                            <td>smtp.gmail.com</td>
                                            <td>587</td>
                                            <td>TLS</td>
                                        </tr>
                                        <tr>
                                            <td>Outlook</td>
                                            <td>smtp-mail.outlook.com</td>
                                            <td>587</td>
                                            <td>TLS</td>
                                        </tr>
                                        <tr>
                                            <td>Yahoo</td>
                                            <td>smtp.mail.yahoo.com</td>
                                            <td>587</td>
                                            <td>TLS</td>
                                        </tr>
                                        <tr>
                                            <td>SendGrid</td>
                                            <td>smtp.sendgrid.net</td>
                                            <td>587</td>
                                            <td>TLS</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-primary">🚀 Available Commands</h6>
                            <div class="bg-light p-3 rounded">
                                <code class="d-block mb-2"># Initialize email templates</code>
                                <code class="d-block mb-3">php artisan email:init-templates</code>

                                <code class="d-block mb-2"># Process scheduled campaigns</code>
                                <code class="d-block mb-3">php artisan email:process-scheduled</code>

                                <code class="d-block mb-2"># Clean up old email logs</code>
                                <code class="d-block mb-3">php artisan email:cleanup-logs --days=90</code>

                                <code class="d-block mb-2"># Start queue worker</code>
                                <code class="d-block mb-3">php artisan queue:work</code>

                                <code class="d-block mb-2"># Send booking reminders</code>
                                <code class="d-block">php artisan bookings:send-reminders</code>
                            </div>

                            <h6 class="text-primary mt-3">⚡ Automated Email Types</h6>
                            <ul class="small">
                                <li><strong>Booking Confirmation:</strong> Sent when booking is created</li>
                                <li><strong>Payment Receipt:</strong> Sent when payment is processed</li>
                                <li><strong>Contact Form:</strong> Admin notification + client confirmation</li>
                                <li><strong>Corporate Inquiry:</strong> Priority notification + confirmation</li>
                                <li><strong>Booking Reminders:</strong> Daily automated reminders</li>
                            </ul>

                            <div class="alert alert-info small mt-3">
                                <strong>💡 Pro Tip:</strong> For Gmail, use App Passwords instead of your regular password.
                                Enable 2FA and generate an app-specific password in your Google Account settings.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Test Email Form
    $('#testEmailForm').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $result = $('#testEmailResult');

        $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Sending...');
        $result.empty();

        $.ajax({
            url: '<?php echo e(route("admin.email.test")); ?>',
            method: 'POST',
            data: $form.serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $result.html('<div class="alert alert-success">' + response.message + '</div>');
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                $result.html('<div class="alert alert-danger">' + (response.message || 'An error occurred') + '</div>');
            },
            complete: function() {
                $button.prop('disabled', false).html('<i class="fas fa-paper-plane me-1"></i> Send Test Email');
            }
        });
    });

    // Custom Email Form
    $('#customEmailForm').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $result = $('#customEmailResult');

        $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> Sending...');
        $result.empty();

        $.ajax({
            url: '<?php echo e(route("admin.email.send-custom")); ?>',
            method: 'POST',
            data: $form.serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $result.html('<div class="alert alert-success">' + response.message + '</div>');
                $form[0].reset();
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                if (response.errors) {
                    let errorHtml = '<div class="alert alert-danger"><ul class="mb-0">';
                    $.each(response.errors, function(field, messages) {
                        $.each(messages, function(index, message) {
                            errorHtml += '<li>' + message + '</li>';
                        });
                    });
                    errorHtml += '</ul></div>';
                    $result.html(errorHtml);
                } else {
                    $result.html('<div class="alert alert-danger">' + (response.message || 'An error occurred') + '</div>');
                }
            },
            complete: function() {
                $button.prop('disabled', false).html('<i class="fas fa-envelope me-1"></i> Send Email');
            }
        });
    });

    // Show/hide SMTP fields based on mail driver
    $('#mail_driver').on('change', function() {
        const driver = $(this).val();
        const smtpFields = $('#mail_host, #mail_port, #mail_username, #mail_password, #mail_encryption').closest('.col-md-6');

        if (driver === 'smtp') {
            smtpFields.show();
        } else {
            smtpFields.hide();
        }
    }).trigger('change');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/email/index.blade.php ENDPATH**/ ?>