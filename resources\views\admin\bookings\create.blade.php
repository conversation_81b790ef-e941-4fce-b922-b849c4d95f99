@extends('layouts.admin')

@section('title', 'Create New Booking')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus-circle me-2"></i>Create New Booking
        </h1>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.bookings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Bookings
            </a>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alert-container"></div>

    <!-- Main Form -->
    <form id="admin-booking-form" class="needs-validation" novalidate>
        @csrf
        <div class="row">
            <!-- Left Column - Form -->
            <div class="col-lg-8">
                <!-- Client Information Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Client Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Client Type Selection -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Client Type <span class="text-danger">*</span></label>
                                <div class="btn-group w-100" role="group" aria-label="Client Type">
                                    <input type="radio" class="btn-check" name="client_type" id="existing_client" value="existing" checked>
                                    <label class="btn btn-outline-primary" for="existing_client">
                                        <i class="fas fa-users me-1"></i> Existing Client
                                    </label>
                                    <input type="radio" class="btn-check" name="client_type" id="new_client" value="new">
                                    <label class="btn btn-outline-primary" for="new_client">
                                        <i class="fas fa-user-plus me-1"></i> New Client
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Existing Client Selection -->
                        <div id="existing-client-section">
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="existing_client_id" class="form-label">Select Client <span class="text-danger">*</span></label>
                                    <select class="form-select" id="existing_client_id" name="existing_client_id" required>
                                        <option value="">Choose a client...</option>
                                        @foreach($clients as $client)
                                            <option value="{{ $client->id }}"
                                                data-name="{{ $client->name }}"
                                                data-email="{{ $client->email }}"
                                                data-phone="{{ $client->phone ?? '' }}"
                                                data-address="{{ $client->address ?? '' }}">
                                                {{ $client->name }} ({{ $client->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback">Please select a client.</div>
                                </div>
                            </div>

                            <!-- Client Details Display -->
                            <div id="client-details" class="mt-3 p-3 bg-light rounded d-none">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Name:</strong> <span id="client-name-display"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Email:</strong> <span id="client-email-display"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Phone:</strong> <span id="client-phone-display"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Address:</strong> <span id="client-address-display"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- New Client Form -->
                        <div id="new-client-section" class="d-none">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="client_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="client_name" name="client_name" placeholder="Enter client's full name">
                                    <div class="invalid-feedback">Please provide a valid name.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="client_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="client_email" name="client_email"
                                           placeholder="Enter email address (e.g., <EMAIL>)"
                                           pattern="[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*"
                                           title="Please enter a valid email address"
                                           autocomplete="email"
                                           spellcheck="false">
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="client_phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="client_phone" name="client_phone" placeholder="Enter phone number">
                                    <div class="invalid-feedback">Please provide a valid phone number.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="client_address" class="form-label">Address</label>
                                    <input type="text" class="form-control" id="client_address" name="client_address" placeholder="Enter client's address">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Details Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calendar-alt me-2"></i>Booking Details
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Booking Type Selection -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Booking Type <span class="text-danger">*</span></label>
                                <div class="btn-group w-100" role="group" aria-label="Booking Type">
                                    <input type="radio" class="btn-check" name="booking_type" id="one_way" value="one_way" checked>
                                    <label class="btn btn-outline-primary" for="one_way">
                                        <i class="fas fa-arrow-right me-1"></i> One Way
                                    </label>
                                    <input type="radio" class="btn-check" name="booking_type" id="return" value="return">
                                    <label class="btn btn-outline-primary" for="return">
                                        <i class="fas fa-exchange-alt me-1"></i> Return
                                    </label>
                                    <input type="radio" class="btn-check" name="booking_type" id="hourly" value="hourly">
                                    <label class="btn btn-outline-primary" for="hourly">
                                        <i class="fas fa-clock me-1"></i> Hourly
                                    </label>
                                    <input type="radio" class="btn-check" name="booking_type" id="airport_transfer" value="airport_transfer">
                                    <label class="btn btn-outline-primary" for="airport_transfer">
                                        <i class="fas fa-plane me-1"></i> Airport Transfer
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Booking Forms -->
                        <!-- One Way Form -->
                        <div id="one-way-form" class="booking-type-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="pickup_address" class="form-label">Pickup Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control address-autocomplete" id="pickup_address" name="pickup_address" placeholder="Enter pickup address" required>
                                    <input type="hidden" id="pickup_lat" name="pickup_lat">
                                    <input type="hidden" id="pickup_lng" name="pickup_lng">
                                    <div class="invalid-feedback">Please provide a pickup address.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="dropoff_address" class="form-label">Dropoff Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control address-autocomplete" id="dropoff_address" name="dropoff_address" placeholder="Enter dropoff address" required>
                                    <input type="hidden" id="dropoff_lat" name="dropoff_lat">
                                    <input type="hidden" id="dropoff_lng" name="dropoff_lng">
                                    <div class="invalid-feedback">Please provide a dropoff address.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_date" class="form-label">Pickup Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="pickup_date" name="pickup_date" data-required required>
                                    <div class="invalid-feedback">Please select a pickup date.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_time" class="form-label">Pickup Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="pickup_time" name="pickup_time" data-required required>
                                    <div class="invalid-feedback">Please select a pickup time.</div>
                                </div>
                            </div>

                            <!-- Via Stops Section for One Way -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <label class="form-label mb-0">
                                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                            </label>
                                            <div class="text-muted small mt-1">
                                                Add intermediate destinations to your journey
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="addOneWayViaStopBtn">
                                            <i class="fas fa-plus me-1"></i>Add Stop
                                        </button>
                                    </div>
                                    <div id="oneWayViaStopsContainer" class="via-stops-container">
                                        <!-- Via stops will be added here dynamically -->
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Maximum 5 via stops allowed. Each stop incurs an additional charge.
                                        </small>
                                        <small class="text-muted" id="oneWayViaStopCounter">
                                            <span id="currentOneWayViaStops">0</span> / 5 stops
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Flight Information Section for One Way -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="text-primary mb-0">
                                            <i class="fas fa-plane me-2"></i>Flight Information
                                        </h6>
                                        <small class="text-muted">Optional - helps track flight status and adjust pickup times</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_flight_number" class="form-label">
                                                <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                            </label>
                                            <input type="text" class="form-control" id="oneway_flight_number" name="flight_number"
                                                   placeholder="e.g., BA123, EK456" maxlength="20">
                                            <small class="text-muted">Enter flight number for real-time tracking</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_airline" class="form-label">
                                                <i class="fas fa-building me-1"></i>Airline
                                            </label>
                                            <input type="text" class="form-control" id="oneway_airline" name="airline"
                                                   placeholder="e.g., British Airways, Emirates" maxlength="100">
                                            <small class="text-muted">Airline name or code</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_departure_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Departure Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="oneway_departure_time" name="departure_time">
                                            <small class="text-muted">Scheduled departure time</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_arrival_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Arrival Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="oneway_arrival_time" name="arrival_time">
                                            <small class="text-muted">Scheduled arrival time</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_terminal" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                            </label>
                                            <input type="text" class="form-control" id="oneway_terminal" name="terminal"
                                                   placeholder="e.g., Terminal 1, T2" maxlength="50">
                                            <small class="text-muted">Departure/arrival terminal</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="oneway_flight_status" class="form-label">
                                                <i class="fas fa-info-circle me-1"></i>Flight Status
                                            </label>
                                            <select class="form-select" id="oneway_flight_status" name="flight_status">
                                                <option value="">Select Status</option>
                                                <option value="scheduled">Scheduled</option>
                                                <option value="delayed">Delayed</option>
                                                <option value="boarding">Boarding</option>
                                                <option value="departed">Departed</option>
                                                <option value="arrived">Arrived</option>
                                                <option value="cancelled">Cancelled</option>
                                            </select>
                                            <small class="text-muted">Current flight status (if known)</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="oneway_flight_notes" class="form-label">
                                                <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                            </label>
                                            <textarea class="form-control" id="oneway_flight_notes" name="flight_notes" rows="2"
                                                      placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                            <small class="text-muted">Additional flight-related information</small>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Flight Tracking:</strong> Providing flight details allows automatic monitoring of flight status
                                        and adjustment of pickup times for delays. Drivers will be notified of any changes.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Return Form -->
                        <div id="return-form" class="booking-type-form d-none">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="return_pickup_address" class="form-label">Pickup Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control address-autocomplete" id="return_pickup_address" name="return_pickup_address" placeholder="Enter pickup address">
                                    <input type="hidden" id="return_pickup_lat" name="return_pickup_lat">
                                    <input type="hidden" id="return_pickup_lng" name="return_pickup_lng">
                                    <div class="invalid-feedback">Please provide a pickup address.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="return_dropoff_address" class="form-label">Dropoff Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control address-autocomplete" id="return_dropoff_address" name="return_dropoff_address" placeholder="Enter dropoff address">
                                    <input type="hidden" id="return_dropoff_lat" name="return_dropoff_lat">
                                    <input type="hidden" id="return_dropoff_lng" name="return_dropoff_lng">
                                    <div class="invalid-feedback">Please provide a dropoff address.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_date_return" class="form-label">Pickup Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="pickup_date_return" name="pickup_date" placeholder="Select pickup date">
                                    <div class="invalid-feedback">Please select a pickup date.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_time_return" class="form-label">Pickup Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="pickup_time_return" name="pickup_time" placeholder="Select pickup time">
                                    <div class="invalid-feedback">Please select a pickup time.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="return_date" class="form-label">Return Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="return_date" name="return_date" placeholder="Select return date">
                                    <div class="invalid-feedback">Please select a return date.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="return_time" class="form-label">Return Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="return_time" name="return_time" placeholder="Select return time">
                                    <div class="invalid-feedback">Please select a return time.</div>
                                </div>
                            </div>

                            <!-- Via Stops Section for Return -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <label class="form-label mb-0">
                                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                            </label>
                                            <div class="text-muted small mt-1">
                                                Add intermediate destinations for return journey
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="addReturnViaStopBtn">
                                            <i class="fas fa-plus me-1"></i>Add Stop
                                        </button>
                                    </div>
                                    <div id="returnViaStopsContainer" class="via-stops-container">
                                        <!-- Return via stops will be added here dynamically -->
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Maximum 5 via stops allowed for return journey.
                                        </small>
                                        <small class="text-muted" id="returnViaStopCounter">
                                            <span id="currentReturnViaStops">0</span> / 5 stops
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Flight Information Section for Return -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="text-primary mb-0">
                                            <i class="fas fa-plane me-2"></i>Flight Information
                                        </h6>
                                        <small class="text-muted">Optional - helps track flight status and adjust pickup times</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="return_flight_number" class="form-label">
                                                <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                            </label>
                                            <input type="text" class="form-control" id="return_flight_number" name="flight_number"
                                                   placeholder="e.g., BA123, EK456" maxlength="20">
                                            <small class="text-muted">Enter flight number for real-time tracking</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="return_airline" class="form-label">
                                                <i class="fas fa-building me-1"></i>Airline
                                            </label>
                                            <input type="text" class="form-control" id="return_airline" name="airline"
                                                   placeholder="e.g., British Airways, Emirates" maxlength="100">
                                            <small class="text-muted">Airline name or code</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="return_departure_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Departure Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="return_departure_time" name="departure_time">
                                            <small class="text-muted">Scheduled departure time</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="return_arrival_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Arrival Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="return_arrival_time" name="arrival_time">
                                            <small class="text-muted">Scheduled arrival time</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="return_terminal" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                            </label>
                                            <input type="text" class="form-control" id="return_terminal" name="terminal"
                                                   placeholder="e.g., Terminal 1, T2" maxlength="50">
                                            <small class="text-muted">Departure/arrival terminal</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="return_flight_status" class="form-label">
                                                <i class="fas fa-info-circle me-1"></i>Flight Status
                                            </label>
                                            <select class="form-select" id="return_flight_status" name="flight_status">
                                                <option value="">Select Status</option>
                                                <option value="scheduled">Scheduled</option>
                                                <option value="delayed">Delayed</option>
                                                <option value="boarding">Boarding</option>
                                                <option value="departed">Departed</option>
                                                <option value="arrived">Arrived</option>
                                                <option value="cancelled">Cancelled</option>
                                            </select>
                                            <small class="text-muted">Current flight status (if known)</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="return_flight_notes" class="form-label">
                                                <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                            </label>
                                            <textarea class="form-control" id="return_flight_notes" name="flight_notes" rows="2"
                                                      placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                            <small class="text-muted">Additional flight-related information</small>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Flight Tracking:</strong> Providing flight details allows automatic monitoring of flight status
                                        and adjustment of pickup times for delays. Drivers will be notified of any changes.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hourly Form -->
                        <div id="hourly-form" class="booking-type-form d-none">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="hourly_pickup_address" class="form-label">Service Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control address-autocomplete" id="hourly_pickup_address" name="hourly_pickup_address" placeholder="Enter service location">
                                    <input type="hidden" id="hourly_pickup_lat" name="hourly_pickup_lat">
                                    <input type="hidden" id="hourly_pickup_lng" name="hourly_pickup_lng">
                                    <div class="invalid-feedback">Please provide a service location.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="duration_hours" class="form-label">Duration (Hours) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="duration_hours" name="duration_hours"
                                           min="1" max="24" step="0.5" placeholder="Enter duration in hours" data-required>
                                    <small class="form-text text-muted">Enter duration in hours (e.g., 2.5 for 2 hours 30 minutes)</small>
                                    <div class="invalid-feedback">Please enter service duration.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_date_hourly" class="form-label">Service Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="pickup_date_hourly" name="pickup_date" placeholder="Select service date">
                                    <div class="invalid-feedback">Please select a service date.</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="pickup_time_hourly" class="form-label">Service Time <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control" id="pickup_time_hourly" name="pickup_time" placeholder="Select service time">
                                    <div class="invalid-feedback">Please select a service time.</div>
                                </div>
                            </div>

                            <!-- Flight Information Section for Hourly -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="text-primary mb-0">
                                            <i class="fas fa-plane me-2"></i>Flight Information
                                        </h6>
                                        <small class="text-muted">Optional - useful for airport-related hourly services</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_flight_number" class="form-label">
                                                <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                            </label>
                                            <input type="text" class="form-control" id="hourly_flight_number" name="flight_number"
                                                   placeholder="e.g., BA123, EK456" maxlength="20">
                                            <small class="text-muted">Enter flight number for real-time tracking</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_airline" class="form-label">
                                                <i class="fas fa-building me-1"></i>Airline
                                            </label>
                                            <input type="text" class="form-control" id="hourly_airline" name="airline"
                                                   placeholder="e.g., British Airways, Emirates" maxlength="100">
                                            <small class="text-muted">Airline name or code</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_departure_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Departure Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="hourly_departure_time" name="departure_time">
                                            <small class="text-muted">Scheduled departure time</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_arrival_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Arrival Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="hourly_arrival_time" name="arrival_time">
                                            <small class="text-muted">Scheduled arrival time</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_terminal" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                            </label>
                                            <input type="text" class="form-control" id="hourly_terminal" name="terminal"
                                                   placeholder="e.g., Terminal 1, T2" maxlength="50">
                                            <small class="text-muted">Departure/arrival terminal</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="hourly_flight_status" class="form-label">
                                                <i class="fas fa-info-circle me-1"></i>Flight Status
                                            </label>
                                            <select class="form-select" id="hourly_flight_status" name="flight_status">
                                                <option value="">Select Status</option>
                                                <option value="scheduled">Scheduled</option>
                                                <option value="delayed">Delayed</option>
                                                <option value="boarding">Boarding</option>
                                                <option value="departed">Departed</option>
                                                <option value="arrived">Arrived</option>
                                                <option value="cancelled">Cancelled</option>
                                            </select>
                                            <small class="text-muted">Current flight status (if known)</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="hourly_flight_notes" class="form-label">
                                                <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                            </label>
                                            <textarea class="form-control" id="hourly_flight_notes" name="flight_notes" rows="2"
                                                      placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                            <small class="text-muted">Additional flight-related information</small>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Flight Tracking:</strong> Providing flight details allows automatic monitoring of flight status
                                        and adjustment of service times for delays. Drivers will be notified of any changes.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Airport Transfer Form -->
                        <div id="airport-transfer-form" class="booking-type-form d-none">
                            <!-- Airport Direction -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label">Transfer Direction <span class="text-danger">*</span></label>
                                    <div class="btn-group w-100" role="group" aria-label="Airport Direction">
                                        <input type="radio" class="btn-check" name="airport_direction" id="to_airport" value="to_airport" checked>
                                        <label class="btn btn-outline-success" for="to_airport">
                                            <i class="fas fa-plane-departure me-1"></i> To Airport
                                        </label>
                                        <input type="radio" class="btn-check" name="airport_direction" id="from_airport" value="from_airport">
                                        <label class="btn btn-outline-info" for="from_airport">
                                            <i class="fas fa-plane-arrival me-1"></i> From Airport
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Airport Selection -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="airport_id" class="form-label">Select Airport <span class="text-danger">*</span></label>
                                    <select class="form-select" id="airport_id" name="airport_id">
                                        <option value="">Choose an airport...</option>
                                        @foreach($airports as $airport)
                                            <option value="{{ $airport->id }}"
                                                data-name="{{ $airport->name }}"
                                                data-code="{{ $airport->code }}"
                                                data-city="{{ $airport->city }}"
                                                data-lat="{{ $airport->latitude }}"
                                                data-lng="{{ $airport->longitude }}">
                                                {{ $airport->name }} ({{ $airport->code }}) - {{ $airport->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback">Please select an airport.</div>
                                </div>
                            </div>

                            <!-- To Airport Form -->
                            <div id="to-airport-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="airport_pickup_address" class="form-label">Pickup Address <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control address-autocomplete" id="airport_pickup_address" name="airport_pickup_address" placeholder="Enter pickup address">
                                        <input type="hidden" id="airport_pickup_lat" name="airport_pickup_lat">
                                        <input type="hidden" id="airport_pickup_lng" name="airport_pickup_lng">
                                        <div class="invalid-feedback">Please provide a pickup address.</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Dropoff Location</label>
                                        <input type="text" class="form-control" id="airport_dropoff_display" placeholder="Select an airport above" readonly>
                                        <small class="text-muted">Dropoff location will be the selected airport</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="pickup_date_airport" class="form-label">Pickup Date <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="pickup_date_airport" name="pickup_date" placeholder="Select pickup date">
                                        <div class="invalid-feedback">Please select a pickup date.</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="pickup_time_airport" class="form-label">Pickup Time <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control" id="pickup_time_airport" name="pickup_time" placeholder="Select pickup time">
                                        <div class="invalid-feedback">Please select a pickup time.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- From Airport Form -->
                            <div id="from-airport-form" class="d-none">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Pickup Location</label>
                                        <input type="text" class="form-control" id="airport_pickup_display" placeholder="Select an airport above" readonly>
                                        <small class="text-muted">Pickup location will be the selected airport</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="airport_dropoff_address" class="form-label">Dropoff Address <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control address-autocomplete" id="airport_dropoff_address" name="airport_dropoff_address" placeholder="Enter dropoff address">
                                        <input type="hidden" id="airport_dropoff_lat" name="airport_dropoff_lat">
                                        <input type="hidden" id="airport_dropoff_lng" name="airport_dropoff_lng">
                                        <div class="invalid-feedback">Please provide a dropoff address.</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="pickup_date_airport_from" class="form-label">Pickup Date <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="pickup_date_airport_from" name="pickup_date" placeholder="Select pickup date">
                                        <div class="invalid-feedback">Please select a pickup date.</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="pickup_time_airport_from" class="form-label">Pickup Time <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control" id="pickup_time_airport_from" name="pickup_time" placeholder="Select pickup time">
                                        <div class="invalid-feedback">Please select a pickup time.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Via Stops Section for Airport Transfer -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <label class="form-label mb-0">
                                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>Via Stops (Optional)
                                            </label>
                                            <div class="text-muted small mt-1">
                                                Add intermediate destinations for airport transfer
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="addAirportViaStopBtn">
                                            <i class="fas fa-plus me-1"></i>Add Stop
                                        </button>
                                    </div>
                                    <div id="airportViaStopsContainer" class="via-stops-container">
                                        <!-- Airport via stops will be added here dynamically -->
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Maximum 5 via stops allowed for airport transfers.
                                        </small>
                                        <small class="text-muted" id="airportViaStopCounter">
                                            <span id="currentAirportViaStops">0</span> / 5 stops
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Flight Information Section -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="text-primary mb-0">
                                            <i class="fas fa-plane me-2"></i>Flight Information
                                        </h6>
                                        <small class="text-muted">Optional - helps track flight status and adjust pickup times</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="flight_number" class="form-label">
                                                <i class="fas fa-ticket-alt me-1"></i>Flight Number
                                            </label>
                                            <input type="text" class="form-control" id="flight_number" name="flight_number"
                                                   placeholder="e.g., BA123, EK456" maxlength="20">
                                            <small class="text-muted">Enter flight number for real-time tracking</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="airline" class="form-label">
                                                <i class="fas fa-building me-1"></i>Airline
                                            </label>
                                            <input type="text" class="form-control" id="airline" name="airline"
                                                   placeholder="e.g., British Airways, Emirates" maxlength="100">
                                            <small class="text-muted">Airline name or code</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="departure_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Departure Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="departure_time" name="departure_time">
                                            <small class="text-muted">Scheduled departure time</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="arrival_time" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Arrival Time
                                            </label>
                                            <input type="datetime-local" class="form-control" id="arrival_time" name="arrival_time">
                                            <small class="text-muted">Scheduled arrival time</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="terminal" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>Terminal
                                            </label>
                                            <input type="text" class="form-control" id="terminal" name="terminal"
                                                   placeholder="e.g., Terminal 1, T2" maxlength="50">
                                            <small class="text-muted">Departure/arrival terminal</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="flight_status" class="form-label">
                                                <i class="fas fa-info-circle me-1"></i>Flight Status
                                            </label>
                                            <select class="form-select" id="flight_status" name="flight_status">
                                                <option value="">Select Status</option>
                                                <option value="scheduled">Scheduled</option>
                                                <option value="delayed">Delayed</option>
                                                <option value="boarding">Boarding</option>
                                                <option value="departed">Departed</option>
                                                <option value="arrived">Arrived</option>
                                                <option value="cancelled">Cancelled</option>
                                            </select>
                                            <small class="text-muted">Current flight status (if known)</small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="flight_notes" class="form-label">
                                                <i class="fas fa-sticky-note me-1"></i>Flight Notes
                                            </label>
                                            <textarea class="form-control" id="flight_notes" name="flight_notes" rows="2"
                                                      placeholder="Any additional flight information or special requirements..." maxlength="500"></textarea>
                                            <small class="text-muted">Additional flight-related information</small>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Flight Tracking:</strong> Providing flight details allows automatic monitoring of flight status
                                        and adjustment of pickup times for delays. Drivers will be notified of any changes.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Extra Services -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-plus-circle me-2"></i>Extra Services
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body p-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="meet_and_greet" name="meet_and_greet" value="1">
                                                    <label class="form-check-label" for="meet_and_greet">
                                                        <strong><i class="fas fa-handshake me-2 text-info"></i>Meet and Greet</strong>
                                                        <small class="text-muted d-block">Driver will meet you with a name sign</small>
                                                        <span class="badge bg-info text-white mt-1">
                                                            +{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($extraServicesSettings['meet_and_greet']['fee'], 2) }}
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body p-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="child_seat" name="child_seat" value="1">
                                                    <label class="form-check-label" for="child_seat">
                                                        <strong><i class="fas fa-baby me-2 text-warning"></i>Child Seat</strong>
                                                        <small class="text-muted d-block">Safety child seat provided</small>
                                                        <span class="badge bg-warning text-dark mt-1">
                                                            +{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($extraServicesSettings['child_seat']['fee'], 2) }}
                                                        </span>
                                                    </label>
                                                </div>
                                                <div class="mt-2 d-none" id="child-seat-details">
                                                    <select class="form-select form-select-sm" id="child_seat_type" name="child_seat_type">
                                                        <option value="">Select seat type...</option>
                                                        <option value="infant">Infant Seat (0-12 months)</option>
                                                        <option value="toddler">Toddler Seat (1-4 years)</option>
                                                        <option value="booster">Booster Seat (4-8 years)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body p-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="wheelchair_accessible" name="wheelchair_accessible" value="1">
                                                    <label class="form-check-label" for="wheelchair_accessible">
                                                        <strong><i class="fas fa-wheelchair me-2 text-success"></i>Wheelchair Accessible</strong>
                                                        <small class="text-muted d-block">Wheelchair accessible vehicle</small>
                                                        <span class="badge bg-success text-white mt-1">
                                                            +{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2) }}
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light h-100">
                                            <div class="card-body p-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="extra_luggage" name="extra_luggage" value="1">
                                                    <label class="form-check-label" for="extra_luggage">
                                                        <strong><i class="fas fa-suitcase me-2 text-secondary"></i>Extra Luggage</strong>
                                                        <small class="text-muted d-block">Additional luggage space</small>
                                                        <span class="badge bg-secondary text-white mt-1">
                                                            +{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($extraServicesSettings['extra_luggage']['fee'], 2) }}
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Special Instructions -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <label for="notes" class="form-label">Special Instructions</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Enter any special instructions or notes for this booking..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Selection Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-car me-2"></i>Vehicle Selection
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Vehicle Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="vehicle_type_filter" class="form-label">Vehicle Type</label>
                                <select class="form-select" id="vehicle_type_filter">
                                    <option value="">All Types</option>
                                    @foreach($vehicleTypes as $type)
                                        <option value="{{ $type }}">{{ ucfirst($type) }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="capacity_filter" class="form-label">Passenger Capacity</label>
                                <select class="form-select" id="capacity_filter">
                                    <option value="">Any Capacity</option>
                                    @foreach($capacityRanges as $range => $label)
                                        <option value="{{ $range }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="vehicle_search" class="form-label">Search Vehicles</label>
                                <input type="text" class="form-control" id="vehicle_search" placeholder="Search by name or features...">
                            </div>
                        </div>

                        <!-- Vehicle Selection -->
                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">Select Vehicle <span class="text-danger">*</span></label>
                                <div id="vehicle-selection" class="row">
                                    @foreach($vehicles as $vehicle)
                                        <div class="col-md-6 col-lg-4 mb-3 vehicle-option"
                                             data-type="{{ $vehicle->type }}"
                                             data-capacity="{{ $vehicle->seats }}"
                                             data-name="{{ strtolower($vehicle->name) }}"
                                             data-features="{{ strtolower($vehicle->features ?? '') }}">
                                            <div class="card vehicle-card h-100" data-vehicle-id="{{ $vehicle->id }}">
                                                <div class="card-body text-center p-3">
                                                    @if($vehicle->image)
                                                        <img src="{{ asset('storage/' . $vehicle->image) }}"
                                                             alt="{{ $vehicle->name }}"
                                                             class="img-fluid mb-2 rounded"
                                                             style="max-height: 80px; object-fit: cover;">
                                                    @else
                                                        <div class="vehicle-placeholder mb-2 d-flex align-items-center justify-content-center bg-light rounded" style="height: 80px;">
                                                            <i class="fas fa-car fa-2x text-muted"></i>
                                                        </div>
                                                    @endif
                                                    <h6 class="card-title mb-1">{{ $vehicle->name }}</h6>
                                                    <p class="card-text small text-muted mb-2">{{ ucfirst($vehicle->type) }}</p>
                                                    <div class="vehicle-features small mb-2">
                                                        <span class="me-2"><i class="fas fa-user me-1"></i> {{ $vehicle->seats }}</span>
                                                        <span><i class="fas fa-suitcase me-1"></i> {{ $vehicle->luggage_capacity }}</span>
                                                    </div>
                                                    <div class="vehicle-pricing small">
                                                        <div class="text-primary fw-bold">
                                                            Base: {{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($vehicle->base_fare, 2) }}
                                                        </div>
                                                        <div class="text-muted">
                                                            Per ml: {{ \App\Services\SettingsService::getCurrencySymbol() }}{{ number_format($vehicle->price_per_km, 2) }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-footer p-2">
                                                    <div class="form-check text-center">
                                                        <input class="form-check-input" type="radio" name="vehicle_id"
                                                               id="vehicle_{{ $vehicle->id }}" value="{{ $vehicle->id }}" required>
                                                        <label class="form-check-label fw-bold" for="vehicle_{{ $vehicle->id }}">
                                                            Select Vehicle
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="invalid-feedback">Please select a vehicle.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Driver Assignment Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user-tie me-2"></i>Driver Assignment
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Driver Assignment Toggle -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="assign_driver" name="assign_driver" value="1">
                                    <label class="form-check-label" for="assign_driver">
                                        <strong>Assign Driver Now</strong>
                                        <small class="text-muted d-block">You can assign a driver later if needed</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Driver Selection -->
                        <div id="driver-assignment-section" class="d-none">
                            <!-- Driver Filters -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="driver_rating_filter" class="form-label">Minimum Rating</label>
                                    <select class="form-select" id="driver_rating_filter">
                                        <option value="">Any Rating</option>
                                        @foreach($ratingOptions as $rating => $label)
                                            <option value="{{ $rating }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="driver_experience_filter" class="form-label">Experience</label>
                                    <select class="form-select" id="driver_experience_filter">
                                        <option value="">Any Experience</option>
                                        @foreach($experienceOptions as $years => $label)
                                            <option value="{{ $years }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="driver_search" class="form-label">Search Drivers</label>
                                    <input type="text" class="form-control" id="driver_search" placeholder="Search by name...">
                                </div>
                            </div>

                            <!-- Available Drivers -->
                            <div class="row">
                                <div class="col-md-12">
                                    <label class="form-label">Select Driver</label>
                                    <div id="driver-list" class="row">
                                        @foreach($drivers as $driver)
                                            <div class="col-md-6 col-lg-4 mb-3 driver-option"
                                                 data-rating="{{ $driver->rating ?? 0 }}"
                                                 data-experience="{{ $driver->years_experience ?? 0 }}"
                                                 data-name="{{ strtolower($driver->name) }}">
                                                <div class="card driver-card h-100" data-driver-id="{{ $driver->id }}">
                                                    <div class="card-body text-center p-3">
                                                        @if($driver->profile_photo)
                                                            <img src="{{ asset('storage/' . $driver->profile_photo) }}"
                                                                 alt="{{ $driver->name }}"
                                                                 class="rounded-circle mb-2"
                                                                 style="width: 60px; height: 60px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-primary rounded-circle mb-2 mx-auto d-flex align-items-center justify-content-center"
                                                                 style="width: 60px; height: 60px; color: white; font-size: 1.5rem;">
                                                                {{ strtoupper(substr($driver->name, 0, 1)) }}
                                                            </div>
                                                        @endif
                                                        <h6 class="card-title mb-1">{{ $driver->name }}</h6>
                                                        <p class="card-text small text-muted mb-2">{{ $driver->phone }}</p>
                                                        <div class="driver-stats small mb-2">
                                                            @if($driver->rating)
                                                                <div class="text-warning">
                                                                    @for($i = 1; $i <= 5; $i++)
                                                                        <i class="fas fa-star{{ $i <= $driver->rating ? '' : '-o' }}"></i>
                                                                    @endfor
                                                                    <span class="ms-1">{{ number_format($driver->rating, 1) }}</span>
                                                                </div>
                                                            @endif
                                                            @if($driver->years_experience)
                                                                <div class="text-muted">{{ $driver->years_experience }} years exp.</div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="card-footer p-2">
                                                        <div class="form-check text-center">
                                                            <input class="form-check-input" type="radio" name="driver_id"
                                                                   id="driver_{{ $driver->id }}" value="{{ $driver->id }}">
                                                            <label class="form-check-label fw-bold" for="driver_{{ $driver->id }}">
                                                                Select Driver
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Map and Summary -->
            <div class="col-lg-4">
                <!-- Map Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map me-2"></i>Route Map
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div id="admin-booking-map" style="height: 300px; width: 100%;">
                            <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                                <div class="text-center">
                                    <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Enter addresses to view route</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fare Summary Card -->
                <div class="card shadow mb-4" id="fare-summary-card">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>Fare Summary
                        </h6>
                        <div class="fare-status-indicator">
                            <span class="badge bg-secondary" id="fare-status-badge">Pending</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Calculation Status -->
                        <div id="fare-calculation-status" class="fare-status-container">
                            <div class="text-center py-4">
                                <div class="fare-status-icon mb-3">
                                    <i class="fas fa-info-circle fa-3x text-muted"></i>
                                </div>
                                <h6 class="text-muted mb-2">Ready to Calculate</h6>
                                <p class="text-muted small mb-0">Select vehicle and enter addresses to calculate fare</p>
                            </div>
                        </div>

                        <!-- Fare Breakdown -->
                        <div id="fare-breakdown" class="fare-breakdown-container d-none">
                            <!-- Base Charges Section -->
                            <div class="fare-section mb-3">
                                <h6 class="fare-section-title text-dark mb-2">
                                    <i class="fas fa-car me-2 text-primary"></i>Base Charges
                                </h6>
                                <div class="fare-items">
                                    <div class="fare-item d-flex justify-content-between align-items-center py-2">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Base Fare</span>
                                            <small class="text-muted d-block">Starting rate</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-dark" id="base-fare">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2" id="distance-fare-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Distance Charge</span>
                                            <small class="text-muted d-block"><span id="distance-display">0</span> <span id="distance-unit">{{ \App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'mi' : 'km' }}</span></small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-dark" id="distance-fare">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="duration-fare-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Duration Charge</span>
                                            <small class="text-muted d-block"><span id="duration-display">0</span> hours</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-dark" id="duration-fare">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="airport-surcharge-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Airport Surcharge</span>
                                            <small class="text-muted d-block">Additional airport fee</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-warning" id="airport-surcharge">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="booking-fee-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Booking Fee</span>
                                            <small class="text-muted d-block">Service booking fee</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-info" id="booking-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="weekend-surcharge-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Weekend Surcharge</span>
                                            <small class="text-muted d-block">Weekend premium</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-warning" id="weekend-surcharge">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="night-surcharge-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Night Surcharge</span>
                                            <small class="text-muted d-block">Night time premium</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-warning" id="night-surcharge">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="via-charges-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Via Stops</span>
                                            <small class="text-muted d-block"><span id="via-stops-count">0</span> additional stops</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-primary" id="via-charges">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="tax-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Tax/VAT</span>
                                            <small class="text-muted d-block"><span id="tax-rate-display">0</span>% tax</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-secondary" id="tax-amount">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Extra Services Section -->
                            <div class="fare-section mb-3" id="extra-services-section" style="display: none;">
                                <h6 class="fare-section-title text-dark mb-2">
                                    <i class="fas fa-plus-circle me-2 text-success"></i>Extra Services
                                </h6>
                                <div class="fare-items">
                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="meet-and-greet-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Meet and Greet</span>
                                            <small class="text-muted d-block">Driver with name sign</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-info" id="meet-and-greet-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="child-seat-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Child Seat</span>
                                            <small class="text-muted d-block">Safety child seat</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-warning" id="child-seat-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="wheelchair-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Wheelchair Accessible</span>
                                            <small class="text-muted d-block">Accessible vehicle</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-success" id="wheelchair-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>

                                    <div class="fare-item d-flex justify-content-between align-items-center py-2 d-none" id="extra-luggage-item">
                                        <div class="fare-item-label">
                                            <span class="fw-medium">Extra Luggage</span>
                                            <small class="text-muted d-block">Additional luggage space</small>
                                        </div>
                                        <div class="fare-item-value">
                                            <span class="fw-bold text-secondary" id="extra-luggage-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Section -->
                            <div class="fare-total-section">
                                <div class="fare-total-divider mb-3"></div>
                                <div class="fare-total-container bg-light rounded p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="fare-total-label">
                                            <h5 class="mb-1 text-dark">Total Amount</h5>
                                            <small class="text-muted">All charges included</small>
                                        </div>
                                        <div class="fare-total-value">
                                            <h4 class="mb-0 text-primary fw-bold" id="total-fare">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Fare Details -->
                            <div class="fare-details mt-3">
                                <div class="text-center">
                                    <button type="button" class="btn btn-link btn-sm text-muted" id="fare-details-toggle">
                                        <i class="fas fa-info-circle me-1"></i>View Calculation Details
                                    </button>
                                </div>
                                <div class="fare-calculation-details collapse" id="fare-calculation-details">
                                    <div class="card border-0 bg-light mt-2">
                                        <div class="card-body p-3">
                                            <h6 class="card-title text-dark mb-2">Calculation Breakdown</h6>
                                            <div class="row text-sm">
                                                <div class="col-6">
                                                    <small class="text-muted">Base Rate:</small><br>
                                                    <span id="calc-base-rate">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ $baseFare }}</span>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">Per {{ \App\Services\SettingsService::getDistanceUnit() === 'miles' ? 'Mile' : 'KM' }}:</small><br>
                                                    <span id="calc-per-km">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ $pricePerKm }}</span>
                                                </div>
                                                <div class="col-6 mt-2" id="calc-hourly-section" style="display: none;">
                                                    <small class="text-muted">Hourly Rate:</small><br>
                                                    <span id="calc-hourly-rate">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ $hourlyRate }}</span>
                                                </div>
                                                <div class="col-6 mt-2" id="calc-airport-section" style="display: none;">
                                                    <small class="text-muted">Airport Fee:</small><br>
                                                    <span id="calc-airport-fee">{{ \App\Services\SettingsService::getCurrencySymbol() }}{{ $airportSurcharge }}</span>
                                                </div>
                                                <div class="col-6 mt-2" id="calc-via-section" style="display: none;">
                                                    <small class="text-muted">Via Stops:</small><br>
                                                    <span id="calc-via-charges">{{ \App\Services\SettingsService::getCurrencySymbol() }}0.00</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden Fields -->
                        <input type="hidden" id="amount" name="amount" required>
                        <input type="hidden" id="distance_value" name="distance_value">
                        <input type="hidden" id="duration_value" name="duration_value">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card shadow">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg" id="create-booking-btn">
                                <i class="fas fa-plus-circle me-2"></i>Create Booking
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="reset-form-btn">
                                <i class="fas fa-undo me-2"></i>Reset Form
                            </button>
                        </div>

                        <!-- Enhanced Loading State -->
                        <div id="form-loading" class="text-center mt-3 d-none">
                            <div class="loading-container">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Processing...</span>
                                </div>
                                <div class="loading-progress">
                                    <div class="progress mb-2">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" id="loading-progress-bar"></div>
                                    </div>
                                    <p class="text-muted mb-0" id="loading-message">Preparing booking...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Form Progress Indicator -->
                        <div id="form-progress" class="mt-3">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="form-progress-bar"></div>
                            </div>
                            <small class="text-muted mt-1 d-block" id="form-progress-text">Form completion: 0%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('styles')
<style>
/* Vehicle and Driver Cards */
.vehicle-card, .driver-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.vehicle-card:hover, .driver-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.vehicle-card.selected, .driver-card.selected {
    border-color: #4e73df;
    background-color: #f8f9fc;
}

.vehicle-card.selected .card-footer,
.driver-card.selected .card-footer {
    background-color: #4e73df;
    color: white;
}

/* Booking Type Forms */
.booking-type-form {
    transition: all 0.3s ease;
}

/* Address Autocomplete */
.address-autocomplete {
    position: relative;
}

/* Via Stops Styling */
.via-stop-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.via-stop-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.via-stop-item .input-group-text {
    background: #4e73df;
    color: white;
    border: none;
    font-weight: 500;
}

.via-stop-item .form-control {
    border-left: none;
    padding-left: 15px;
}

.via-stop-item .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.via-stop-item .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    transition: all 0.3s ease;
}

.via-stop-item .btn-outline-danger:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: scale(1.05);
}

.via-stop-number {
    position: absolute;
    top: -8px;
    left: 15px;
    background: #4e73df;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.via-stops-container:empty::before {
    content: "No via stops added yet. Click 'Add Stop' to add intermediate destinations.";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 20px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #fafafa;
}

/* Animation for adding/removing via stops */
.via-stop-item {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.via-stop-removing {
    animation: slideOutDown 0.3s ease-in forwards;
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Map Styles */
#admin-booking-map {
    border-radius: 0.35rem;
}

/* Fare Summary */
#fare-summary-card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.fare-status-container {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fare-status-icon {
    transition: all 0.3s ease;
}

.fare-breakdown-container {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fare-section {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 1rem;
}

.fare-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.fare-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
}

.fare-items {
    background: #f8f9fc;
    border-radius: 0.5rem;
    padding: 0.5rem;
}

.fare-item {
    padding: 0.75rem;
    margin-bottom: 0.25rem;
    background: white;
    border-radius: 0.35rem;
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease;
}

.fare-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.fare-item:last-child {
    margin-bottom: 0;
}

.fare-item-label {
    flex: 1;
}

.fare-item-label span {
    font-size: 0.95rem;
}

.fare-item-label small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.fare-item-value {
    text-align: right;
}

.fare-item-value span {
    font-size: 1rem;
}

.fare-total-divider {
    height: 2px;
    background: linear-gradient(90deg, #4e73df, #224abe);
    border-radius: 1px;
}

.fare-total-container {
    border: 2px solid #4e73df;
    background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.1);
}

.fare-total-label h5 {
    font-weight: 700;
    color: #2c3e50;
}

.fare-total-value h4 {
    font-size: 1.8rem;
    font-weight: 800;
    text-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fare-details {
    border-top: 1px solid #e3e6f0;
    padding-top: 1rem;
}

.fare-calculation-details .card {
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.fare-status-indicator .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 1rem;
}

/* Status Badge Colors */
.badge.bg-secondary { background-color: #6c757d !important; }
.badge.bg-warning { background-color: #f6c23e !important; color: #1a1a1a; }
.badge.bg-success { background-color: #1cc88a !important; }
.badge.bg-info { background-color: #36b9cc !important; }

/* Extra Services Section */
#extra-services-section {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; height: 0; }
    to { opacity: 1; height: auto; }
}

/* Loading States */
.fare-calculating {
    position: relative;
    overflow: hidden;
}

.fare-calculating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(78, 115, 223, 0.1), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Form Validation */
.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: #1cc88a;
}

.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: #e74a3b;
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Client Details Display */
#client-details {
    border-left: 4px solid #4e73df;
}

/* Airport Direction Buttons */
.btn-check:checked + .btn-outline-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-check:checked + .btn-outline-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

/* Vehicle and Driver Filters */
.vehicle-option, .driver-option {
    transition: all 0.3s ease;
}

.vehicle-option.d-none, .driver-option.d-none {
    display: none !important;
}

/* Alert Styles */
.alert {
    border-radius: 0.35rem;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .vehicle-option, .driver-option {
        margin-bottom: 1rem;
    }

    #admin-booking-map {
        height: 250px !important;
    }
}
</style>
@endsection

@section('scripts')
<!-- Google Maps API -->
@if($googleMapsApiKey)
<script>
// Global variables for Google Maps and Settings
window.googleMapsApiKey = '{{ $googleMapsApiKey }}';
window.countryCode = '{{ $countryCode }}';

// Google Maps autocomplete settings
window.autocompleteSettings = {
    enabled: {{ $googleMapsApiKey ? 'true' : 'false' }},
    restrictCountry: {{ \App\Services\SettingsService::get('google_maps_restrict_country', 'true') === 'true' ? 'true' : 'false' }},
    country: '{{ \App\Services\SettingsService::get('google_maps_country_code', $countryCode) }}',
    types: "{{ $autocompleteSettings['types'] }}",
    fields: "{{ $autocompleteSettings['fields'] }}"
};

// Global variables for map functionality
let adminBookingMap = null;
let directionsService = null;
let directionsRenderer = null;
window.businessSettings = {
    name: '{{ $businessName }}',
    phone: '{{ $businessPhone }}',
    email: '{{ $businessEmail }}',
    address: '{{ $businessAddress }}'
};
window.bookingSettings = {
    maxAdvanceBookingDays: {{ $maxAdvanceBookingDays }},
    minAdvanceBookingHours: {{ $minAdvanceBookingHours }},
    defaultStatus: '{{ $defaultBookingStatus }}',
    autoAssignDriver: {{ $autoAssignDriver ? 'true' : 'false' }}
};
window.pricingSettings = {
    baseFare: {{ $baseFare }},
    pricePerKm: {{ $pricePerKm }},
    hourlyRate: {{ $hourlyRate }},
    airportSurcharge: {{ $airportSurcharge }},
    currencySymbol: '{{ $currencySymbol }}',
    currencyCode: '{{ $currencyCode }}',
    taxRate: {{ $taxRate }},
    serviceFee: {{ $serviceFee }},
    meetAndGreetFee: {{ $extraServicesSettings['meet_and_greet']['fee'] }},
    childSeatFee: {{ $extraServicesSettings['child_seat']['fee'] }},
    wheelchairFee: {{ $extraServicesSettings['wheelchair_accessible']['fee'] }},
    extraLuggageFee: {{ $extraServicesSettings['extra_luggage']['fee'] }},
    distanceUnit: '{{ \App\Services\SettingsService::getDistanceUnit() }}',
    weekendSurcharge: {{ \App\Services\SettingsService::get('weekend_surcharge', '5.00') }},
    nightSurcharge: {{ \App\Services\SettingsService::get('night_surcharge', '10.00') }}
};

// Pass extra services settings
window.extraServicesSettings = {
    meet_and_greet: {
        enabled: {{ $extraServicesSettings['meet_and_greet']['enabled'] ? 'true' : 'false' }},
        fee: {{ $extraServicesSettings['meet_and_greet']['fee'] }},
        label: "{{ $extraServicesSettings['meet_and_greet']['label'] }}",
        description: "{{ $extraServicesSettings['meet_and_greet']['description'] }}"
    },
    child_seat: {
        enabled: {{ $extraServicesSettings['child_seat']['enabled'] ? 'true' : 'false' }},
        fee: {{ $extraServicesSettings['child_seat']['fee'] }},
        label: "{{ $extraServicesSettings['child_seat']['label'] }}",
        description: "{{ $extraServicesSettings['child_seat']['description'] }}"
    },
    wheelchair_accessible: {
        enabled: {{ $extraServicesSettings['wheelchair_accessible']['enabled'] ? 'true' : 'false' }},
        fee: {{ $extraServicesSettings['wheelchair_accessible']['fee'] }},
        label: "{{ $extraServicesSettings['wheelchair_accessible']['label'] }}",
        description: "{{ $extraServicesSettings['wheelchair_accessible']['description'] }}"
    },
    extra_luggage: {
        enabled: {{ $extraServicesSettings['extra_luggage']['enabled'] ? 'true' : 'false' }},
        fee: {{ $extraServicesSettings['extra_luggage']['fee'] }},
        label: "{{ $extraServicesSettings['extra_luggage']['label'] }}",
        description: "{{ $extraServicesSettings['extra_luggage']['description'] }}"
    }
};

// Initialize Google Maps API
function initGoogleMapsApi() {
    window.dispatchEvent(new Event('google-maps-loaded'));
}
</script>
<script src="https://maps.googleapis.com/maps/api/js?key={{ $googleMapsApiKey }}&libraries=places&callback=initGoogleMapsApi" async defer></script>
@else
<script>
    console.error('Google Maps API key is not configured. Please set it in the admin settings.');
    showAlert('Google Maps API key is not configured. Please configure it in the admin settings to enable map features.', 'warning');
</script>
@endif

<script>
// Additional global variables - consolidated
let autocompleteInstances = {};
let currentBookingType = 'one_way';
let baseFareAmount = 0;

// ============================================================================
// MAIN INITIALIZATION
// ============================================================================

document.addEventListener('DOMContentLoaded', function() {
    // Initialize when Google Maps API is loaded
    window.addEventListener('google-maps-loaded', initializeAdminBooking);

    // If Google Maps is already loaded, initialize immediately
    if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
        initializeAdminBooking();
    }

    // Apply auto driver assignment setting
    applyAutoDriverAssignmentSetting();
});

function initializeAdminBooking() {
    console.log('Initializing admin booking form...');

    // Initialize Google Maps
    initializeMap();

    // Initialize address autocomplete
    initializeAutocomplete();

    // Initialize form handlers
    initializeFormHandlers();

    // Initialize AJAX fare calculation
    initializeAutoFareCalculation();

    // Initialize extra services
    initializeExtraServices();

    // Initialize distance unit display
    initializeDistanceUnitDisplay();

    // Initialize form progress tracking
    initializeFormProgress();

    // Initialize auto-save functionality
    initializeAutoSave();
}

// ============================================================================
// GOOGLE MAPS INITIALIZATION
// ============================================================================

function initializeMap() {
    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
        console.error('Google Maps API not loaded');
        return;
    }

    // Initialize map
    adminBookingMap = new google.maps.Map(document.getElementById('admin-booking-map'), {
        zoom: {{ $mapDefaultZoom }},
        center: { lat: {{ $mapDefaultLat }}, lng: {{ $mapDefaultLng }} },
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
            {
                "featureType": "poi",
                "stylers": [
                    { "visibility": "off" }
                ]
            }
        ]
    });

    // Initialize directions service
    directionsService = new google.maps.DirectionsService();
    directionsRenderer = new google.maps.DirectionsRenderer({
        map: adminBookingMap,
        suppressMarkers: false,
        polylineOptions: {
            strokeColor: '#4e73df',
            strokeWeight: 5,
            strokeOpacity: 0.7
        }
    });
    directionsRenderer.setMap(adminBookingMap);

    console.log('Admin booking map initialized');
}

function initializeDistanceUnitDisplay() {
    // Get distance unit from settings
    const distanceUnit = window.pricingSettings?.distanceUnit || 'km';

    // Convert to proper unit label
    const unitLabel = distanceUnit === 'miles' ? 'mi' : 'km';

    // Update distance unit display in fare breakdown
    const distanceUnitElement = document.getElementById('distance-unit');
    if (distanceUnitElement) {
        distanceUnitElement.textContent = unitLabel;
    }

    console.log('Distance unit initialized:', distanceUnit, 'Display label:', unitLabel);
}

function calculateTimeBasedSurcharges(pickupDate, pickupTime) {
    const surcharges = {
        weekend_surcharge: 0,
        night_surcharge: 0
    };

    if (!pickupDate || !pickupTime) {
        console.log('No pickup date/time provided for surcharge calculation');
        return surcharges;
    }

    try {
        // Create date object from pickup date and time
        const pickupDateTime = new Date(`${pickupDate}T${pickupTime}`);

        if (isNaN(pickupDateTime.getTime())) {
            console.error('Invalid pickup date/time:', pickupDate, pickupTime);
            return surcharges;
        }

        // Check for weekend (Saturday = 6, Sunday = 0)
        const dayOfWeek = pickupDateTime.getDay();
        const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);

        if (isWeekend) {
            surcharges.weekend_surcharge = parseFloat(window.pricingSettings?.weekendSurcharge) || 0;
            console.log('Weekend detected, surcharge:', surcharges.weekend_surcharge);
        }

        // Check for night time (10 PM - 6 AM)
        const hour = pickupDateTime.getHours();
        const isNightTime = (hour >= 22 || hour < 6);

        if (isNightTime) {
            surcharges.night_surcharge = parseFloat(window.pricingSettings?.nightSurcharge) || 0;
            console.log('Night time detected, surcharge:', surcharges.night_surcharge);
        }

        console.log('Time-based surcharges calculated:', {
            date: pickupDate,
            time: pickupTime,
            dayOfWeek: dayOfWeek,
            hour: hour,
            isWeekend: isWeekend,
            isNightTime: isNightTime,
            surcharges: surcharges
        });

    } catch (error) {
        console.error('Error calculating time-based surcharges:', error);
    }

    return surcharges;
}

function initializeAutocomplete() {
    // Address input configuration
    const addressInputs = [
        'pickup_address', 'dropoff_address', 'return_pickup_address',
        'return_dropoff_address', 'hourly_pickup_address',
        'airport_pickup_address', 'airport_dropoff_address'
    ];

    // Build autocomplete options once
    const autocompleteOptions = {
        fields: window.autocompleteSettings?.fields?.split(',') ||
                ['address_components', 'geometry', 'name', 'formatted_address']
    };

    // Add country restriction if enabled
    if (window.autocompleteSettings?.restrictCountry && window.autocompleteSettings?.country) {
        autocompleteOptions.componentRestrictions = { country: window.autocompleteSettings.country };
    }

    // Add types if specified
    if (window.autocompleteSettings?.types) {
        autocompleteOptions.types = [window.autocompleteSettings.types];
    }

    // Initialize autocomplete for each input
    addressInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (!input) return;

        const autocomplete = new google.maps.places.Autocomplete(input, autocompleteOptions);

        autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (!place.geometry) return;

            // Store coordinates in hidden fields
            const latField = document.getElementById(inputId.replace('_address', '_lat'));
            const lngField = document.getElementById(inputId.replace('_address', '_lng'));

            if (latField) latField.value = place.geometry.location.lat();
            if (lngField) lngField.value = place.geometry.location.lng();

            // Update map and calculate fare with debouncing
            setTimeout(updateMapAndFare, 500);
        });

        autocompleteInstances[inputId] = autocomplete;
    });
}

// ============================================================================
// FORM HANDLERS AND EVENT LISTENERS
// ============================================================================

function initializeFormHandlers() {
    // Client type toggle
    document.querySelectorAll('input[name="client_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            toggleClientSections(this.value);
        });
    });

    // Existing client selection
    const existingClientSelect = document.getElementById('existing_client_id');
    if (existingClientSelect) {
        existingClientSelect.addEventListener('change', function() {
            displayClientDetails(this);
        });
    }

    // Booking type toggle
    document.querySelectorAll('input[name="booking_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            currentBookingType = this.value;
            toggleBookingTypeForms(this.value);
            updateMapAndFare();
        });
    });

    // Airport direction toggle
    document.querySelectorAll('input[name="airport_direction"]').forEach(radio => {
        radio.addEventListener('change', function() {
            toggleAirportForms(this.value);
            updateMapAndFare();
        });
    });

    // Airport selection
    const airportSelect = document.getElementById('airport_id');
    if (airportSelect) {
        airportSelect.addEventListener('change', function() {
            updateAirportDisplays(this);
            updateMapAndFare();
        });
    }

    // Vehicle selection
    document.querySelectorAll('.vehicle-card').forEach(card => {
        card.addEventListener('click', function() {
            selectVehicle(this);
        });
    });

    // Driver assignment toggle
    const assignDriverCheckbox = document.getElementById('assign_driver');
    if (assignDriverCheckbox) {
        assignDriverCheckbox.addEventListener('change', function() {
            toggleDriverSelection(this.checked);
        });
    }

    // Driver selection
    document.querySelectorAll('.driver-card').forEach(card => {
        card.addEventListener('click', function() {
            selectDriver(this);
        });
    });

    // Vehicle and driver filters - consolidated
    ['vehicle_type_filter', 'capacity_filter'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', filterVehicles);
            console.log(`Added filter listener for ${id}`);
        } else {
            console.warn(`Element ${id} not found`);
        }
    });

    const vehicleSearchElement = document.getElementById('vehicle_search');
    if (vehicleSearchElement) {
        vehicleSearchElement.addEventListener('input', filterVehicles);
        console.log('Added search listener for vehicle_search');
    } else {
        console.warn('Element vehicle_search not found');
    }

    // Debug: Log available vehicle types
    const vehicleTypeFilter = document.getElementById('vehicle_type_filter');
    if (vehicleTypeFilter) {
        console.log('Available vehicle types:', Array.from(vehicleTypeFilter.options).map(opt => opt.value).filter(v => v));
    }

    // Debug: Log available vehicles
    const vehicleOptions = document.querySelectorAll('.vehicle-option');
    console.log(`Found ${vehicleOptions.length} vehicle options`);
    vehicleOptions.forEach((option, index) => {
        console.log(`Vehicle ${index + 1}: type=${option.dataset.type}, name=${option.dataset.name}`);
    });

    ['driver_rating_filter', 'driver_experience_filter'].forEach(id => {
        document.getElementById(id)?.addEventListener('change', filterDrivers);
    });
    document.getElementById('driver_search')?.addEventListener('input', filterDrivers);

    // Form submission
    document.getElementById('admin-booking-form').addEventListener('submit', handleFormSubmission);

    // Reset form
    document.getElementById('reset-form-btn').addEventListener('click', resetForm);

    // Fare details toggle
    const fareDetailsToggle = document.getElementById('fare-details-toggle');
    if (fareDetailsToggle) {
        fareDetailsToggle.addEventListener('click', function() {
            const fareDetails = document.getElementById('fare-calculation-details');
            if (fareDetails) {
                if (fareDetails.classList.contains('show')) {
                    fareDetails.classList.remove('show');
                    this.innerHTML = '<i class="fas fa-info-circle me-1"></i>View Calculation Details';
                } else {
                    fareDetails.classList.add('show');
                    this.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Hide Calculation Details';
                }
            }
        });
    }

    // Set minimum date to today
    setMinimumDates();

    // Duration input for hourly bookings
    const durationInput = document.getElementById('duration_hours');
    if (durationInput) {
        durationInput.addEventListener('input', function() {
            // Trigger fare calculation if vehicle is selected
            const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
            if (selectedVehicle && this.value) {
                calculateFareAjax();
            }
        });
    }

    // Date and time inputs for surcharge calculation
    const dateTimeInputs = [
        'pickup_date', 'pickup_time',
        'return_pickup_date', 'return_pickup_time',
        'hourly_pickup_date', 'hourly_pickup_time',
        'airport_pickup_date', 'airport_pickup_time'
    ];

    dateTimeInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                // Trigger fare calculation if vehicle is selected
                const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
                if (selectedVehicle) {
                    console.log('Date/time changed, recalculating fare with surcharges');
                    calculateFareAjax();
                }
            });
        }
    });

    // Email validation for new client
    const clientEmailInput = document.getElementById('client_email');
    if (clientEmailInput) {
        clientEmailInput.addEventListener('input', function() {
            validateEmailField(this);
        });

        clientEmailInput.addEventListener('blur', function() {
            validateEmailField(this);
        });
    }

    // Extra services event listeners (handled in initializeExtraServices)
    // Note: Extra services are now initialized in initializeExtraServices() function
}

function toggleClientSections(clientType) {
    const existingSection = document.getElementById('existing-client-section');
    const newSection = document.getElementById('new-client-section');
    const clientDetails = document.getElementById('client-details');

    if (clientType === 'existing') {
        existingSection.classList.remove('d-none');
        newSection.classList.add('d-none');

        // Clear new client form
        newSection.querySelectorAll('input').forEach(input => {
            input.value = '';
            input.removeAttribute('required');
        });

        // Add required to existing client
        document.getElementById('existing_client_id').setAttribute('required', 'required');
    } else {
        existingSection.classList.add('d-none');
        newSection.classList.remove('d-none');
        clientDetails.classList.add('d-none');

        // Clear existing client selection
        document.getElementById('existing_client_id').value = '';
        document.getElementById('existing_client_id').removeAttribute('required');

        // Add required to new client fields
        document.getElementById('client_name').setAttribute('required', 'required');
        document.getElementById('client_email').setAttribute('required', 'required');
        document.getElementById('client_phone').setAttribute('required', 'required');
    }
}

function displayClientDetails(selectElement) {
    const clientDetails = document.getElementById('client-details');
    const selectedOption = selectElement.selectedOptions[0];

    if (selectedOption && selectedOption.value) {
        document.getElementById('client-name-display').textContent = selectedOption.dataset.name || 'N/A';
        document.getElementById('client-email-display').textContent = selectedOption.dataset.email || 'N/A';
        document.getElementById('client-phone-display').textContent = selectedOption.dataset.phone || 'N/A';
        document.getElementById('client-address-display').textContent = selectedOption.dataset.address || 'N/A';

        clientDetails.classList.remove('d-none');
    } else {
        clientDetails.classList.add('d-none');
    }
}

function toggleBookingTypeForms(bookingType) {
    // Hide all forms
    document.querySelectorAll('.booking-type-form').forEach(form => {
        form.classList.add('d-none');
        // Remove required attributes
        form.querySelectorAll('input[required], select[required]').forEach(input => {
            input.removeAttribute('required');
        });
    });

    // Show selected form and add required attributes
    let activeForm;
    switch(bookingType) {
        case 'one_way':
            activeForm = document.getElementById('one-way-form');
            break;
        case 'return':
            activeForm = document.getElementById('return-form');
            break;
        case 'hourly':
            activeForm = document.getElementById('hourly-form');
            break;
        case 'airport_transfer':
            activeForm = document.getElementById('airport-transfer-form');
            break;
    }

    if (activeForm) {
        activeForm.classList.remove('d-none');
        // Add required attributes to visible inputs
        activeForm.querySelectorAll('input[data-required], select[data-required]').forEach(input => {
            input.setAttribute('required', 'required');
        });
    }
}

function toggleAirportForms(direction) {
    const toAirportForm = document.getElementById('to-airport-form');
    const fromAirportForm = document.getElementById('from-airport-form');

    if (direction === 'to_airport') {
        toAirportForm.classList.remove('d-none');
        fromAirportForm.classList.add('d-none');

        // Add required to to-airport fields
        toAirportForm.querySelectorAll('input[type="text"], input[type="date"], input[type="time"]').forEach(input => {
            if (input.id !== 'airport_dropoff_display') {
                input.setAttribute('required', 'required');
            }
        });

        // Remove required from from-airport fields
        fromAirportForm.querySelectorAll('input').forEach(input => {
            input.removeAttribute('required');
        });
    } else {
        toAirportForm.classList.add('d-none');
        fromAirportForm.classList.remove('d-none');

        // Add required to from-airport fields
        fromAirportForm.querySelectorAll('input[type="text"], input[type="date"], input[type="time"]').forEach(input => {
            if (input.id !== 'airport_pickup_display') {
                input.setAttribute('required', 'required');
            }
        });

        // Remove required from to-airport fields
        toAirportForm.querySelectorAll('input').forEach(input => {
            input.removeAttribute('required');
        });
    }
}

function updateAirportDisplays(selectElement) {
    const selectedOption = selectElement.selectedOptions[0];
    const airportPickupDisplay = document.getElementById('airport_pickup_display');
    const airportDropoffDisplay = document.getElementById('airport_dropoff_display');

    if (selectedOption && selectedOption.value) {
        const airportName = selectedOption.dataset.name;
        const airportCode = selectedOption.dataset.code;
        const displayText = `${airportName} (${airportCode})`;

        if (airportPickupDisplay) airportPickupDisplay.value = displayText;
        if (airportDropoffDisplay) airportDropoffDisplay.value = displayText;
    } else {
        if (airportPickupDisplay) airportPickupDisplay.value = '';
        if (airportDropoffDisplay) airportDropoffDisplay.value = '';
    }
}

function selectVehicle(cardElement) {
    // Remove selection from all vehicle cards
    document.querySelectorAll('.vehicle-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selection to clicked card
    cardElement.classList.add('selected');

    // Check the radio button
    const radioButton = cardElement.querySelector('input[type="radio"]');
    if (radioButton) {
        radioButton.checked = true;

        // Trigger fare calculation
        setTimeout(() => {
            calculateFareAjax();
        }, 100);
    }
}

function selectDriver(cardElement) {
    // Remove selection from all driver cards
    document.querySelectorAll('.driver-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selection to clicked card
    cardElement.classList.add('selected');

    // Check the radio button
    const radioButton = cardElement.querySelector('input[type="radio"]');
    if (radioButton) {
        radioButton.checked = true;
    }
}

function toggleDriverSelection(show) {
    const driverSection = document.getElementById('driver-assignment-section');
    const driverIdInputs = document.querySelectorAll('input[name="driver_id"]');

    if (show) {
        driverSection.classList.remove('d-none');
    } else {
        driverSection.classList.add('d-none');

        // Clear driver selection
        driverIdInputs.forEach(input => {
            input.checked = false;
        });

        document.querySelectorAll('.driver-card').forEach(card => {
            card.classList.remove('selected');
        });
    }
}

function filterVehicles() {
    const typeFilter = document.getElementById('vehicle_type_filter').value.toLowerCase();
    const capacityFilter = document.getElementById('capacity_filter').value;
    const searchFilter = document.getElementById('vehicle_search').value.toLowerCase();

    console.log('Filtering vehicles with:', { typeFilter, capacityFilter, searchFilter });

    let visibleCount = 0;
    document.querySelectorAll('.vehicle-option').forEach(option => {
        let show = true;

        // Type filter
        if (typeFilter && option.dataset.type.toLowerCase() !== typeFilter) {
            show = false;
            console.log(`Vehicle ${option.dataset.name} hidden by type filter: ${option.dataset.type} !== ${typeFilter}`);
        }

        // Capacity filter
        if (capacityFilter) {
            const capacity = parseInt(option.dataset.capacity);
            if (capacityFilter === '1-3' && (capacity < 1 || capacity > 3)) {
                show = false;
            } else if (capacityFilter === '4-5' && (capacity < 4 || capacity > 5)) {
                show = false;
            } else if (capacityFilter === '6+' && capacity < 6) {
                show = false;
            }
        }

        // Search filter
        if (searchFilter) {
            const name = option.dataset.name || '';
            const features = option.dataset.features || '';
            if (!name.includes(searchFilter) && !features.includes(searchFilter)) {
                show = false;
            }
        }

        option.style.display = show ? 'block' : 'none';
        if (show) visibleCount++;
    });

    console.log(`Filtered vehicles: ${visibleCount} visible`);
}

function filterDrivers() {
    const ratingFilter = parseFloat(document.getElementById('driver_rating_filter').value);
    const experienceFilter = parseInt(document.getElementById('driver_experience_filter').value);
    const searchFilter = document.getElementById('driver_search').value.toLowerCase();

    document.querySelectorAll('.driver-option').forEach(option => {
        let show = true;

        // Rating filter
        if (ratingFilter) {
            const rating = parseFloat(option.dataset.rating);
            if (rating < ratingFilter) {
                show = false;
            }
        }

        // Experience filter
        if (experienceFilter) {
            const experience = parseInt(option.dataset.experience);
            if (experience < experienceFilter) {
                show = false;
            }
        }

        // Search filter
        if (searchFilter) {
            const name = option.dataset.name;
            if (!name.includes(searchFilter)) {
                show = false;
            }
        }

        option.style.display = show ? 'block' : 'none';
    });
}

function setMinimumDates() {
    const now = new Date();
    const minAdvanceHours = window.bookingSettings ? window.bookingSettings.minAdvanceBookingHours : {{ $minAdvanceBookingHours }};
    const maxAdvanceDays = window.bookingSettings ? window.bookingSettings.maxAdvanceBookingDays : {{ $maxAdvanceBookingDays }};

    // Calculate minimum date (current date + minimum advance hours)
    const minDate = new Date(now.getTime() + (minAdvanceHours * 60 * 60 * 1000));
    const minDateString = minDate.toISOString().split('T')[0];

    // Calculate maximum date (current date + maximum advance days)
    const maxDate = new Date(now.getTime() + (maxAdvanceDays * 24 * 60 * 60 * 1000));
    const maxDateString = maxDate.toISOString().split('T')[0];

    const dateInputs = document.querySelectorAll('input[type="date"]');

    dateInputs.forEach(input => {
        input.min = minDateString;
        input.max = maxDateString;

        // Add title for user guidance
        input.title = `Bookings must be made at least ${minAdvanceHours} hours in advance and no more than ${maxAdvanceDays} days in advance.`;
    });
}

function initializeExtraServices() {
    console.log('Initializing extra services...');

    // Ensure pricing settings are available
    if (!window.pricingSettings) {
        console.error('Pricing settings not available for extra services');
        return;
    }

    // Meet and Greet
    const meetAndGreetCheckbox = document.getElementById('meet_and_greet');
    if (meetAndGreetCheckbox) {
        meetAndGreetCheckbox.addEventListener('change', function() {
            console.log('Meet and greet checkbox changed:', this.checked);
            updateExtraServicesFare();
        });
    } else {
        console.warn('Meet and greet checkbox not found');
    }

    // Child Seat
    const childSeatCheckbox = document.getElementById('child_seat');
    if (childSeatCheckbox) {
        childSeatCheckbox.addEventListener('change', function() {
            console.log('Child seat checkbox changed:', this.checked);
            const childSeatDetails = document.getElementById('child-seat-details');
            if (this.checked) {
                if (childSeatDetails) {
                    childSeatDetails.classList.remove('d-none');
                }
            } else {
                if (childSeatDetails) {
                    childSeatDetails.classList.add('d-none');
                }
                const childSeatTypeElement = document.getElementById('child_seat_type');
                if (childSeatTypeElement) {
                    childSeatTypeElement.value = '';
                }
            }
            updateExtraServicesFare();
        });
    } else {
        console.warn('Child seat checkbox not found');
    }

    // Wheelchair Accessible
    const wheelchairCheckbox = document.getElementById('wheelchair_accessible');
    if (wheelchairCheckbox) {
        wheelchairCheckbox.addEventListener('change', function() {
            console.log('Wheelchair checkbox changed:', this.checked);
            updateExtraServicesFare();
        });
    } else {
        console.warn('Wheelchair checkbox not found');
    }

    // Extra Luggage
    const extraLuggageCheckbox = document.getElementById('extra_luggage');
    if (extraLuggageCheckbox) {
        extraLuggageCheckbox.addEventListener('change', function() {
            console.log('Extra luggage checkbox changed:', this.checked);
            updateExtraServicesFare();
        });
    } else {
        console.warn('Extra luggage checkbox not found');
    }

    // Initialize the display state
    updateExtraServicesFare();

    console.log('Extra services initialized successfully');
}

function initializeFormProgress() {
    console.log('Initializing form progress tracking...');

    // Track form completion progress
    const formElements = document.querySelectorAll('#admin-booking-form input, #admin-booking-form select, #admin-booking-form textarea');

    formElements.forEach(element => {
        element.addEventListener('input', updateFormProgress);
        element.addEventListener('change', updateFormProgress);
    });

    // Initial progress calculation
    updateFormProgress();

    console.log('Form progress tracking initialized');
}

function updateFormProgress() {
    const requiredFields = document.querySelectorAll('#admin-booking-form [required]:not([disabled])');
    const visibleRequiredFields = Array.from(requiredFields).filter(field => {
        const form = field.closest('.booking-type-form');
        return !form || !form.classList.contains('d-none');
    });

    let completedFields = 0;

    visibleRequiredFields.forEach(field => {
        if (field.type === 'radio' || field.type === 'checkbox') {
            const name = field.name;
            if (document.querySelector(`input[name="${name}"]:checked`)) {
                completedFields++;
            }
        } else if (field.value && field.value.trim() !== '') {
            completedFields++;
        }
    });

    const progressPercentage = visibleRequiredFields.length > 0
        ? Math.round((completedFields / visibleRequiredFields.length) * 100)
        : 0;

    const progressBar = document.getElementById('form-progress-bar');
    const progressText = document.getElementById('form-progress-text');

    if (progressBar) {
        progressBar.style.width = progressPercentage + '%';
        progressBar.setAttribute('aria-valuenow', progressPercentage);
    }

    if (progressText) {
        progressText.textContent = `Form completion: ${progressPercentage}%`;
    }

    // Update progress bar color based on completion
    if (progressBar) {
        progressBar.className = 'progress-bar';
        if (progressPercentage < 30) {
            progressBar.classList.add('bg-danger');
        } else if (progressPercentage < 70) {
            progressBar.classList.add('bg-warning');
        } else {
            progressBar.classList.add('bg-success');
        }
    }
}

function showLoadingProgress(message, percentage) {
    const loadingDiv = document.getElementById('form-loading');
    const loadingMessage = document.getElementById('loading-message');
    const progressBar = document.getElementById('loading-progress-bar');

    if (loadingDiv) {
        loadingDiv.classList.remove('d-none');
    }

    if (loadingMessage) {
        loadingMessage.textContent = message;
    }

    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
    }
}

function hideLoadingProgress() {
    const loadingDiv = document.getElementById('form-loading');
    if (loadingDiv) {
        loadingDiv.classList.add('d-none');
    }
}

function initializeAutoSave() {
    console.log('Initializing auto-save functionality...');

    let autoSaveTimeout;
    const AUTOSAVE_DELAY = 30000; // 30 seconds

    // Load saved draft on page load
    loadDraft();

    // Set up auto-save on form changes
    const formElements = document.querySelectorAll('#admin-booking-form input, #admin-booking-form select, #admin-booking-form textarea');

    formElements.forEach(element => {
        element.addEventListener('input', () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(saveDraft, AUTOSAVE_DELAY);
        });

        element.addEventListener('change', () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(saveDraft, AUTOSAVE_DELAY);
        });
    });

    // Save draft before page unload
    window.addEventListener('beforeunload', saveDraft);

    console.log('Auto-save functionality initialized');
}

function saveDraft() {
    try {
        const formData = {};
        const form = document.getElementById('admin-booking-form');
        const formElements = form.querySelectorAll('input, select, textarea');

        formElements.forEach(element => {
            if (element.type === 'radio' || element.type === 'checkbox') {
                if (element.checked) {
                    formData[element.name] = element.value;
                }
            } else if (element.value && element.value.trim() !== '') {
                formData[element.name] = element.value;
            }
        });

        // Only save if there's meaningful data
        if (Object.keys(formData).length > 2) { // More than just CSRF token and booking type
            localStorage.setItem('admin_booking_draft', JSON.stringify({
                data: formData,
                timestamp: new Date().toISOString(),
                bookingType: currentBookingType
            }));

            console.log('Draft saved successfully');
            showDraftSavedIndicator();
        }
    } catch (error) {
        console.error('Error saving draft:', error);
    }
}

function loadDraft() {
    try {
        const draftData = localStorage.getItem('admin_booking_draft');
        if (!draftData) return;

        const draft = JSON.parse(draftData);
        const draftAge = new Date() - new Date(draft.timestamp);
        const MAX_DRAFT_AGE = 24 * 60 * 60 * 1000; // 24 hours

        if (draftAge > MAX_DRAFT_AGE) {
            localStorage.removeItem('admin_booking_draft');
            return;
        }

        // Ask user if they want to restore the draft
        if (confirm('A draft booking was found. Would you like to restore it?')) {
            restoreDraft(draft);
        }
    } catch (error) {
        console.error('Error loading draft:', error);
        localStorage.removeItem('admin_booking_draft');
    }
}

function restoreDraft(draft) {
    try {
        // Set booking type first
        if (draft.bookingType) {
            const bookingTypeRadio = document.getElementById(draft.bookingType);
            if (bookingTypeRadio) {
                bookingTypeRadio.checked = true;
                currentBookingType = draft.bookingType;
                toggleBookingTypeForms(draft.bookingType);
            }
        }

        // Restore form data
        Object.entries(draft.data).forEach(([name, value]) => {
            const element = document.querySelector(`[name="${name}"]`);
            if (element) {
                if (element.type === 'radio' || element.type === 'checkbox') {
                    const specificElement = document.querySelector(`[name="${name}"][value="${value}"]`);
                    if (specificElement) {
                        specificElement.checked = true;
                    }
                } else {
                    element.value = value;
                }
            }
        });

        // Update form progress
        updateFormProgress();

        // Show restoration message
        showAlert('Draft booking restored successfully!', 'info');

        console.log('Draft restored successfully');
    } catch (error) {
        console.error('Error restoring draft:', error);
        showAlert('Error restoring draft booking.', 'warning');
    }
}

function clearDraft() {
    localStorage.removeItem('admin_booking_draft');
    console.log('Draft cleared');
}

function showDraftSavedIndicator() {
    // Create or update draft saved indicator
    let indicator = document.getElementById('draft-saved-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'draft-saved-indicator';
        indicator.className = 'position-fixed bottom-0 end-0 m-3 alert alert-success alert-dismissible fade show';
        indicator.style.zIndex = '9999';
        indicator.innerHTML = `
            <i class="fas fa-save me-1"></i>Draft saved
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(indicator);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (indicator && indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }
}

function showBookingConfirmation() {
    // Get booking summary for confirmation
    const clientType = document.querySelector('input[name="client_type"]:checked')?.value;
    let clientInfo = '';

    if (clientType === 'existing') {
        const clientSelect = document.getElementById('existing_client_id');
        const selectedOption = clientSelect.selectedOptions[0];
        if (selectedOption) {
            clientInfo = selectedOption.textContent;
        }
    } else {
        const clientName = document.getElementById('client_name')?.value || '';
        const clientEmail = document.getElementById('client_email')?.value || '';
        clientInfo = `${clientName} (${clientEmail})`;
    }

    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    const vehicleInfo = selectedVehicle ?
        selectedVehicle.closest('.vehicle-card').querySelector('.card-title').textContent : 'No vehicle selected';

    const totalAmount = document.getElementById('total-fare')?.textContent || '$0.00';

    const bookingTypeText = {
        'one_way': 'One Way',
        'return': 'Return Trip',
        'hourly': 'Hourly Service',
        'airport_transfer': 'Airport Transfer'
    }[currentBookingType] || 'Unknown';

    const confirmationMessage = `
        <div class="booking-confirmation">
            <h5 class="text-primary mb-3">Confirm Booking Creation</h5>
            <div class="row">
                <div class="col-md-6">
                    <strong>Client:</strong><br>
                    <span class="text-muted">${clientInfo}</span>
                </div>
                <div class="col-md-6">
                    <strong>Booking Type:</strong><br>
                    <span class="text-muted">${bookingTypeText}</span>
                </div>
                <div class="col-md-6 mt-2">
                    <strong>Vehicle:</strong><br>
                    <span class="text-muted">${vehicleInfo}</span>
                </div>
                <div class="col-md-6 mt-2">
                    <strong>Total Amount:</strong><br>
                    <span class="text-success fw-bold">${totalAmount}</span>
                </div>
            </div>
            <hr>
            <p class="text-muted mb-0">Are you sure you want to create this booking?</p>
        </div>
    `;

    // Create custom confirmation modal
    const modalHtml = `
        <div class="modal fade" id="bookingConfirmationModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-check-circle me-2 text-success"></i>Confirm Booking
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${confirmationMessage}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="confirmBookingBtn">
                            <i class="fas fa-check me-1"></i>Create Booking
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('bookingConfirmationModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal and handle confirmation
    const modal = new bootstrap.Modal(document.getElementById('bookingConfirmationModal'));
    modal.show();

    return new Promise((resolve) => {
        document.getElementById('confirmBookingBtn').addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        document.getElementById('bookingConfirmationModal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('bookingConfirmationModal').remove();
            resolve(false);
        });
    });
}

function updateExtraServicesFare() {
    // Ensure pricing settings are available
    if (!window.pricingSettings || !window.pricingSettings.currencySymbol) {
        console.error('Pricing settings not available');
        return;
    }

    const currencySymbol = window.pricingSettings.currencySymbol;

    // Meet and Greet
    const meetAndGreetItem = document.getElementById('meet-and-greet-item');
    const meetAndGreetCheckbox = document.getElementById('meet_and_greet');
    const meetAndGreetFeeElement = document.getElementById('meet-and-greet-fee');
    const extraServicesSection = document.getElementById('extra-services-section');

    if (meetAndGreetItem && meetAndGreetCheckbox && meetAndGreetFeeElement) {
        if (meetAndGreetCheckbox.checked) {
            const fee = parseFloat(window.pricingSettings.meetAndGreetFee) || 0;
            meetAndGreetFeeElement.textContent = currencySymbol + fee.toFixed(2);
            meetAndGreetItem.classList.remove('d-none');
            if (extraServicesSection) {
                extraServicesSection.style.display = 'block';
            }
        } else {
            meetAndGreetItem.classList.add('d-none');
        }
    }

    // Child Seat
    const childSeatItem = document.getElementById('child-seat-item');
    const childSeatCheckbox = document.getElementById('child_seat');
    const childSeatFeeElement = document.getElementById('child-seat-fee');

    if (childSeatItem && childSeatCheckbox && childSeatFeeElement) {
        if (childSeatCheckbox.checked) {
            const fee = parseFloat(window.pricingSettings.childSeatFee) || 0;
            childSeatFeeElement.textContent = currencySymbol + fee.toFixed(2);
            childSeatItem.classList.remove('d-none');
            if (extraServicesSection) {
                extraServicesSection.style.display = 'block';
            }
        } else {
            childSeatItem.classList.add('d-none');
        }
    }

    // Wheelchair Accessible
    const wheelchairItem = document.getElementById('wheelchair-item');
    const wheelchairCheckbox = document.getElementById('wheelchair_accessible');
    const wheelchairFeeElement = document.getElementById('wheelchair-fee');

    if (wheelchairItem && wheelchairCheckbox && wheelchairFeeElement) {
        if (wheelchairCheckbox.checked) {
            const fee = parseFloat(window.pricingSettings.wheelchairFee) || 0;
            wheelchairFeeElement.textContent = currencySymbol + fee.toFixed(2);
            wheelchairItem.classList.remove('d-none');
            if (extraServicesSection) {
                extraServicesSection.style.display = 'block';
            }
        } else {
            wheelchairItem.classList.add('d-none');
        }
    }

    // Extra Luggage
    const extraLuggageItem = document.getElementById('extra-luggage-item');
    const extraLuggageCheckbox = document.getElementById('extra_luggage');
    const extraLuggageFeeElement = document.getElementById('extra-luggage-fee');

    if (extraLuggageItem && extraLuggageCheckbox && extraLuggageFeeElement) {
        if (extraLuggageCheckbox.checked) {
            const fee = parseFloat(window.pricingSettings.extraLuggageFee) || 0;
            extraLuggageFeeElement.textContent = currencySymbol + fee.toFixed(2);
            extraLuggageItem.classList.remove('d-none');
            if (extraServicesSection) {
                extraServicesSection.style.display = 'block';
            }
        } else {
            extraLuggageItem.classList.add('d-none');
        }
    }

    // Check if any extra services are selected to show/hide the section
    const anyServiceSelected = (meetAndGreetCheckbox && meetAndGreetCheckbox.checked) ||
                              (childSeatCheckbox && childSeatCheckbox.checked) ||
                              (wheelchairCheckbox && wheelchairCheckbox.checked) ||
                              (extraLuggageCheckbox && extraLuggageCheckbox.checked);

    if (extraServicesSection) {
        if (anyServiceSelected) {
            extraServicesSection.style.display = 'block';
        } else {
            extraServicesSection.style.display = 'none';
        }
    }

    // Recalculate total fare with extras (use local calculation for better reliability)
    calculateTotalFareWithExtras();
}

// Global variable to store the base fare (without extra services) - declared above

function calculateTotalFareWithExtrasAjax() {
    // Check if we have a base fare to work with
    if (!baseFareAmount || baseFareAmount <= 0) {
        console.log('No base fare available for extra services calculation');
        return;
    }

    // Ensure pricing settings are available
    if (!window.pricingSettings || !window.pricingSettings.currencySymbol) {
        console.error('Pricing settings not available for fare calculation');
        return;
    }

    // Get selected vehicle
    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    if (!selectedVehicle) {
        console.log('No vehicle selected for extra services calculation');
        return;
    }

    // Collect extra services data
    const extraServices = {
        meet_and_greet: document.getElementById('meet_and_greet')?.checked || false,
        child_seat: document.getElementById('child_seat')?.checked || false,
        wheelchair_accessible: document.getElementById('wheelchair_accessible')?.checked || false,
        extra_luggage: document.getElementById('extra_luggage')?.checked || false
    };

    // Prepare form data for AJAX request
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('booking_type', currentBookingType);
    formData.append('vehicle_id', selectedVehicle.value);
    formData.append('base_fare', baseFareAmount);

    // Add extra services
    Object.keys(extraServices).forEach(service => {
        if (extraServices[service]) {
            formData.append(service, '1');
        }
    });

    // Add current booking data for context
    addCurrentBookingDataToFormData(formData);

    // Show loading indicator for extra services calculation
    showExtraServicesLoading(true);

    // Make AJAX request to recalculate fare with extra services
    fetch('{{ route("booking.calculate-fare") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data && data.success) {
            // Store the response for extra services checking
            window.lastFareResponse = data;

            // Update fare summary with new total including extra services
            updateFareSummaryWithExtras(data.fare_details || data, currentBookingType);
        } else {
            console.error('Extra services fare calculation failed:', data);
            // Fall back to local calculation
            calculateTotalFareWithExtras();
        }
    })
    .catch(error => {
        console.error('Error calculating fare with extra services:', error);
        // Fall back to local calculation
        calculateTotalFareWithExtras();
    })
    .finally(() => {
        // Hide loading indicator
        showExtraServicesLoading(false);
    });
}

function calculateTotalFareWithExtras() {
    // Ensure pricing settings are available
    if (!window.pricingSettings || !window.pricingSettings.currencySymbol) {
        console.error('Pricing settings not available for fare calculation');
        return;
    }

    const currencySymbol = window.pricingSettings.currencySymbol;

    // Calculate current extra services total
    const currentExtraServicesTotal = calculateCurrentExtraServicesTotal();
    console.log('Current extra services total:', currentExtraServicesTotal);

    // Get base total (without extra services)
    let baseTotal = 0;

    // If we have a backend response, get the base total without extra services
    if (window.lastFareResponse && window.lastFareResponse.fare_details) {
        const fareDetails = window.lastFareResponse.fare_details;
        // Calculate base total without extra services
        baseTotal = (parseFloat(fareDetails.total_fare) || 0) - (parseFloat(fareDetails.extra_services_total) || 0);
        console.log('Using backend base total (excluding extra services):', baseTotal);
    } else {
        // Use the stored base fare amount
        baseTotal = baseFareAmount || 0;

        // If base total is still 0, try to get it from the display and subtract any existing extra services
        if (baseTotal === 0) {
            const totalFareElement = document.getElementById('total-fare');
            if (totalFareElement) {
                const displayText = totalFareElement.textContent || '';
                const numericValue = displayText.replace(/[^\d.-]/g, '');
                baseTotal = parseFloat(numericValue) || 0;

                // If this total might already include extra services, try to subtract them
                // This is a fallback scenario
                if (baseTotal > 0) {
                    console.log('Using display total as base, might need adjustment:', baseTotal);
                }
            }
        }
    }

    console.log('Base total before adding extra services:', baseTotal);

    // Use the current extra services total we calculated
    const extrasTotal = currentExtraServicesTotal;

    // Get selected services for logging
    const selectedServices = [];
    const meetAndGreetCheckbox = document.getElementById('meet_and_greet');
    if (meetAndGreetCheckbox && meetAndGreetCheckbox.checked) {
        selectedServices.push({
            name: 'Meet and Greet',
            fee: parseFloat(window.pricingSettings.meetAndGreetFee) || 0
        });
    }

    const childSeatCheckbox = document.getElementById('child_seat');
    if (childSeatCheckbox && childSeatCheckbox.checked) {
        selectedServices.push({
            name: 'Child Seat',
            fee: parseFloat(window.pricingSettings.childSeatFee) || 0
        });
    }

    const wheelchairCheckbox = document.getElementById('wheelchair_accessible');
    if (wheelchairCheckbox && wheelchairCheckbox.checked) {
        selectedServices.push({
            name: 'Wheelchair Accessible',
            fee: parseFloat(window.pricingSettings.wheelchairFee) || 0
        });
    }

    const extraLuggageCheckbox = document.getElementById('extra_luggage');
    if (extraLuggageCheckbox && extraLuggageCheckbox.checked) {
        selectedServices.push({
            name: 'Extra Luggage',
            fee: parseFloat(window.pricingSettings.extraLuggageFee) || 0
        });
    }

    // Calculate new total: base + extra services
    const newTotal = baseTotal + extrasTotal;

    console.log('Fare calculation breakdown:', {
        baseTotal: baseTotal,
        extrasTotal: extrasTotal,
        newTotal: newTotal,
        selectedServices: selectedServices
    });

    // Update total display
    const totalFareElement = document.getElementById('total-fare');
    if (totalFareElement) {
        totalFareElement.textContent = currencySymbol + newTotal.toFixed(2);
    }

    // Update hidden amount field
    const amountField = document.getElementById('amount');
    if (amountField) {
        amountField.value = newTotal.toFixed(2);
    }

    // Enhanced logging with service breakdown
    console.log('Total fare updated:', {
        baseTotal: baseTotal,
        extrasTotal: extrasTotal,
        newTotal: newTotal,
        selectedServices: selectedServices,
        currency: currencySymbol,
        timestamp: new Date().toISOString()
    });

    // Show extra services summary if any are selected
    if (selectedServices.length > 0) {
        console.log('Extra services selected:', selectedServices.map(s => `${s.name}: ${currencySymbol}${s.fee.toFixed(2)}`).join(', '));
    }
}

function getCurrentExtraServicesSelection() {
    const services = {};

    const meetAndGreetCheckbox = document.getElementById('meet_and_greet');
    if (meetAndGreetCheckbox) {
        services.meet_and_greet = meetAndGreetCheckbox.checked;
    }

    const childSeatCheckbox = document.getElementById('child_seat');
    if (childSeatCheckbox) {
        services.child_seat = childSeatCheckbox.checked;
    }

    const wheelchairCheckbox = document.getElementById('wheelchair_accessible');
    if (wheelchairCheckbox) {
        services.wheelchair_accessible = wheelchairCheckbox.checked;
    }

    const extraLuggageCheckbox = document.getElementById('extra_luggage');
    if (extraLuggageCheckbox) {
        services.extra_luggage = extraLuggageCheckbox.checked;
    }

    return services;
}

function calculateCurrentExtraServicesTotal() {
    let total = 0;

    const meetAndGreetCheckbox = document.getElementById('meet_and_greet');
    if (meetAndGreetCheckbox && meetAndGreetCheckbox.checked) {
        total += parseFloat(window.pricingSettings.meetAndGreetFee) || 0;
    }

    const childSeatCheckbox = document.getElementById('child_seat');
    if (childSeatCheckbox && childSeatCheckbox.checked) {
        total += parseFloat(window.pricingSettings.childSeatFee) || 0;
    }

    const wheelchairCheckbox = document.getElementById('wheelchair_accessible');
    if (wheelchairCheckbox && wheelchairCheckbox.checked) {
        total += parseFloat(window.pricingSettings.wheelchairFee) || 0;
    }

    const extraLuggageCheckbox = document.getElementById('extra_luggage');
    if (extraLuggageCheckbox && extraLuggageCheckbox.checked) {
        total += parseFloat(window.pricingSettings.extraLuggageFee) || 0;
    }

    return total;
}

function showExtraServicesLoading(show) {
    const totalFareElement = document.getElementById('total-fare');
    if (!totalFareElement) return;

    if (show) {
        // Show loading spinner in total fare
        totalFareElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calculating...';
    } else {
        // This will be updated by the AJAX response or fallback calculation
        // No need to do anything here as the response will update the display
    }
}

function addCurrentBookingDataToFormData(formData) {
    // Add client information
    const clientTypeElement = document.querySelector('input[name="client_type"]:checked');
    if (clientTypeElement) {
        const clientType = clientTypeElement.value;
        if (clientType === 'existing') {
            const existingClientId = document.getElementById('existing_client_id').value;
            if (existingClientId) {
                formData.append('user_id', existingClientId);
            }
        }
    }

    // Add booking type specific data
    if (currentBookingType === 'one_way') {
        const pickupAddress = document.getElementById('pickup_address')?.value;
        const dropoffAddress = document.getElementById('dropoff_address')?.value;
        if (pickupAddress) formData.append('pickup_address', pickupAddress);
        if (dropoffAddress) formData.append('dropoff_address', dropoffAddress);

        // Add flight details for one way
        const flightNumber = document.getElementById('oneway_flight_number')?.value;
        const airline = document.getElementById('oneway_airline')?.value;
        const departureTime = document.getElementById('oneway_departure_time')?.value;
        const arrivalTime = document.getElementById('oneway_arrival_time')?.value;
        const terminal = document.getElementById('oneway_terminal')?.value;
        const flightStatus = document.getElementById('oneway_flight_status')?.value;
        const flightNotes = document.getElementById('oneway_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);
    } else if (currentBookingType === 'return') {
        const pickupAddress = document.getElementById('return_pickup_address')?.value;
        const dropoffAddress = document.getElementById('return_dropoff_address')?.value;
        if (pickupAddress) formData.append('pickup_address', pickupAddress);
        if (dropoffAddress) formData.append('dropoff_address', dropoffAddress);

        // Add flight details for return
        const flightNumber = document.getElementById('return_flight_number')?.value;
        const airline = document.getElementById('return_airline')?.value;
        const departureTime = document.getElementById('return_departure_time')?.value;
        const arrivalTime = document.getElementById('return_arrival_time')?.value;
        const terminal = document.getElementById('return_terminal')?.value;
        const flightStatus = document.getElementById('return_flight_status')?.value;
        const flightNotes = document.getElementById('return_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);
    } else if (currentBookingType === 'hourly') {
        const pickupAddress = document.getElementById('hourly_pickup_address')?.value;
        const durationHours = document.getElementById('duration_hours')?.value;
        if (pickupAddress) formData.append('pickup_address', pickupAddress);
        if (durationHours) formData.append('duration_hours', durationHours);

        // Add flight details for hourly
        const flightNumber = document.getElementById('hourly_flight_number')?.value;
        const airline = document.getElementById('hourly_airline')?.value;
        const departureTime = document.getElementById('hourly_departure_time')?.value;
        const arrivalTime = document.getElementById('hourly_arrival_time')?.value;
        const terminal = document.getElementById('hourly_terminal')?.value;
        const flightStatus = document.getElementById('hourly_flight_status')?.value;
        const flightNotes = document.getElementById('hourly_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);
    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value;
        const airportId = document.getElementById('airport_id')?.value;
        if (direction) formData.append('airport_direction', direction);
        if (airportId) formData.append('airport_id', airportId);

        // Add flight details for airport transfers
        const flightNumber = document.getElementById('flight_number')?.value;
        const airline = document.getElementById('airline')?.value;
        const departureTime = document.getElementById('departure_time')?.value;
        const arrivalTime = document.getElementById('arrival_time')?.value;
        const terminal = document.getElementById('terminal')?.value;
        const flightStatus = document.getElementById('flight_status')?.value;
        const flightNotes = document.getElementById('flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);
    }

    // Add distance and duration if available
    const distanceValue = document.getElementById('distance_value')?.value;
    const durationValue = document.getElementById('duration_value')?.value;
    if (distanceValue) formData.append('distance_value', distanceValue);
    if (durationValue) formData.append('duration_value', durationValue);
}

function updateFareSummaryWithExtras(fareData, bookingType) {
    // This function updates the fare summary when extra services are included
    const currencySymbol = window.pricingSettings ? window.pricingSettings.currencySymbol : '{{ $currencySymbol }}';

    // Ensure fareData exists and has required properties
    if (!fareData || typeof fareData !== 'object') {
        console.error('Invalid fare data received for extra services:', fareData);
        return;
    }

    // Update total with safe number conversion
    const totalFare = parseFloat(fareData.total_fare) || 0;

    // Update the fare summary display
    updateFareSummaryDisplay(totalFare, currencySymbol);

    // Log the update
    console.log('Fare summary updated with extra services:', {
        totalFare: totalFare,
        currency: currencySymbol,
        timestamp: new Date().toISOString()
    });
}

function initializeAutoFareCalculation() {
    // Add event listeners for automatic fare calculation
    const addressInputs = document.querySelectorAll('.address-autocomplete');
    addressInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Validate address before proceeding
            const isValid = isValidAddress(this.value);

            // Add visual feedback
            if (this.value && !isValid) {
                this.classList.add('is-invalid');

                // Show validation message
                let feedback = this.parentNode.querySelector('.invalid-feedback');
                if (!feedback) {
                    feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    this.parentNode.appendChild(feedback);
                }
                feedback.textContent = 'Please enter a valid address (minimum 3 characters, no single letters)';
            } else {
                this.classList.remove('is-invalid');
                const feedback = this.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.remove();
                }
            }

            // Proceed with map and fare calculation
            setTimeout(() => {
                updateMapAndFare();
            }, 500);
        });

        // Also validate on input (real-time)
        input.addEventListener('input', function() {
            // Remove validation styling while typing
            this.classList.remove('is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        });
    });
}

function updateMapAndFare() {
    updateMap();
    calculateFareAjax();
}

function updateMap() {
    if (!adminBookingMap || !directionsService || !directionsRenderer) return;

    const bookingType = currentBookingType;

    if (bookingType === 'hourly') {
        updateHourlyMap();
    } else if (bookingType === 'airport_transfer') {
        updateAirportMap();
    } else {
        updateRegularMap();
    }
}

function updateRegularMap() {
    let pickupAddress, dropoffAddress;

    if (currentBookingType === 'one_way') {
        const pickupElement = document.getElementById('pickup_address');
        const dropoffElement = document.getElementById('dropoff_address');
        pickupAddress = pickupElement ? pickupElement.value : '';
        dropoffAddress = dropoffElement ? dropoffElement.value : '';
    } else if (currentBookingType === 'return') {
        const pickupElement = document.getElementById('return_pickup_address');
        const dropoffElement = document.getElementById('return_dropoff_address');
        pickupAddress = pickupElement ? pickupElement.value : '';
        dropoffAddress = dropoffElement ? dropoffElement.value : '';
    }

    if (pickupAddress && dropoffAddress) {
        // Enhanced address validation before making API calls
        if (!isValidAddress(pickupAddress) || !isValidAddress(dropoffAddress)) {
            console.log('Invalid addresses detected, skipping route calculation');
            showMapError('Invalid addresses provided', 'INVALID_ADDRESS');

            // Show user-friendly message
            showAlert('Please enter valid pickup and dropoff addresses (minimum 3 characters, no single letters).', 'warning');

            // Still try to calculate fare without route data
            setTimeout(() => {
                calculateFareAjax();
            }, 1000);
            return;
        }

        // Clean and prepare addresses
        const cleanPickup = cleanAddress(pickupAddress);
        const cleanDropoff = cleanAddress(dropoffAddress);

        console.log('Calculating route:', {
            pickup: cleanPickup,
            dropoff: cleanDropoff
        });

        const request = {
            origin: cleanPickup,
            destination: cleanDropoff,
            travelMode: google.maps.TravelMode.DRIVING,
            unitSystem: google.maps.UnitSystem.METRIC,
            avoidHighways: false,
            avoidTolls: false
        };

        directionsService.route(request, function(result, status) {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);

                // Store distance and duration with null checks
                const route = result.routes[0];
                const leg = route.legs[0];

                const distanceValueElement = document.getElementById('distance_value');
                const durationValueElement = document.getElementById('duration_value');

                if (distanceValueElement) {
                    distanceValueElement.value = leg.distance.value;
                }
                if (durationValueElement) {
                    durationValueElement.value = leg.duration.value;
                }

                console.log('Route calculated successfully:', {
                    distance: leg.distance.text,
                    duration: leg.duration.text
                });
            } else {
                console.error('Directions request failed due to ' + status);

                // Handle specific error cases
                let errorMessage = 'Could not calculate route';
                switch(status) {
                    case 'NOT_FOUND':
                        errorMessage = 'Route not found. Please check the addresses.';
                        break;
                    case 'ZERO_RESULTS':
                        errorMessage = 'No route found between these locations.';
                        break;
                    case 'OVER_QUERY_LIMIT':
                        errorMessage = 'Maps service temporarily unavailable.';
                        break;
                    case 'REQUEST_DENIED':
                        errorMessage = 'Maps service access denied.';
                        break;
                    case 'INVALID_REQUEST':
                        errorMessage = 'Invalid route request.';
                        break;
                    default:
                        errorMessage = `Route calculation failed: ${status}`;
                }

                showMapError(errorMessage, status);

                // For NOT_FOUND errors, try geocoding the addresses individually
                if (status === 'NOT_FOUND') {
                    console.log('Attempting to validate addresses individually...');
                    validateAddressesIndividually(cleanPickup, cleanDropoff);
                } else {
                    // Still try to calculate fare without route data
                    console.log('Attempting fare calculation without route data...');
                    setTimeout(() => {
                        calculateFareAjax();
                    }, 1000);
                }
            }
        });
    }
}

function updateHourlyMap() {
    const pickupAddressElement = document.getElementById('hourly_pickup_address');
    const pickupAddress = pickupAddressElement ? pickupAddressElement.value : '';

    if (pickupAddress) {
        // Enhanced address validation
        if (!isValidAddress(pickupAddress)) {
            console.log('Invalid address detected for hourly service');
            showMapError('Invalid service location', 'INVALID_ADDRESS');
            showAlert('Please enter a valid service location (minimum 3 characters, no single letters).', 'warning');
            return;
        }

        // Clean address
        const cleanPickupAddress = cleanAddress(pickupAddress);

        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ address: cleanPickupAddress }, function(results, status) {
            if (status === 'OK') {
                adminBookingMap.setCenter(results[0].geometry.location);
                adminBookingMap.setZoom(15);

                // Clear previous directions
                directionsRenderer.setDirections({routes: []});

                new google.maps.Marker({
                    map: adminBookingMap,
                    position: results[0].geometry.location,
                    title: 'Service Location',
                    icon: {
                        url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png'
                    }
                });

                console.log('Hourly service location found:', results[0].formatted_address);
            } else {
                console.error('Geocoding failed due to ' + status);

                let errorMessage = 'Could not find location';
                switch(status) {
                    case 'NOT_FOUND':
                        errorMessage = 'Location not found. Please check the address.';
                        break;
                    case 'ZERO_RESULTS':
                        errorMessage = 'No results found for this location.';
                        break;
                    case 'OVER_QUERY_LIMIT':
                        errorMessage = 'Maps service temporarily unavailable.';
                        break;
                    default:
                        errorMessage = `Location lookup failed: ${status}`;
                }

                showMapError(errorMessage, status);
            }
        });
    }
}

function updateAirportMap() {
    const directionElement = document.querySelector('input[name="airport_direction"]:checked');
    if (!directionElement) {
        console.log('No airport direction selected');
        return;
    }

    const direction = directionElement.value;
    const airportSelect = document.getElementById('airport_id');

    if (!airportSelect) {
        console.error('Airport select element not found');
        return;
    }

    const selectedAirport = airportSelect.selectedOptions[0];

    let pickupAddress, dropoffAddress;

    if (direction === 'to_airport') {
        const pickupAddressElement = document.getElementById('airport_pickup_address');
        if (pickupAddressElement) {
            pickupAddress = pickupAddressElement.value;
        }
        if (selectedAirport && selectedAirport.value) {
            dropoffAddress = selectedAirport.dataset.name;
        }
    } else {
        if (selectedAirport && selectedAirport.value) {
            pickupAddress = selectedAirport.dataset.name;
        }
        const dropoffAddressElement = document.getElementById('airport_dropoff_address');
        if (dropoffAddressElement) {
            dropoffAddress = dropoffAddressElement.value;
        }
    }

    if (pickupAddress && dropoffAddress) {
        // Enhanced address validation
        if (!isValidAddress(pickupAddress) || !isValidAddress(dropoffAddress)) {
            console.log('Invalid addresses detected, skipping route calculation');
            showMapError('Invalid addresses provided', 'INVALID_ADDRESS');
            // Still try to calculate fare without route data
            setTimeout(() => {
                calculateFareAjax();
            }, 1000);
            return;
        }

        // Clean and prepare addresses
        const cleanPickup = cleanAddress(pickupAddress);
        const cleanDropoff = cleanAddress(dropoffAddress);

        console.log('Calculating route:', {
            pickup: cleanPickup,
            dropoff: cleanDropoff
        });

        const request = {
            origin: cleanPickup,
            destination: cleanDropoff,
            travelMode: google.maps.TravelMode.DRIVING,
            unitSystem: google.maps.UnitSystem.METRIC,
            avoidHighways: false,
            avoidTolls: false
        };

        directionsService.route(request, function(result, status) {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);

                // Store distance and duration with null checks
                const route = result.routes[0];
                const leg = route.legs[0];

                const distanceValueElement = document.getElementById('distance_value');
                const durationValueElement = document.getElementById('duration_value');

                if (distanceValueElement) {
                    distanceValueElement.value = leg.distance.value;
                }
                if (durationValueElement) {
                    durationValueElement.value = leg.duration.value;
                }

                console.log('Route calculated successfully:', {
                    distance: leg.distance.text,
                    duration: leg.duration.text
                });
            } else {
                console.error('Directions request failed due to ' + status);

                // Handle specific error cases
                let errorMessage = 'Could not calculate route';
                switch(status) {
                    case 'NOT_FOUND':
                        errorMessage = 'Route not found. Please check the addresses.';
                        break;
                    case 'ZERO_RESULTS':
                        errorMessage = 'No route found between these locations.';
                        break;
                    case 'OVER_QUERY_LIMIT':
                        errorMessage = 'Maps service temporarily unavailable.';
                        break;
                    case 'REQUEST_DENIED':
                        errorMessage = 'Maps service access denied.';
                        break;
                    case 'INVALID_REQUEST':
                        errorMessage = 'Invalid route request.';
                        break;
                    default:
                        errorMessage = `Route calculation failed: ${status}`;
                }

                showMapError(errorMessage, status);

                // For NOT_FOUND errors, try geocoding the addresses individually
                if (status === 'NOT_FOUND') {
                    console.log('Attempting to validate addresses individually...');
                    validateAddressesIndividually(cleanPickup, cleanDropoff);
                } else {
                    // Still try to calculate fare without route data
                    console.log('Attempting fare calculation without route data...');
                    setTimeout(() => {
                        calculateFareAjax();
                    }, 1000);
                }
            }
        });
    }
}

function showMapError(title, status) {
    let errorIcon = 'fas fa-exclamation-triangle';
    let errorColor = 'text-warning';
    let retryButton = '';
    let additionalInfo = '';

    // Customize error display based on status
    switch(status) {
        case 'NOT_FOUND':
            errorIcon = 'fas fa-map-marker-alt';
            additionalInfo = '<small class="text-muted d-block mt-2">Please check that both addresses are complete and valid.</small>';
            break;
        case 'INVALID_ADDRESS':
            errorIcon = 'fas fa-edit';
            errorColor = 'text-danger';
            additionalInfo = '<small class="text-muted d-block mt-2">Please enter valid pickup and dropoff addresses.</small>';
            break;
        case 'OVER_QUERY_LIMIT':
            errorIcon = 'fas fa-clock';
            errorColor = 'text-info';
            additionalInfo = '<small class="text-muted d-block mt-2">Please wait a moment and try again.</small>';
            break;
        case 'ADDRESS_VALIDATION_FAILED':
            errorIcon = 'fas fa-times-circle';
            errorColor = 'text-danger';
            break;
        default:
            retryButton = `
                <button class="btn btn-sm btn-primary mt-2" onclick="updateMap()">
                    <i class="fas fa-redo me-1"></i> Retry Route Calculation
                </button>
            `;
    }

    // Always show fare calculation button
    const fareButton = `
        <button class="btn btn-sm btn-success mt-2 ms-2" onclick="calculateFareAjax()">
            <i class="fas fa-calculator me-1"></i> Calculate Fare Anyway
        </button>
    `;

    document.getElementById('admin-booking-map').innerHTML = `
        <div class="d-flex align-items-center justify-content-center h-100">
            <div class="text-center p-4">
                <i class="${errorIcon} fa-3x ${errorColor} mb-3"></i>
                <h6 class="text-dark mb-2">${title}</h6>
                <p class="text-muted small mb-2">Status: ${status}</p>
                ${additionalInfo}
                <div class="mt-3">
                    ${retryButton}
                    ${fareButton}
                </div>
            </div>
        </div>
    `;
}

function calculateFareAjax() {
    // Validate fare summary state first
    if (!validateFareSummaryState()) {
        console.error('Fare summary validation failed');
        showFareError('Fare calculation system not properly initialized');
        return;
    }

    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    if (!selectedVehicle) {
        console.log('No vehicle selected for fare calculation');
        return;
    }

    console.log('Starting fare calculation for booking type:', currentBookingType);

    // Show loading
    const fareStatus = document.getElementById('fare-calculation-status');
    const fareBreakdown = document.getElementById('fare-breakdown');

    fareStatus.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Calculating...</span>
            </div>
            <p class="text-muted mt-2 mb-0">Calculating fare...</p>
        </div>
    `;
    fareStatus.classList.remove('d-none');
    fareBreakdown.classList.add('d-none');

    // Prepare form data
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    formData.append('booking_type', currentBookingType);
    formData.append('vehicle_id', selectedVehicle.value);

    // Add client information
    const clientTypeElement = document.querySelector('input[name="client_type"]:checked');
    if (!clientTypeElement) {
        showFareError('Please select a client type first');
        return;
    }

    const clientType = clientTypeElement.value;
    if (clientType === 'existing') {
        const existingClientId = document.getElementById('existing_client_id').value;
        if (existingClientId) {
            formData.append('user_id', existingClientId);
        }
    } else {
        // New client data
        const clientName = document.getElementById('client_name').value;
        const clientEmail = document.getElementById('client_email').value;
        const clientPhone = document.getElementById('client_phone').value;
        const clientAddress = document.getElementById('client_address').value;

        if (clientName) formData.append('client_name', clientName.toString());
        if (clientEmail) formData.append('client_email', clientEmail.toString());
        if (clientPhone) formData.append('client_phone', clientPhone.toString());
        if (clientAddress) formData.append('client_address', clientAddress.toString());
    }

    // Add pickup date and time (required for all booking types)
    let formPickupDate = '';
    let formPickupTime = '';

    if (currentBookingType === 'one_way') {
        formPickupDate = document.getElementById('pickup_date').value;
        formPickupTime = document.getElementById('pickup_time').value;
    } else if (currentBookingType === 'return') {
        formPickupDate = document.getElementById('pickup_date_return').value;
        formPickupTime = document.getElementById('pickup_time_return').value;
    } else if (currentBookingType === 'hourly') {
        formPickupDate = document.getElementById('pickup_date_hourly').value;
        formPickupTime = document.getElementById('pickup_time_hourly').value;
    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value;
        if (direction === 'to_airport') {
            formPickupDate = document.getElementById('pickup_date_airport').value;
            formPickupTime = document.getElementById('pickup_time_airport').value;
        } else {
            formPickupDate = document.getElementById('pickup_date_airport_from').value;
            formPickupTime = document.getElementById('pickup_time_airport_from').value;
        }
    }

    if (formPickupDate) formData.append('pickup_date', formPickupDate);
    if (formPickupTime) formData.append('pickup_time', formPickupTime);

    // Add driver assignment
    const assignDriver = document.getElementById('assign_driver').checked;
    formData.append('assign_driver', assignDriver ? '1' : '0');

    if (assignDriver) {
        const selectedDriver = document.querySelector('input[name="driver_id"]:checked');
        if (selectedDriver) {
            formData.append('driver_id', selectedDriver.value);
        }
    }

    // Add booking type specific data
    if (currentBookingType === 'one_way') {
        const pickupAddress = document.getElementById('pickup_address').value;
        const dropoffAddress = document.getElementById('dropoff_address').value;

        if (!pickupAddress || !dropoffAddress) {
            showFareError('Please enter both pickup and dropoff addresses');
            return;
        }

        formData.append('pickup_address', pickupAddress);
        formData.append('dropoff_address', dropoffAddress);
        formData.append('pickup_lat', document.getElementById('pickup_lat').value || '');
        formData.append('pickup_lng', document.getElementById('pickup_lng').value || '');
        formData.append('dropoff_lat', document.getElementById('dropoff_lat').value || '');
        formData.append('dropoff_lng', document.getElementById('dropoff_lng').value || '');
    } else if (currentBookingType === 'return') {
        const pickupAddress = document.getElementById('return_pickup_address').value;
        const dropoffAddress = document.getElementById('return_dropoff_address').value;

        if (!pickupAddress || !dropoffAddress) {
            showFareError('Please enter both pickup and dropoff addresses');
            return;
        }

        formData.append('pickup_address', pickupAddress);
        formData.append('dropoff_address', dropoffAddress);
        formData.append('pickup_lat', document.getElementById('return_pickup_lat').value || '');
        formData.append('pickup_lng', document.getElementById('return_pickup_lng').value || '');
        formData.append('dropoff_lat', document.getElementById('return_dropoff_lat').value || '');
        formData.append('dropoff_lng', document.getElementById('return_dropoff_lng').value || '');
        formData.append('return_date', document.getElementById('return_date').value || '');
        formData.append('return_time', document.getElementById('return_time').value || '');
    } else if (currentBookingType === 'hourly') {
        const pickupAddress = document.getElementById('hourly_pickup_address').value;
        const durationHours = document.getElementById('duration_hours').value;

        if (!pickupAddress) {
            showFareError('Please enter service location');
            return;
        }

        if (!durationHours) {
            showFareError('Please select service duration');
            return;
        }

        formData.append('pickup_address', pickupAddress);
        formData.append('pickup_lat', document.getElementById('hourly_pickup_lat').value || '');
        formData.append('pickup_lng', document.getElementById('hourly_pickup_lng').value || '');
        formData.append('duration_hours', durationHours);
        // For hourly bookings, dropoff is not required but we need to satisfy validation
        formData.append('dropoff_address', '');
    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value;
        const airportId = document.getElementById('airport_id').value;

        if (!direction) {
            showFareError('Please select airport direction');
            return;
        }

        if (!airportId) {
            showFareError('Please select an airport');
            return;
        }

        formData.append('airport_direction', direction);
        formData.append('airport_id', airportId);

        // Add airport surcharge flag for fare calculation
        formData.append('include_airport_surcharge', '1');

        if (direction === 'to_airport') {
            const pickupAddress = document.getElementById('airport_pickup_address').value;
            if (!pickupAddress) {
                showFareError('Please enter pickup address');
                return;
            }
            formData.append('pickup_address', pickupAddress);
            formData.append('pickup_lat', document.getElementById('airport_pickup_lat').value || '');
            formData.append('pickup_lng', document.getElementById('airport_pickup_lng').value || '');
            // Get airport as dropoff
            const airportSelect = document.getElementById('airport_id');
            const selectedAirport = airportSelect.selectedOptions[0];
            formData.append('dropoff_address', selectedAirport ? selectedAirport.dataset.name : '');
        } else {
            const dropoffAddress = document.getElementById('airport_dropoff_address').value;
            if (!dropoffAddress) {
                showFareError('Please enter dropoff address');
                return;
            }
            formData.append('dropoff_address', dropoffAddress);
            formData.append('dropoff_lat', document.getElementById('airport_dropoff_lat').value || '');
            formData.append('dropoff_lng', document.getElementById('airport_dropoff_lng').value || '');
            // Get airport as pickup
            const airportSelect = document.getElementById('airport_id');
            const selectedAirport = airportSelect.selectedOptions[0];
            formData.append('pickup_address', selectedAirport ? selectedAirport.dataset.name : '');
        }
    }

    // Add distance and duration if available
    const distanceValueElement = document.getElementById('distance_value');
    const durationValueElement = document.getElementById('duration_value');
    const distanceValue = distanceValueElement ? distanceValueElement.value : '';
    const durationValue = durationValueElement ? durationValueElement.value : '';

    if (distanceValue && distanceValue !== '0') {
        formData.append('distance_value', distanceValue);
    }
    if (durationValue && durationValue !== '0') {
        formData.append('duration_value', durationValue);
    }

    // Calculate and add time-based surcharges
    const farePickupDate = formData.get('pickup_date') ||
                          formData.get('return_pickup_date') ||
                          formData.get('hourly_pickup_date') ||
                          formData.get('airport_pickup_date');

    const farePickupTime = formData.get('pickup_time') ||
                          formData.get('return_pickup_time') ||
                          formData.get('hourly_pickup_time') ||
                          formData.get('airport_pickup_time');

    // Calculate time-based surcharges
    const timeBasedSurcharges = calculateTimeBasedSurcharges(farePickupDate, farePickupTime);

    // Add surcharges to form data
    if (timeBasedSurcharges.weekend_surcharge > 0) {
        formData.append('weekend_surcharge', timeBasedSurcharges.weekend_surcharge);
        console.log('Weekend surcharge added to form data:', timeBasedSurcharges.weekend_surcharge);
    }
    if (timeBasedSurcharges.night_surcharge > 0) {
        formData.append('night_surcharge', timeBasedSurcharges.night_surcharge);
        console.log('Night surcharge added to form data:', timeBasedSurcharges.night_surcharge);
    }

    // Add flag to indicate if we have route data
    const hasRouteData = distanceValue && distanceValue !== '0' && durationValue && durationValue !== '0';
    formData.append('has_route_data', hasRouteData ? '1' : '0');

    // Debug: Log form data being sent
    console.log('Form data being sent:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    // Make AJAX request
    fetch('{{ route("booking.calculate-fare") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            // Try to get the response body for more details
            return response.text().then(text => {
                console.error('HTTP error response:', text);
                throw new Error(`HTTP error! status: ${response.status} - ${text.substring(0, 200)}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Fare calculation response:', data);

        if (data && data.success) {
            // Store the response for extra services checking
            window.lastFareResponse = data;

            // Update amount field with safe number conversion
            const totalFare = parseFloat(data.total_fare || data.fare || data.fare_details?.total_fare) || 0;
            const amountField = document.getElementById('amount');
            if (amountField) {
                amountField.value = totalFare.toFixed(2);
            }

            // Update fare summary
            updateFareSummary(data.fare_details || data, currentBookingType);
        } else {
            console.error('Fare calculation failed:', data);

            // Handle validation errors specifically
            if (data && data.errors) {
                let errorMessage = 'Validation errors:\n';
                Object.keys(data.errors).forEach(field => {
                    errorMessage += `${field}: ${data.errors[field].join(', ')}\n`;
                });
                console.error('Validation errors:', data.errors);
                showFareError(errorMessage);
            } else {
                showFareError(data && data.message ? data.message : 'Fare calculation failed');
            }
        }
    })
    .catch(error => {
        console.error('Error calculating fare:', error);
        showFareError('Error calculating fare: ' + error.message);
    });
}

function updateFareSummary(fareData, bookingType) {
    const currencySymbol = window.pricingSettings ? window.pricingSettings.currencySymbol : '{{ $currencySymbol }}';

    // Ensure fareData exists and has required properties
    if (!fareData || typeof fareData !== 'object') {
        console.error('Invalid fare data received:', fareData);
        showFareError('Invalid fare data received');
        return;
    }

    // Get required elements with null checks
    const fareStatus = document.getElementById('fare-calculation-status');
    const fareBreakdown = document.getElementById('fare-breakdown');
    const baseFareElement = document.getElementById('base-fare');
    const fareStatusBadge = document.getElementById('fare-status-badge');

    if (!fareStatus || !fareBreakdown || !baseFareElement) {
        console.error('Required fare summary elements not found');
        return;
    }

    // Update status badge
    if (fareStatusBadge) {
        fareStatusBadge.textContent = 'Calculated';
        fareStatusBadge.className = 'badge bg-success';
    }

    // Hide status, show breakdown with animation
    fareStatus.classList.add('d-none');
    fareBreakdown.classList.remove('d-none');

    // Update base fare with safe number conversion
    let baseFare = parseFloat(fareData.base_fare) || 0;

    // If base fare is 0, try to get it from settings
    if (baseFare === 0) {
        baseFare = parseFloat(window.pricingSettings?.baseFare) || 5.0;
    }

    baseFareElement.textContent = currencySymbol + baseFare.toFixed(2);

    // Update calculation details base fare
    const calcBaseFareElement = document.getElementById('calc-base-fare');
    if (calcBaseFareElement) {
        calcBaseFareElement.textContent = currencySymbol + baseFare.toFixed(2);
    }

    // Update distance fare (hide for hourly bookings)
    const distanceFareItem = document.getElementById('distance-fare-item');
    const distanceDisplay = document.getElementById('distance-display');
    const distanceFareDisplay = document.getElementById('distance-fare');

    if (distanceFareItem) {
        if (bookingType === 'hourly') {
            // Hide distance for hourly bookings
            distanceFareItem.classList.add('d-none');
        } else if (fareData.distance_km && !isNaN(fareData.distance_km) && distanceDisplay && distanceFareDisplay) {
            const distanceKm = parseFloat(fareData.distance_km) || 0;

            // Calculate distance fare if not provided
            let distanceFare = parseFloat(fareData.distance_fare) || 0;
            if (distanceFare === 0 && distanceKm > 0) {
                const pricePerKm = parseFloat(fareData.price_per_km) ||
                                 parseFloat(window.pricingSettings?.pricePerKm) || 2.5;
                distanceFare = distanceKm * pricePerKm;
            }

            // Get distance unit from settings
            const distanceUnit = window.pricingSettings?.distanceUnit || 'km';

            // Convert distance if needed
            let displayDistance = distanceKm;
            let unitLabel = 'km';

            if (distanceUnit === 'miles') {
                displayDistance = distanceKm * 0.621371; // Convert km to miles
                unitLabel = 'mi';
            }

            distanceDisplay.textContent = displayDistance.toFixed(1);
            distanceFareDisplay.textContent = currencySymbol + distanceFare.toFixed(2);
            distanceFareItem.classList.remove('d-none');

            // Update distance unit display
            const distanceUnitElement = document.getElementById('distance-unit');
            if (distanceUnitElement) {
                distanceUnitElement.textContent = unitLabel;
            }

            // Update calculation details
            const calcDistanceSection = document.getElementById('calc-distance-section');
            if (calcDistanceSection) {
                calcDistanceSection.style.display = 'block';

                const calcDistanceElement = document.getElementById('calc-distance');
                const calcPricePerKmElement = document.getElementById('calc-price-per-km');

                if (calcDistanceElement) {
                    calcDistanceElement.textContent = displayDistance.toFixed(1) + ' ' + unitLabel;
                }
                if (calcPricePerKmElement) {
                    const pricePerKm = distanceFare / distanceKm;
                    calcPricePerKmElement.textContent = currencySymbol + pricePerKm.toFixed(2);
                }
            }
        } else {
            distanceFareItem.classList.add('d-none');
            const calcDistanceSection = document.getElementById('calc-distance-section');
            if (calcDistanceSection) {
                calcDistanceSection.style.display = 'none';
            }
        }
    }

    // Update duration fare for hourly bookings
    const durationFareItem = document.getElementById('duration-fare-item');
    const durationDisplay = document.getElementById('duration-display');
    const durationFareDisplay = document.getElementById('duration-fare');

    if (durationFareItem) {
        if (bookingType === 'hourly') {
            // For hourly bookings, show duration and hourly fare
            const durationHours = parseFloat(fareData.duration_hours) ||
                                 parseFloat(fareData.hours) ||
                                 parseFloat(document.getElementById('duration_hours')?.value) || 0;

            // Get hourly rate and calculate total hourly fare
            let hourlyRate = parseFloat(fareData.hourly_rate) ||
                           parseFloat(window.pricingSettings?.hourlyRate) || 50;

            // Calculate total hourly fare
            let hourlyFare = 0;
            if (fareData.hourly_charge !== undefined) {
                hourlyFare = parseFloat(fareData.hourly_charge) || 0;
            } else if (fareData.total_fare !== undefined) {
                hourlyFare = parseFloat(fareData.total_fare) || 0;
            } else {
                hourlyFare = hourlyRate * durationHours;
            }

            if (durationHours > 0 && durationDisplay && durationFareDisplay) {
                durationDisplay.textContent = durationHours + ' hours';
                durationFareDisplay.textContent = currencySymbol + hourlyFare.toFixed(2);
                durationFareItem.classList.remove('d-none');

                // Show hourly rate in calculation details
                const calcHourlySection = document.getElementById('calc-hourly-section');
                if (calcHourlySection) {
                    calcHourlySection.style.display = 'block';

                    // Update hourly rate display
                    const hourlyRateElement = document.getElementById('calc-hourly-rate');
                    const calcDurationElement = document.getElementById('calc-duration');

                    if (hourlyRateElement) {
                        hourlyRateElement.textContent = currencySymbol + hourlyRate.toFixed(2);
                    }
                    if (calcDurationElement) {
                        calcDurationElement.textContent = durationHours + ' hours';
                    }
                }

                console.log('Hourly calculation updated:', {
                    durationHours: durationHours,
                    hourlyRate: hourlyRate,
                    hourlyFare: hourlyFare,
                    currency: currencySymbol
                });
            } else {
                durationFareItem.classList.add('d-none');
            }
        } else {
            durationFareItem.classList.add('d-none');
            const calcHourlySection = document.getElementById('calc-hourly-section');
            if (calcHourlySection) {
                calcHourlySection.style.display = 'none';
            }
        }
    }

    // Update airport surcharge
    const airportSurchargeItem = document.getElementById('airport-surcharge-item');
    const airportSurchargeDisplay = document.getElementById('airport-surcharge');

    if (airportSurchargeItem && airportSurchargeDisplay) {
        if (bookingType === 'airport_transfer') {
            // Get airport surcharge from fare data or settings
            let airportSurcharge = 0;

            if (fareData.airport_surcharge !== undefined) {
                airportSurcharge = parseFloat(fareData.airport_surcharge) || 0;
            } else if (window.pricingSettings && window.pricingSettings.airportSurcharge) {
                airportSurcharge = parseFloat(window.pricingSettings.airportSurcharge) || 0;
            }

            // Always show airport surcharge for airport transfers, even if 0
            airportSurchargeDisplay.textContent = currencySymbol + airportSurcharge.toFixed(2);
            airportSurchargeItem.classList.remove('d-none');

            // Show airport fee in calculation details
            const calcAirportSection = document.getElementById('calc-airport-section');
            if (calcAirportSection) {
                calcAirportSection.style.display = 'block';

                // Update airport fee display in calculation details
                const calcAirportFeeElement = document.getElementById('calc-airport-fee');
                if (calcAirportFeeElement) {
                    calcAirportFeeElement.textContent = currencySymbol + airportSurcharge.toFixed(2);
                }
            }

            console.log('Airport surcharge updated:', {
                amount: airportSurcharge,
                currency: currencySymbol,
                source: fareData.airport_surcharge !== undefined ? 'fare_data' : 'settings'
            });
        } else {
            airportSurchargeItem.classList.add('d-none');
            const calcAirportSection = document.getElementById('calc-airport-section');
            if (calcAirportSection) {
                calcAirportSection.style.display = 'none';
            }
        }
    }

    // Update additional charges display
    updateAdditionalChargesDisplay(fareData, currencySymbol);

    // Calculate comprehensive total including all charges
    const comprehensiveCalculation = calculateComprehensiveTotal(fareData, bookingType);
    let totalFare = parseFloat(fareData.total_fare) || comprehensiveCalculation.total;

    // Always use comprehensive calculation to ensure all charges are included
    if (comprehensiveCalculation.total > totalFare) {
        totalFare = comprehensiveCalculation.total;
        console.log('Using comprehensive calculation as it includes more charges:', comprehensiveCalculation);
    }

    // Store the base fare amount (without extra services) for later calculations
    baseFareAmount = totalFare;

    // Update the fare summary display
    updateFareSummaryDisplay(totalFare, currencySymbol);

    // Update extra services display and recalculate total with extras
    updateExtraServicesFare();

    // Update calculation details summary
    updateCalculationDetails(fareData, bookingType, currencySymbol);

    console.log('Fare summary updated:', {
        baseFare: baseFare,
        totalFare: totalFare,
        bookingType: bookingType,
        fareData: fareData,
        currency: currencySymbol
    });
}

function updateCalculationDetails(fareData, bookingType, currencySymbol) {
    // Update base rate
    const calcBaseRateElement = document.getElementById('calc-base-rate');
    if (calcBaseRateElement) {
        const baseFare = parseFloat(fareData.base_fare) ||
                        parseFloat(window.pricingSettings?.baseFare) || 5.0;
        calcBaseRateElement.textContent = currencySymbol + baseFare.toFixed(2);
    }

    // Update per km rate for distance-based bookings
    if (bookingType !== 'hourly') {
        const calcPerKmElement = document.getElementById('calc-per-km');
        if (calcPerKmElement) {
            const pricePerKm = parseFloat(fareData.price_per_km) ||
                             parseFloat(window.pricingSettings?.pricePerKm) || 2.5;
            calcPerKmElement.textContent = currencySymbol + pricePerKm.toFixed(2);
        }
    }

    // Update hourly rate for hourly bookings
    if (bookingType === 'hourly') {
        const calcHourlyRateElement = document.getElementById('calc-hourly-rate');
        if (calcHourlyRateElement) {
            const hourlyRate = parseFloat(fareData.hourly_rate) ||
                             parseFloat(window.pricingSettings?.hourlyRate) || 50;
            calcHourlyRateElement.textContent = currencySymbol + hourlyRate.toFixed(2);
        }
    }

    // Update airport fee for airport transfers
    if (bookingType === 'airport_transfer') {
        const calcAirportFeeElement = document.getElementById('calc-airport-fee');
        if (calcAirportFeeElement) {
            const airportSurcharge = parseFloat(fareData.airport_surcharge) ||
                                   parseFloat(window.pricingSettings?.airportSurcharge) || 0;
            calcAirportFeeElement.textContent = currencySymbol + airportSurcharge.toFixed(2);
        }
    }

    console.log('Calculation details updated for booking type:', bookingType);
}

function updateAdditionalChargesDisplay(fareData, currencySymbol) {
    // Update booking fee display
    const bookingFeeItem = document.getElementById('booking-fee-item');
    const bookingFeeDisplay = document.getElementById('booking-fee');

    if (bookingFeeItem && bookingFeeDisplay) {
        const bookingFee = parseFloat(fareData.booking_fee) || 0;
        if (bookingFee > 0) {
            bookingFeeDisplay.textContent = currencySymbol + bookingFee.toFixed(2);
            bookingFeeItem.classList.remove('d-none');
            console.log('Booking fee displayed:', bookingFee);
        } else {
            bookingFeeItem.classList.add('d-none');
        }
    }

    // Update weekend surcharge display
    const weekendSurchargeItem = document.getElementById('weekend-surcharge-item');
    const weekendSurchargeDisplay = document.getElementById('weekend-surcharge');

    if (weekendSurchargeItem && weekendSurchargeDisplay) {
        const weekendSurcharge = parseFloat(fareData.weekend_surcharge) || 0;
        if (weekendSurcharge > 0) {
            weekendSurchargeDisplay.textContent = currencySymbol + weekendSurcharge.toFixed(2);
            weekendSurchargeItem.classList.remove('d-none');
            console.log('Weekend surcharge displayed:', weekendSurcharge);
        } else {
            weekendSurchargeItem.classList.add('d-none');
        }
    }

    // Update night surcharge display
    const nightSurchargeItem = document.getElementById('night-surcharge-item');
    const nightSurchargeDisplay = document.getElementById('night-surcharge');

    if (nightSurchargeItem && nightSurchargeDisplay) {
        const nightSurcharge = parseFloat(fareData.night_surcharge) || 0;
        if (nightSurcharge > 0) {
            nightSurchargeDisplay.textContent = currencySymbol + nightSurcharge.toFixed(2);
            nightSurchargeItem.classList.remove('d-none');
            console.log('Night surcharge displayed:', nightSurcharge);
        } else {
            nightSurchargeItem.classList.add('d-none');
        }
    }

    // Update tax display
    const taxItem = document.getElementById('tax-item');
    const taxAmountDisplay = document.getElementById('tax-amount');
    const taxRateDisplay = document.getElementById('tax-rate-display');

    if (taxItem && taxAmountDisplay && taxRateDisplay) {
        const taxAmount = parseFloat(fareData.tax_amount) || 0;
        const taxRate = parseFloat(fareData.tax_rate) || 0;

        if (taxAmount > 0) {
            taxAmountDisplay.textContent = currencySymbol + taxAmount.toFixed(2);
            taxRateDisplay.textContent = taxRate.toFixed(1);
            taxItem.classList.remove('d-none');
            console.log('Tax displayed:', { amount: taxAmount, rate: taxRate });
        } else {
            taxItem.classList.add('d-none');
        }
    }

    // Log all additional charges for debugging
    console.log('Additional charges updated:', {
        booking_fee: parseFloat(fareData.booking_fee) || 0,
        weekend_surcharge: parseFloat(fareData.weekend_surcharge) || 0,
        night_surcharge: parseFloat(fareData.night_surcharge) || 0,
        tax_amount: parseFloat(fareData.tax_amount) || 0,
        tax_rate: parseFloat(fareData.tax_rate) || 0
    });
}

function debugFareCalculation() {
    console.log('=== FARE CALCULATION DEBUG ===');
    console.log('Current booking type:', currentBookingType);
    console.log('Base fare amount:', baseFareAmount);
    console.log('Pricing settings:', window.pricingSettings);

    // Check fare summary elements
    const fareElements = {
        'fare-calculation-status': document.getElementById('fare-calculation-status'),
        'fare-breakdown': document.getElementById('fare-breakdown'),
        'base-fare': document.getElementById('base-fare'),
        'distance-fare-item': document.getElementById('distance-fare-item'),
        'duration-fare-item': document.getElementById('duration-fare-item'),
        'airport-surcharge-item': document.getElementById('airport-surcharge-item'),
        'total-fare': document.getElementById('total-fare'),
        'amount': document.getElementById('amount')
    };

    console.log('Fare elements status:');
    Object.keys(fareElements).forEach(key => {
        const element = fareElements[key];
        console.log(`${key}:`, element ? 'Found' : 'Missing', element?.textContent || element?.value || '');
    });

    // Check extra services
    const extraServices = {
        'meet_and_greet': document.getElementById('meet_and_greet'),
        'child_seat': document.getElementById('child_seat'),
        'wheelchair_accessible': document.getElementById('wheelchair_accessible'),
        'extra_luggage': document.getElementById('extra_luggage')
    };

    console.log('Extra services status:');
    Object.keys(extraServices).forEach(key => {
        const element = extraServices[key];
        console.log(`${key}:`, element ? (element.checked ? 'Checked' : 'Unchecked') : 'Missing');
    });

    // Check selected vehicle
    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    console.log('Selected vehicle:', selectedVehicle ? selectedVehicle.value : 'None');

    console.log('=== END DEBUG ===');
}

function isValidAddress(address) {
    if (!address || typeof address !== 'string') {
        return false;
    }

    const trimmed = address.trim();

    // Check minimum length
    if (trimmed.length < 3) {
        return false;
    }

    // Check for invalid single characters or very short strings
    if (trimmed.length === 1 || /^[a-zA-Z]$/.test(trimmed)) {
        return false;
    }

    // Check for placeholder text or invalid entries
    const invalidPatterns = [
        /^(test|example|placeholder|temp|xxx|yyy|zzz)$/i,
        /^[0-9]+$/,  // Only numbers
        /^[^a-zA-Z0-9\s,.-]+$/,  // Only special characters
        /^(.)\1{2,}$/  // Repeated characters like "aaa", "111"
    ];

    for (const pattern of invalidPatterns) {
        if (pattern.test(trimmed)) {
            return false;
        }
    }

    // Must contain at least one letter or number
    if (!/[a-zA-Z0-9]/.test(trimmed)) {
        return false;
    }

    return true;
}

function cleanAddress(address) {
    if (!address || typeof address !== 'string') {
        return '';
    }

    let cleaned = address.trim();

    // Remove multiple spaces
    cleaned = cleaned.replace(/\s+/g, ' ');

    // Remove leading/trailing commas
    cleaned = cleaned.replace(/^,+|,+$/g, '');

    // Ensure proper formatting for UK addresses
    if (cleaned.toLowerCase().includes('uk') && !cleaned.toLowerCase().includes(', uk')) {
        cleaned = cleaned.replace(/\s+uk$/i, ', UK');
    }

    return cleaned;
}

function validateAddressBeforeRouting(pickup, dropoff) {
    const issues = [];

    if (!isValidAddress(pickup)) {
        issues.push('Pickup address is invalid or too short');
    }

    if (!isValidAddress(dropoff)) {
        issues.push('Dropoff address is invalid or too short');
    }

    if (pickup && dropoff && pickup.toLowerCase() === dropoff.toLowerCase()) {
        issues.push('Pickup and dropoff addresses cannot be the same');
    }

    return {
        isValid: issues.length === 0,
        issues: issues
    };
}

function validateAddressesForRouting(pickup, dropoff) {
    const issues = [];

    // Check if addresses exist
    if (!pickup || !pickup.trim()) {
        issues.push('Pickup address is required');
    }

    if (!dropoff || !dropoff.trim()) {
        issues.push('Dropoff address is required');
    }

    // Validate pickup address
    if (pickup && !isValidAddress(pickup)) {
        issues.push(`Pickup address "${pickup}" is invalid (too short or contains invalid characters)`);
    }

    // Validate dropoff address
    if (dropoff && !isValidAddress(dropoff)) {
        issues.push(`Dropoff address "${dropoff}" is invalid (too short or contains invalid characters)`);
    }

    // Check if addresses are the same (for non-hourly bookings)
    if (pickup && dropoff && pickup.toLowerCase().trim() === dropoff.toLowerCase().trim()) {
        issues.push('Pickup and dropoff addresses cannot be the same');
    }

    // Check for common invalid patterns
    const invalidAddresses = ['test', 'example', 'placeholder', 'temp', 'xxx', 'yyy', 'zzz'];
    if (pickup && invalidAddresses.includes(pickup.toLowerCase().trim())) {
        issues.push(`Pickup address "${pickup}" appears to be a placeholder`);
    }

    if (dropoff && invalidAddresses.includes(dropoff.toLowerCase().trim())) {
        issues.push(`Dropoff address "${dropoff}" appears to be a placeholder`);
    }

    return {
        isValid: issues.length === 0,
        issues: issues
    };
}

function validateAddressesIndividually(pickup, dropoff) {
    const geocoder = new google.maps.Geocoder();
    let validAddresses = 0;
    let totalAddresses = 2;
    let addressResults = {};

    console.log('Validating addresses individually:', { pickup, dropoff });

    // Validate pickup address
    geocoder.geocode({ address: pickup }, function(results, status) {
        if (status === 'OK') {
            console.log('Pickup address is valid:', pickup);
            addressResults.pickup = {
                valid: true,
                location: results[0].geometry.location,
                formatted: results[0].formatted_address
            };
            validAddresses++;
        } else {
            console.log('Pickup address is invalid:', pickup, status);
            addressResults.pickup = {
                valid: false,
                error: status
            };
        }

        checkValidationComplete();
    });

    // Validate dropoff address
    geocoder.geocode({ address: dropoff }, function(results, status) {
        if (status === 'OK') {
            console.log('Dropoff address is valid:', dropoff);
            addressResults.dropoff = {
                valid: true,
                location: results[0].geometry.location,
                formatted: results[0].formatted_address
            };
            validAddresses++;
        } else {
            console.log('Dropoff address is invalid:', dropoff, status);
            addressResults.dropoff = {
                valid: false,
                error: status
            };
        }

        checkValidationComplete();
    });

    function checkValidationComplete() {
        if (Object.keys(addressResults).length === totalAddresses) {
            handleAddressValidationResults(addressResults);
        }
    }
}

function handleAddressValidationResults(results) {
    console.log('Address validation results:', results);

    let errorMessage = 'Address validation failed:\n';
    let hasErrors = false;

    if (!results.pickup.valid) {
        errorMessage += `• Pickup address "${document.getElementById('pickup_address')?.value || 'Unknown'}" is invalid\n`;
        hasErrors = true;
    }

    if (!results.dropoff.valid) {
        errorMessage += `• Dropoff address "${document.getElementById('dropoff_address')?.value || 'Unknown'}" is invalid\n`;
        hasErrors = true;
    }

    if (hasErrors) {
        // Show specific error message
        showMapError('Invalid Addresses Detected', 'ADDRESS_VALIDATION_FAILED');

        // Show user-friendly alert
        showAlert(errorMessage + '\nPlease check your addresses and try again.', 'warning');
    } else {
        // Both addresses are valid but route calculation failed
        // Try alternative route calculation or proceed with fare calculation
        console.log('Both addresses are valid, proceeding with fare calculation without route data');
        showMapError('Route calculation failed but addresses are valid', 'ROUTE_CALCULATION_FAILED');
    }

    // Always try to calculate fare
    setTimeout(() => {
        calculateFareAjax();
    }, 1000);
}

function updateFareSummaryDisplay(totalFare, currencySymbol) {
    // Validate inputs
    if (typeof totalFare !== 'number' || isNaN(totalFare)) {
        console.error('Invalid totalFare provided:', totalFare);
        return;
    }

    if (!currencySymbol) {
        console.error('Currency symbol not provided');
        currencySymbol = '$'; // fallback
    }

    // Update the total fare display
    const totalFareElement = document.getElementById('total-fare');
    if (totalFareElement) {
        totalFareElement.textContent = currencySymbol + totalFare.toFixed(2);
    }

    // Update hidden amount field
    const amountField = document.getElementById('amount');
    if (amountField) {
        amountField.value = totalFare.toFixed(2);
    }

    // Show a summary of what's included in the total fare
    console.log('Total fare updated (including all charges):', {
        totalFare: totalFare,
        currency: currencySymbol,
        timestamp: new Date().toISOString()
    });
}

function calculateComprehensiveTotal(fareData, bookingType) {
    let total = 0;
    const breakdown = {};

    // Base fare
    const baseFare = parseFloat(fareData.base_fare) ||
                    parseFloat(window.pricingSettings?.baseFare) || 5.0;
    total += baseFare;
    breakdown.baseFare = baseFare;

    // Distance charge (for non-hourly bookings)
    if (bookingType !== 'hourly' && fareData.distance_km) {
        const distanceKm = parseFloat(fareData.distance_km) || 0;
        const pricePerKm = parseFloat(fareData.price_per_km) ||
                          parseFloat(window.pricingSettings?.pricePerKm) || 2.5;
        const distanceCharge = distanceKm * pricePerKm;
        total += distanceCharge;
        breakdown.distanceCharge = distanceCharge;
    }

    // Hourly charge (for hourly bookings)
    if (bookingType === 'hourly') {
        const durationHours = parseFloat(fareData.duration_hours) ||
                             parseFloat(fareData.hours) ||
                             parseFloat(document.getElementById('duration_hours')?.value) || 0;
        const hourlyRate = parseFloat(fareData.hourly_rate) ||
                          parseFloat(window.pricingSettings?.hourlyRate) || 50;
        const hourlyCharge = durationHours * hourlyRate;
        total += hourlyCharge;
        breakdown.hourlyCharge = hourlyCharge;
    }

    // Airport surcharge
    if (bookingType === 'airport_transfer') {
        const airportSurcharge = parseFloat(fareData.airport_surcharge) ||
                               parseFloat(window.pricingSettings?.airportSurcharge) || 0;
        total += airportSurcharge;
        breakdown.airportSurcharge = airportSurcharge;
    }

    // Booking fee
    const bookingFee = parseFloat(fareData.booking_fee) || 0;
    if (bookingFee > 0) {
        total += bookingFee;
        breakdown.bookingFee = bookingFee;
    }

    // Weekend surcharge
    const weekendSurcharge = parseFloat(fareData.weekend_surcharge) || 0;
    if (weekendSurcharge > 0) {
        total += weekendSurcharge;
        breakdown.weekendSurcharge = weekendSurcharge;
    }

    // Night surcharge
    const nightSurcharge = parseFloat(fareData.night_surcharge) || 0;
    if (nightSurcharge > 0) {
        total += nightSurcharge;
        breakdown.nightSurcharge = nightSurcharge;
    }

    // Tax
    const taxAmount = parseFloat(fareData.tax_amount) || 0;
    if (taxAmount > 0) {
        total += taxAmount;
        breakdown.taxAmount = taxAmount;
    }

    console.log('Comprehensive total calculation:', {
        bookingType: bookingType,
        breakdown: breakdown,
        total: total
    });

    return {
        total: total,
        breakdown: breakdown
    };
}

function showFareError(message) {
    const fareStatus = document.getElementById('fare-calculation-status');
    const fareBreakdown = document.getElementById('fare-breakdown');
    const fareStatusBadge = document.getElementById('fare-status-badge');

    if (!fareStatus) {
        console.error('Fare status element not found');
        return;
    }

    // Update status badge
    if (fareStatusBadge) {
        fareStatusBadge.textContent = 'Error';
        fareStatusBadge.className = 'badge bg-danger';
    }

    fareStatus.innerHTML = `
        <div class="text-center py-4">
            <div class="fare-status-icon mb-3">
                <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
            </div>
            <h6 class="text-warning mb-2">Calculation Error</h6>
            <p class="text-muted small mb-0">${message}</p>
        </div>
    `;
    fareStatus.classList.remove('d-none');

    if (fareBreakdown) {
        fareBreakdown.classList.add('d-none');
    }
}

function validateFareSummaryState() {
    // Check if all required elements exist
    const requiredElements = [
        'fare-calculation-status',
        'fare-breakdown',
        'base-fare',
        'total-fare',
        'amount'
    ];

    const missingElements = [];

    requiredElements.forEach(elementId => {
        if (!document.getElementById(elementId)) {
            missingElements.push(elementId);
        }
    });

    if (missingElements.length > 0) {
        console.error('Missing fare summary elements:', missingElements);
        return false;
    }

    // Check if pricing settings are available
    if (!window.pricingSettings) {
        console.error('Pricing settings not available');
        return false;
    }

    return true;
}

async function handleFormSubmission(event) {
    event.preventDefault();

    const form = event.target;
    const createBtn = document.getElementById('create-booking-btn');
    const loadingDiv = document.getElementById('form-loading');

    // Custom validation for admin booking form
    if (!validateAdminBookingForm()) {
        form.classList.add('was-validated');
        return;
    }

    // Validate booking settings
    if (!validateBookingSettings()) {
        return;
    }

    // Show confirmation dialog for booking creation
    const confirmed = await showBookingConfirmation();
    if (!confirmed) {
        return;
    }

    // Prepare form data for submission
    const formData = prepareAdminBookingFormData();

    // Show enhanced loading state
    createBtn.disabled = true;
    showLoadingProgress('Validating booking data...', 10);

    // Clear previous alerts
    clearAlerts();

    // Submit form via AJAX with progress updates
    showLoadingProgress('Sending booking data...', 30);

    fetch('{{ route("admin.bookings.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        showLoadingProgress('Processing booking...', 60);
        return response.json();
    })
    .then(data => {
        showLoadingProgress('Finalizing booking...', 90);

        if (data.success) {
            showLoadingProgress('Booking created successfully!', 100);
            showAlert('Booking created successfully!', 'success');

            // Clear the draft since booking was successful
            clearDraft();

            // Redirect after a short delay
            setTimeout(() => {
                showLoadingProgress('Redirecting...', 100);
                window.location.href = data.redirect;
            }, 1500);
        } else {
            hideLoadingProgress();

            // Handle validation errors
            if (data.errors) {
                let errorMessage = 'Please correct the following errors:<ul>';
                Object.values(data.errors).forEach(errors => {
                    errors.forEach(error => {
                        errorMessage += `<li>${error}</li>`;
                    });
                });
                errorMessage += '</ul>';
                showAlert(errorMessage, 'danger');
            } else {
                showAlert(data.message || 'An error occurred while creating the booking.', 'danger');
            }
        }
    })
    .catch(error => {
        console.error('Error submitting form:', error);
        hideLoadingProgress();
        showAlert('An unexpected error occurred. Please try again.', 'danger');
    })
    .finally(() => {
        // Reset button state only if not redirecting
        if (!document.querySelector('.alert-success')) {
            createBtn.disabled = false;
            hideLoadingProgress();
        }
    });
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? All entered data will be lost.')) {
        // Reset the form
        const form = document.getElementById('admin-booking-form');
        if (form) {
            form.reset();
            form.classList.remove('was-validated');
        }

        // Reset booking type to one_way
        currentBookingType = 'one_way';
        const oneWayRadio = document.getElementById('one_way');
        if (oneWayRadio) {
            oneWayRadio.checked = true;
        }
        toggleBookingTypeForms('one_way');

        // Reset client type to existing
        const existingClientRadio = document.getElementById('existing_client');
        if (existingClientRadio) {
            existingClientRadio.checked = true;
        }
        toggleClientSections('existing');

        // Clear vehicle and driver selections
        document.querySelectorAll('.vehicle-card, .driver-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Reset driver assignment
        const assignDriverCheckbox = document.getElementById('assign_driver');
        if (assignDriverCheckbox) {
            assignDriverCheckbox.checked = false;
        }
        toggleDriverSelection(false);

        // Clear fare summary and reset base fare
        baseFareAmount = 0;
        const fareStatus = document.getElementById('fare-calculation-status');
        const fareBreakdown = document.getElementById('fare-breakdown');

        if (fareStatus) {
            fareStatus.classList.remove('d-none');
            fareStatus.innerHTML = `
                <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">Select vehicle and enter addresses to calculate fare</p>
            `;
        }
        if (fareBreakdown) {
            fareBreakdown.classList.add('d-none');
        }

        // Hide all extra service items
        document.querySelectorAll('[id$="-item"]').forEach(item => {
            if (item.id.includes('meet-and-greet') || item.id.includes('child-seat') ||
                item.id.includes('wheelchair') || item.id.includes('extra-luggage')) {
                item.style.display = 'none';
            }
        });

        // Reset map
        if (adminBookingMap) {
            adminBookingMap.setCenter({ lat: {{ $mapDefaultLat }}, lng: {{ $mapDefaultLng }} });
            adminBookingMap.setZoom({{ $mapDefaultZoom }});
            if (directionsRenderer) {
                directionsRenderer.setDirections({routes: []});
            }
        }

        // Clear alerts
        clearAlerts();

        showAlert('Form has been reset.', 'info');
    }
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    const alertId = 'alert-' + Date.now();

    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    alertContainer.innerHTML = alertHtml;

    // Auto-dismiss success and info alerts after 5 seconds
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alert-container').innerHTML = '';
}

function applyAutoDriverAssignmentSetting() {
    const autoAssignDriver = window.bookingSettings ? window.bookingSettings.autoAssignDriver : {{ $autoAssignDriver ? 'true' : 'false' }};
    const assignDriverCheckbox = document.getElementById('assign_driver');

    if (assignDriverCheckbox && autoAssignDriver) {
        assignDriverCheckbox.checked = true;
        toggleDriverSelection(true);

        // Add a note about auto assignment
        const driverSection = document.getElementById('driver-assignment-section');
        if (driverSection) {
            const autoNote = document.createElement('div');
            autoNote.className = 'alert alert-info small mt-2';
            autoNote.innerHTML = '<i class="fas fa-info-circle me-1"></i> Auto driver assignment is enabled in settings.';
            driverSection.insertBefore(autoNote, driverSection.firstChild);
        }
    }
}

function validateAdminBookingForm() {
    let isValid = true;
    const errors = [];

    // Validate client type selection
    const clientTypeElement = document.querySelector('input[name="client_type"]:checked');
    if (!clientTypeElement) {
        errors.push('Please select a client type.');
        isValid = false;
        return isValid;
    }

    // Validate client information
    const clientType = clientTypeElement.value;
    if (clientType === 'existing') {
        const existingClientElement = document.getElementById('existing_client_id');
        const existingClientId = existingClientElement ? existingClientElement.value : '';
        if (!existingClientId) {
            errors.push('Please select a client.');
            isValid = false;
        }
    } else {
        // Validate new client fields
        const clientNameElement = document.getElementById('client_name');
        const clientEmailElement = document.getElementById('client_email');
        const clientPhoneElement = document.getElementById('client_phone');

        const clientName = clientNameElement ? clientNameElement.value.trim() : '';
        const clientEmail = clientEmailElement ? clientEmailElement.value.trim() : '';
        const clientPhone = clientPhoneElement ? clientPhoneElement.value.trim() : '';

        if (!clientName) {
            errors.push('Client name is required.');
            isValid = false;
        }

        if (!clientEmail) {
            errors.push('Client email is required.');
            isValid = false;
        } else if (!isValidEmail(clientEmail)) {
            errors.push('Please enter a valid email address.');
            isValid = false;
        }

        if (!clientPhone) {
            errors.push('Client phone is required.');
            isValid = false;
        }
    }

    // Validate vehicle selection
    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    if (!selectedVehicle) {
        errors.push('Please select a vehicle.');
        isValid = false;
    }

    // Validate booking type specific fields
    if (currentBookingType === 'one_way') {
        const pickupAddressElement = document.getElementById('pickup_address');
        const dropoffAddressElement = document.getElementById('dropoff_address');
        const pickupDateElement = document.getElementById('pickup_date');
        const pickupTimeElement = document.getElementById('pickup_time');

        const pickupAddress = pickupAddressElement ? pickupAddressElement.value.trim() : '';
        const dropoffAddress = dropoffAddressElement ? dropoffAddressElement.value.trim() : '';
        const pickupDate = pickupDateElement ? pickupDateElement.value : '';
        const pickupTime = pickupTimeElement ? pickupTimeElement.value : '';

        if (!pickupAddress) {
            errors.push('Pickup address is required.');
            isValid = false;
        }
        if (!dropoffAddress) {
            errors.push('Dropoff address is required.');
            isValid = false;
        }
        if (!pickupDate) {
            errors.push('Pickup date is required.');
            isValid = false;
        }
        if (!pickupTime) {
            errors.push('Pickup time is required.');
            isValid = false;
        }
    } else if (currentBookingType === 'return') {
        const pickupAddress = document.getElementById('return_pickup_address').value.trim();
        const dropoffAddress = document.getElementById('return_dropoff_address').value.trim();
        const pickupDate = document.getElementById('pickup_date_return').value;
        const pickupTime = document.getElementById('pickup_time_return').value;
        const returnDate = document.getElementById('return_date').value;
        const returnTime = document.getElementById('return_time').value;

        if (!pickupAddress) {
            errors.push('Pickup address is required.');
            isValid = false;
        }
        if (!dropoffAddress) {
            errors.push('Dropoff address is required.');
            isValid = false;
        }
        if (!pickupDate) {
            errors.push('Pickup date is required.');
            isValid = false;
        }
        if (!pickupTime) {
            errors.push('Pickup time is required.');
            isValid = false;
        }
        if (!returnDate) {
            errors.push('Return date is required.');
            isValid = false;
        }
        if (!returnTime) {
            errors.push('Return time is required.');
            isValid = false;
        }
    } else if (currentBookingType === 'hourly') {
        const pickupAddress = document.getElementById('hourly_pickup_address').value.trim();
        const durationHours = document.getElementById('duration_hours').value;
        const pickupDate = document.getElementById('pickup_date_hourly').value;
        const pickupTime = document.getElementById('pickup_time_hourly').value;

        if (!pickupAddress) {
            errors.push('Service location is required.');
            isValid = false;
        }
        if (!durationHours) {
            errors.push('Service duration is required.');
            isValid = false;
        }
        if (!pickupDate) {
            errors.push('Service date is required.');
            isValid = false;
        }
        if (!pickupTime) {
            errors.push('Service time is required.');
            isValid = false;
        }
    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value;
        const airportId = document.getElementById('airport_id').value;

        if (!direction) {
            errors.push('Please select airport direction.');
            isValid = false;
        }
        if (!airportId) {
            errors.push('Please select an airport.');
            isValid = false;
        }

        if (direction === 'to_airport') {
            const pickupAddress = document.getElementById('airport_pickup_address').value.trim();
            const pickupDate = document.getElementById('pickup_date_airport').value;
            const pickupTime = document.getElementById('pickup_time_airport').value;

            if (!pickupAddress) {
                errors.push('Pickup address is required.');
                isValid = false;
            }
            if (!pickupDate) {
                errors.push('Pickup date is required.');
                isValid = false;
            }
            if (!pickupTime) {
                errors.push('Pickup time is required.');
                isValid = false;
            }
        } else {
            const dropoffAddress = document.getElementById('airport_dropoff_address').value.trim();
            const pickupDate = document.getElementById('pickup_date_airport_from').value;
            const pickupTime = document.getElementById('pickup_time_airport_from').value;

            if (!dropoffAddress) {
                errors.push('Dropoff address is required.');
                isValid = false;
            }
            if (!pickupDate) {
                errors.push('Pickup date is required.');
                isValid = false;
            }
            if (!pickupTime) {
                errors.push('Pickup time is required.');
                isValid = false;
            }
        }
    }

    // Show errors if any
    if (!isValid) {
        const errorMessage = 'Please correct the following errors:<ul><li>' + errors.join('</li><li>') + '</li></ul>';
        showAlert(errorMessage, 'danger');
    }

    return isValid;
}

function isValidEmail(email) {
    if (!email || typeof email !== 'string') {
        return false;
    }

    // Trim whitespace
    email = email.trim();

    // Check minimum length
    if (email.length < 5) {
        return false;
    }

    // Enhanced email regex pattern
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    // Test the email format
    if (!emailRegex.test(email)) {
        return false;
    }

    // Additional checks
    // Check for consecutive dots
    if (email.includes('..')) {
        return false;
    }

    // Check for valid domain
    const parts = email.split('@');
    if (parts.length !== 2) {
        return false;
    }

    const [localPart, domain] = parts;

    // Local part checks
    if (localPart.length === 0 || localPart.length > 64) {
        return false;
    }

    // Domain checks
    if (domain.length === 0 || domain.length > 253) {
        return false;
    }

    // Domain must contain at least one dot
    if (!domain.includes('.')) {
        return false;
    }

    // Domain parts check
    const domainParts = domain.split('.');
    for (const part of domainParts) {
        if (part.length === 0 || part.length > 63) {
            return false;
        }
    }

    return true;
}

function validateEmailField(emailInput) {
    if (!emailInput) return;

    const email = emailInput.value.trim();
    const isValid = email === '' || isValidEmail(email);

    // Remove existing validation classes
    emailInput.classList.remove('is-valid', 'is-invalid');

    // Remove existing feedback
    const existingFeedback = emailInput.parentNode.querySelector('.invalid-feedback.email-validation');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    if (email !== '') {
        if (isValid) {
            emailInput.classList.add('is-valid');
        } else {
            emailInput.classList.add('is-invalid');

            // Add specific email validation feedback
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback email-validation';
            feedback.textContent = 'Please enter a valid email address (e.g., <EMAIL>)';
            emailInput.parentNode.appendChild(feedback);
        }
    }

    return isValid;
}

function validateBookingSettings() {
    const now = new Date();
    const minAdvanceHours = window.bookingSettings ? window.bookingSettings.minAdvanceBookingHours : {{ $minAdvanceBookingHours }};

    // Get selected pickup date and time based on current booking type
    let validationPickupDate = '';
    let validationPickupTime = '';

    if (currentBookingType === 'one_way') {
        validationPickupDate = document.getElementById('pickup_date').value;
        validationPickupTime = document.getElementById('pickup_time').value;
    } else if (currentBookingType === 'return') {
        validationPickupDate = document.getElementById('pickup_date_return').value;
        validationPickupTime = document.getElementById('pickup_time_return').value;
    } else if (currentBookingType === 'hourly') {
        validationPickupDate = document.getElementById('pickup_date_hourly').value;
        validationPickupTime = document.getElementById('pickup_time_hourly').value;
    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value;
        if (direction === 'to_airport') {
            validationPickupDate = document.getElementById('pickup_date_airport').value;
            validationPickupTime = document.getElementById('pickup_time_airport').value;
        } else {
            validationPickupDate = document.getElementById('pickup_date_airport_from').value;
            validationPickupTime = document.getElementById('pickup_time_airport_from').value;
        }
    }

    if (validationPickupDate && validationPickupTime) {
        const pickupDateTime = new Date(validationPickupDate + 'T' + validationPickupTime);
        const timeDiff = (pickupDateTime.getTime() - now.getTime()) / (1000 * 60 * 60); // hours
        const daysDiff = (pickupDateTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24); // days
        const maxAdvanceDays = window.bookingSettings ? window.bookingSettings.maxAdvanceBookingDays : {{ $maxAdvanceBookingDays }};

        // Check minimum advance time
        if (timeDiff < minAdvanceHours) {
            showAlert(`Bookings must be made at least ${minAdvanceHours} hours in advance. Please select a later date/time.`, 'warning');
            return false;
        }

        // Check maximum advance time
        if (daysDiff > maxAdvanceDays) {
            showAlert(`Bookings cannot be made more than ${maxAdvanceDays} days in advance. Please select an earlier date.`, 'warning');
            return false;
        }

        // Check if pickup time is in the past
        if (pickupDateTime <= now) {
            showAlert('Pickup time cannot be in the past. Please select a future date and time.', 'warning');
            return false;
        }
    }

    // Validate return date for return bookings
    if (currentBookingType === 'return') {
        const returnDate = document.getElementById('return_date').value;
        const returnTime = document.getElementById('return_time').value;

        if (returnDate && returnTime && validationPickupDate && validationPickupTime) {
            const returnDateTime = new Date(returnDate + 'T' + returnTime);
            const pickupDateTime = new Date(validationPickupDate + 'T' + validationPickupTime);

            if (returnDateTime <= pickupDateTime) {
                showAlert('Return time must be after pickup time. Please adjust your dates and times.', 'warning');
                return false;
            }
        }
    }

    return true;
}

function prepareAdminBookingFormData() {
    const formData = new FormData();

    // Add CSRF token
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    // Add booking type
    formData.append('booking_type', currentBookingType);

    // Add client information
    const clientTypeElement = document.querySelector('input[name="client_type"]:checked');
    const clientType = clientTypeElement ? clientTypeElement.value : 'existing'; // Default to existing if none selected
    formData.append('client_type', clientType); // Add client type field

    if (clientType === 'existing') {
        const existingClientId = document.getElementById('existing_client_id').value;
        if (existingClientId) {
            formData.append('existing_client_id', existingClientId);
        }
    } else {
        // New client data
        const clientName = document.getElementById('client_name').value.trim();
        const clientEmail = document.getElementById('client_email').value.trim();
        const clientPhone = document.getElementById('client_phone').value.trim();
        const clientAddress = document.getElementById('client_address').value.trim();

        formData.append('client_name', clientName);
        formData.append('client_email', clientEmail);
        formData.append('client_phone', clientPhone);
        if (clientAddress) formData.append('client_address', clientAddress);
    }

    // Add vehicle selection
    const selectedVehicle = document.querySelector('input[name="vehicle_id"]:checked');
    if (selectedVehicle) {
        formData.append('vehicle_id', selectedVehicle.value);
    }

    // Add driver assignment (boolean field)
    const assignDriver = document.getElementById('assign_driver').checked;
    formData.append('assign_driver', assignDriver ? '1' : '0');

    if (assignDriver) {
        const selectedDriver = document.querySelector('input[name="driver_id"]:checked');
        if (selectedDriver) {
            formData.append('driver_id', selectedDriver.value);
        }
    }

    // Add booking type specific data
    if (currentBookingType === 'one_way') {
        formData.append('pickup_address', document.getElementById('pickup_address').value.trim());
        formData.append('dropoff_address', document.getElementById('dropoff_address').value.trim());
        formData.append('pickup_date', document.getElementById('pickup_date').value);
        formData.append('pickup_time', document.getElementById('pickup_time').value);

        // Add coordinates if available
        const pickupLat = document.getElementById('pickup_lat').value;
        const pickupLng = document.getElementById('pickup_lng').value;
        const dropoffLat = document.getElementById('dropoff_lat').value;
        const dropoffLng = document.getElementById('dropoff_lng').value;

        if (pickupLat) formData.append('pickup_lat', pickupLat);
        if (pickupLng) formData.append('pickup_lng', pickupLng);
        if (dropoffLat) formData.append('dropoff_lat', dropoffLat);
        if (dropoffLng) formData.append('dropoff_lng', dropoffLng);

        // Add flight details for one way
        const flightNumber = document.getElementById('oneway_flight_number')?.value;
        const airline = document.getElementById('oneway_airline')?.value;
        const departureTime = document.getElementById('oneway_departure_time')?.value;
        const arrivalTime = document.getElementById('oneway_arrival_time')?.value;
        const terminal = document.getElementById('oneway_terminal')?.value;
        const flightStatus = document.getElementById('oneway_flight_status')?.value;
        const flightNotes = document.getElementById('oneway_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);

    } else if (currentBookingType === 'return') {
        formData.append('pickup_address', document.getElementById('return_pickup_address').value.trim());
        formData.append('dropoff_address', document.getElementById('return_dropoff_address').value.trim());
        formData.append('pickup_date', document.getElementById('pickup_date_return').value);
        formData.append('pickup_time', document.getElementById('pickup_time_return').value);
        formData.append('return_date', document.getElementById('return_date').value);
        formData.append('return_time', document.getElementById('return_time').value);

        // Add coordinates if available
        const pickupLat = document.getElementById('return_pickup_lat').value;
        const pickupLng = document.getElementById('return_pickup_lng').value;
        const dropoffLat = document.getElementById('return_dropoff_lat').value;
        const dropoffLng = document.getElementById('return_dropoff_lng').value;

        if (pickupLat) formData.append('pickup_lat', pickupLat);
        if (pickupLng) formData.append('pickup_lng', pickupLng);
        if (dropoffLat) formData.append('dropoff_lat', dropoffLat);
        if (dropoffLng) formData.append('dropoff_lng', dropoffLng);

        // Add flight details for return
        const flightNumber = document.getElementById('return_flight_number')?.value;
        const airline = document.getElementById('return_airline')?.value;
        const departureTime = document.getElementById('return_departure_time')?.value;
        const arrivalTime = document.getElementById('return_arrival_time')?.value;
        const terminal = document.getElementById('return_terminal')?.value;
        const flightStatus = document.getElementById('return_flight_status')?.value;
        const flightNotes = document.getElementById('return_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);

    } else if (currentBookingType === 'hourly') {
        formData.append('pickup_address', document.getElementById('hourly_pickup_address').value.trim());
        formData.append('pickup_date', document.getElementById('pickup_date_hourly').value);
        formData.append('pickup_time', document.getElementById('pickup_time_hourly').value);
        formData.append('duration_hours', document.getElementById('duration_hours').value);

        // Add coordinates if available
        const pickupLat = document.getElementById('hourly_pickup_lat').value;
        const pickupLng = document.getElementById('hourly_pickup_lng').value;

        if (pickupLat) formData.append('pickup_lat', pickupLat);
        if (pickupLng) formData.append('pickup_lng', pickupLng);

        // Add flight details for hourly
        const flightNumber = document.getElementById('hourly_flight_number')?.value;
        const airline = document.getElementById('hourly_airline')?.value;
        const departureTime = document.getElementById('hourly_departure_time')?.value;
        const arrivalTime = document.getElementById('hourly_arrival_time')?.value;
        const terminal = document.getElementById('hourly_terminal')?.value;
        const flightStatus = document.getElementById('hourly_flight_status')?.value;
        const flightNotes = document.getElementById('hourly_flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);

    } else if (currentBookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked').value;
        const airportId = document.getElementById('airport_id').value;

        formData.append('airport_direction', direction);
        formData.append('airport_id', airportId);

        if (direction === 'to_airport') {
            formData.append('pickup_address', document.getElementById('airport_pickup_address').value.trim());
            formData.append('pickup_date', document.getElementById('pickup_date_airport').value);
            formData.append('pickup_time', document.getElementById('pickup_time_airport').value);

            // Add coordinates if available
            const pickupLat = document.getElementById('airport_pickup_lat').value;
            const pickupLng = document.getElementById('airport_pickup_lng').value;

            if (pickupLat) formData.append('pickup_lat', pickupLat);
            if (pickupLng) formData.append('pickup_lng', pickupLng);

            // Get airport as dropoff
            const airportSelect = document.getElementById('airport_id');
            const selectedAirport = airportSelect.selectedOptions[0];
            if (selectedAirport) {
                formData.append('dropoff_address', selectedAirport.dataset.name);
                if (selectedAirport.dataset.lat) formData.append('dropoff_lat', selectedAirport.dataset.lat);
                if (selectedAirport.dataset.lng) formData.append('dropoff_lng', selectedAirport.dataset.lng);
                // Add airport-specific fields
                formData.append('dropoff_airport_id', airportId);
            }
        } else {
            formData.append('dropoff_address', document.getElementById('airport_dropoff_address').value.trim());
            formData.append('pickup_date', document.getElementById('pickup_date_airport_from').value);
            formData.append('pickup_time', document.getElementById('pickup_time_airport_from').value);

            // Add coordinates if available
            const dropoffLat = document.getElementById('airport_dropoff_lat').value;
            const dropoffLng = document.getElementById('airport_dropoff_lng').value;

            if (dropoffLat) formData.append('dropoff_lat', dropoffLat);
            if (dropoffLng) formData.append('dropoff_lng', dropoffLng);

            // Get airport as pickup
            const airportSelect = document.getElementById('airport_id');
            const selectedAirport = airportSelect.selectedOptions[0];
            if (selectedAirport) {
                formData.append('pickup_address', selectedAirport.dataset.name);
                if (selectedAirport.dataset.lat) formData.append('pickup_lat', selectedAirport.dataset.lat);
                if (selectedAirport.dataset.lng) formData.append('pickup_lng', selectedAirport.dataset.lng);
                // Add airport-specific fields
                formData.append('pickup_airport_id', airportId);
            }
        }

        // Add flight details for airport transfers
        const flightNumber = document.getElementById('flight_number')?.value;
        const airline = document.getElementById('airline')?.value;
        const departureTime = document.getElementById('departure_time')?.value;
        const arrivalTime = document.getElementById('arrival_time')?.value;
        const terminal = document.getElementById('terminal')?.value;
        const flightStatus = document.getElementById('flight_status')?.value;
        const flightNotes = document.getElementById('flight_notes')?.value;

        if (flightNumber) formData.append('flight_number', flightNumber);
        if (airline) formData.append('airline', airline);
        if (departureTime) formData.append('departure_time', departureTime);
        if (arrivalTime) formData.append('arrival_time', arrivalTime);
        if (terminal) formData.append('terminal', terminal);
        if (flightStatus) formData.append('flight_status', flightStatus);
        if (flightNotes) formData.append('flight_notes', flightNotes);
    }

    // Add extra services
    const meetAndGreet = document.getElementById('meet_and_greet').checked;
    const childSeat = document.getElementById('child_seat').checked;
    const wheelchairAccessible = document.getElementById('wheelchair_accessible').checked;
    const extraLuggage = document.getElementById('extra_luggage').checked;

    formData.append('meet_and_greet', meetAndGreet ? '1' : '0');
    formData.append('child_seat', childSeat ? '1' : '0');
    formData.append('wheelchair_accessible', wheelchairAccessible ? '1' : '0');
    formData.append('extra_luggage', extraLuggage ? '1' : '0');

    // Add child seat type if child seat is selected
    if (childSeat) {
        const childSeatType = document.getElementById('child_seat_type').value;
        if (childSeatType) {
            formData.append('child_seat_type', childSeatType);
        }
    }

    // Add special instructions
    const notes = document.getElementById('notes').value.trim();
    if (notes) {
        formData.append('notes', notes);
    }

    // Add calculated amount
    const amount = document.getElementById('amount').value;
    if (amount) {
        formData.append('amount', amount);
    }

    // Add distance and duration if available
    const distanceValue = document.getElementById('distance_value').value;
    const durationValue = document.getElementById('duration_value').value;
    if (distanceValue) formData.append('distance_value', distanceValue);
    if (durationValue) formData.append('duration_value', durationValue);

    return formData;
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set default values
    setMinimumDates();

    // Initialize client sections
    toggleClientSections('existing');

    // Initialize via stops functionality
    initializeViaStops();

    // Ensure default client type is selected
    const existingClientRadio = document.getElementById('existing_client');
    if (existingClientRadio && !document.querySelector('input[name="client_type"]:checked')) {
        existingClientRadio.checked = true;
    }

    // Initialize booking type forms
    toggleBookingTypeForms('one_way');

    // Add business info to page header if available
    addBusinessInfoToHeader();

    // Initialize Google Maps if API key is available
    if (window.googleMapsApiKey && typeof google !== 'undefined') {
        initializeMap();
        initializeAutocomplete();
    }

    // Initialize form handlers
    initializeFormHandlers();
});

function addBusinessInfoToHeader() {
    const businessName = window.businessSettings ? window.businessSettings.name : '{{ $businessName }}';
    const businessPhone = window.businessSettings ? window.businessSettings.phone : '{{ $businessPhone }}';

    if (businessName && businessName !== 'Transportation Service') {
        const pageTitle = document.querySelector('h1');
        if (pageTitle) {
            pageTitle.innerHTML = `<i class="fas fa-plus-circle me-2"></i>Create New Booking - ${businessName}`;
        }
    }

    if (businessPhone) {
        const headerActions = document.querySelector('.d-flex.gap-2');
        if (headerActions) {
            const phoneButton = document.createElement('a');
            phoneButton.href = `tel:${businessPhone}`;
            phoneButton.className = 'btn btn-outline-primary';
            phoneButton.innerHTML = `<i class="fas fa-phone me-1"></i> ${businessPhone}`;
            headerActions.appendChild(phoneButton);
        }
    }
}

// Missing utility functions
function applyAutoDriverAssignmentSetting() {
    const autoAssignDriver = window.bookingSettings ? window.bookingSettings.autoAssignDriver : false;
    const assignDriverCheckbox = document.getElementById('assign_driver');

    if (assignDriverCheckbox && autoAssignDriver) {
        assignDriverCheckbox.checked = true;
        toggleDriverSelection(true);
    }
}

function setMinimumDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const minDate = tomorrow.toISOString().split('T')[0];

    // Set minimum dates for all date inputs
    const dateInputs = [
        'pickup_date', 'pickup_date_return', 'pickup_date_hourly',
        'pickup_date_airport', 'pickup_date_airport_from', 'return_date'
    ];

    dateInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.min = minDate;
        }
    });
}

function toggleBookingTypeForms(bookingType) {
    // Hide all forms first
    document.querySelectorAll('.booking-type-form').forEach(form => {
        form.classList.add('d-none');
        // Remove required attributes from hidden forms
        form.querySelectorAll('[data-required]').forEach(input => {
            input.removeAttribute('required');
        });
    });

    // Show selected form
    const selectedForm = document.getElementById(`${bookingType.replace('_', '-')}-form`);
    if (selectedForm) {
        selectedForm.classList.remove('d-none');
        // Add required attributes to visible form
        selectedForm.querySelectorAll('[data-required]').forEach(input => {
            input.setAttribute('required', 'required');
        });
    }

    // Special handling for airport transfer
    if (bookingType === 'airport_transfer') {
        const direction = document.querySelector('input[name="airport_direction"]:checked')?.value || 'to_airport';
        toggleAirportForms(direction);
    }
}

function toggleAirportForms(direction) {
    const toAirportForm = document.getElementById('to-airport-form');
    const fromAirportForm = document.getElementById('from-airport-form');

    if (direction === 'to_airport') {
        toAirportForm.classList.remove('d-none');
        fromAirportForm.classList.add('d-none');
    } else {
        toAirportForm.classList.add('d-none');
        fromAirportForm.classList.remove('d-none');
    }
}

function updateAirportDisplays(selectElement) {
    const selectedOption = selectElement.selectedOptions[0];
    if (!selectedOption) return;

    const airportName = selectedOption.dataset.name;
    const airportCode = selectedOption.dataset.code;
    const displayText = `${airportName} (${airportCode})`;

    // Update display fields
    const pickupDisplay = document.getElementById('airport_pickup_display');
    const dropoffDisplay = document.getElementById('airport_dropoff_display');

    if (pickupDisplay) pickupDisplay.value = displayText;
    if (dropoffDisplay) dropoffDisplay.value = displayText;
}

function selectVehicle(cardElement) {
    // Remove selection from all vehicle cards
    document.querySelectorAll('.vehicle-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selection to clicked card
    cardElement.classList.add('selected');

    // Set the hidden radio button
    const vehicleId = cardElement.dataset.vehicleId;
    const radioButton = document.querySelector(`input[name="vehicle_id"][value="${vehicleId}"]`);
    if (radioButton) {
        radioButton.checked = true;
    }

    // Trigger fare calculation
    calculateFareAjax();
}

function toggleDriverSelection(enabled) {
    const driverSection = document.getElementById('driver-assignment-section');
    if (driverSection) {
        if (enabled) {
            driverSection.classList.remove('d-none');
        } else {
            driverSection.classList.add('d-none');
            // Clear driver selection
            document.querySelectorAll('input[name="driver_id"]').forEach(radio => {
                radio.checked = false;
            });
            document.querySelectorAll('.driver-card').forEach(card => {
                card.classList.remove('selected');
            });
        }
    }
}

function selectDriver(cardElement) {
    // Remove selection from all driver cards
    document.querySelectorAll('.driver-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selection to clicked card
    cardElement.classList.add('selected');

    // Set the hidden radio button
    const driverId = cardElement.dataset.driverId;
    const radioButton = document.querySelector(`input[name="driver_id"][value="${driverId}"]`);
    if (radioButton) {
        radioButton.checked = true;
    }
}



function filterDrivers() {
    const ratingFilter = document.getElementById('driver_rating_filter').value;
    const experienceFilter = document.getElementById('driver_experience_filter').value;
    const searchFilter = document.getElementById('driver_search').value.toLowerCase();

    document.querySelectorAll('.driver-option').forEach(option => {
        let show = true;

        // Rating filter
        if (ratingFilter) {
            const rating = parseFloat(option.dataset.rating || 0);
            if (rating < parseFloat(ratingFilter)) {
                show = false;
            }
        }

        // Experience filter
        if (experienceFilter) {
            const experience = parseInt(option.dataset.experience || 0);
            if (experience < parseInt(experienceFilter)) {
                show = false;
            }
        }

        // Search filter
        if (searchFilter) {
            const name = option.dataset.name || '';
            if (!name.includes(searchFilter)) {
                show = false;
            }
        }

        option.style.display = show ? 'block' : 'none';
    });
}

// resetForm function moved to avoid duplication - see comprehensive version above

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container');
    const alertId = 'alert-' + Date.now();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    alertContainer.insertAdjacentHTML('beforeend', alertHtml);

    // Auto-dismiss success alerts after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    const alertContainer = document.getElementById('alert-container');
    alertContainer.innerHTML = '';
}

// Via Stops Functionality
let viaStopsData = {
    oneWay: [],
    return: [],
    airport: []
};

function initializeViaStops() {
    // Add event listeners for via stop buttons
    document.getElementById('addOneWayViaStopBtn')?.addEventListener('click', () => addViaStop('oneWay'));
    document.getElementById('addReturnViaStopBtn')?.addEventListener('click', () => addViaStop('return'));
    document.getElementById('addAirportViaStopBtn')?.addEventListener('click', () => addViaStop('airport'));
}

function addViaStop(type) {
    const containers = {
        oneWay: 'oneWayViaStopsContainer',
        return: 'returnViaStopsContainer',
        airport: 'airportViaStopsContainer'
    };

    const counters = {
        oneWay: 'currentOneWayViaStops',
        return: 'currentReturnViaStops',
        airport: 'currentAirportViaStops'
    };

    const container = document.getElementById(containers[type]);
    const counter = document.getElementById(counters[type]);

    if (!container || !counter) return;

    const currentCount = viaStopsData[type].length;

    if (currentCount >= 5) {
        showAlert('Maximum 5 via stops allowed per journey.', 'warning');
        return;
    }

    const stopIndex = currentCount;
    const stopId = `${type}ViaStop${stopIndex}`;

    const viaStopHtml = `
        <div class="via-stop-item" id="${stopId}" data-index="${stopIndex}">
            <div class="via-stop-number">${stopIndex + 1}</div>
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-map-marker-alt"></i>
                </span>
                <input type="text"
                       class="form-control via-stop-address"
                       name="via_stops[${stopIndex}][address]"
                       placeholder="Enter via stop address"
                       data-type="${type}"
                       data-index="${stopIndex}"
                       autocomplete="off">
                <button type="button" class="btn btn-outline-danger" onclick="removeViaStop('${type}', ${stopIndex})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <input type="hidden" name="via_stops[${stopIndex}][lat]" class="via-stop-lat">
            <input type="hidden" name="via_stops[${stopIndex}][lng]" class="via-stop-lng">
        </div>
    `;

    container.insertAdjacentHTML('beforeend', viaStopHtml);

    // Initialize autocomplete for the new input
    const newInput = container.querySelector(`#${stopId} .via-stop-address`);
    if (newInput && window.autocompleteSettings?.enabled) {
        initializeAddressAutocomplete(newInput);
    }

    // Update data and counter
    viaStopsData[type].push({
        address: '',
        lat: null,
        lng: null
    });

    updateViaStopCounter(type);
    updateFareCalculation();
}

function removeViaStop(type, index) {
    const stopId = `${type}ViaStop${index}`;
    const stopElement = document.getElementById(stopId);

    if (!stopElement) return;

    // Add removing animation
    stopElement.classList.add('via-stop-removing');

    setTimeout(() => {
        stopElement.remove();

        // Remove from data array
        viaStopsData[type].splice(index, 1);

        // Renumber remaining stops
        renumberViaStops(type);
        updateViaStopCounter(type);
        updateFareCalculation();
    }, 300);
}

function renumberViaStops(type) {
    const containers = {
        oneWay: 'oneWayViaStopsContainer',
        return: 'returnViaStopsContainer',
        airport: 'airportViaStopsContainer'
    };

    const container = document.getElementById(containers[type]);
    if (!container) return;

    const stops = container.querySelectorAll('.via-stop-item');
    stops.forEach((stop, index) => {
        // Update stop number
        const numberElement = stop.querySelector('.via-stop-number');
        if (numberElement) {
            numberElement.textContent = index + 1;
        }

        // Update IDs and data attributes
        stop.id = `${type}ViaStop${index}`;
        stop.dataset.index = index;

        // Update input names
        const addressInput = stop.querySelector('.via-stop-address');
        const latInput = stop.querySelector('.via-stop-lat');
        const lngInput = stop.querySelector('.via-stop-lng');

        if (addressInput) {
            addressInput.name = `via_stops[${index}][address]`;
            addressInput.dataset.index = index;
        }
        if (latInput) latInput.name = `via_stops[${index}][lat]`;
        if (lngInput) lngInput.name = `via_stops[${index}][lng]`;

        // Update remove button
        const removeBtn = stop.querySelector('.btn-outline-danger');
        if (removeBtn) {
            removeBtn.setAttribute('onclick', `removeViaStop('${type}', ${index})`);
        }
    });
}

function updateViaStopCounter(type) {
    const counters = {
        oneWay: 'currentOneWayViaStops',
        return: 'currentReturnViaStops',
        airport: 'currentAirportViaStops'
    };

    const counter = document.getElementById(counters[type]);
    if (counter) {
        counter.textContent = viaStopsData[type].length;
    }
}

function initializeAddressAutocomplete(input) {
    if (!window.google || !window.google.maps || !window.google.maps.places) {
        console.warn('Google Maps Places API not loaded');
        return;
    }

    const autocomplete = new google.maps.places.Autocomplete(input, {
        types: ['address'],
        componentRestrictions: window.autocompleteSettings.restrictCountry ?
            { country: window.autocompleteSettings.country } : undefined
    });

    autocomplete.addListener('place_changed', function() {
        const place = autocomplete.getPlace();
        const type = input.dataset.type;
        const index = parseInt(input.dataset.index);

        if (place.geometry) {
            // Update data
            viaStopsData[type][index] = {
                address: place.formatted_address || input.value,
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
            };

            // Update hidden inputs
            const stopElement = input.closest('.via-stop-item');
            const latInput = stopElement.querySelector('.via-stop-lat');
            const lngInput = stopElement.querySelector('.via-stop-lng');

            if (latInput) latInput.value = place.geometry.location.lat();
            if (lngInput) lngInput.value = place.geometry.location.lng();

            updateFareCalculation();
        }
    });
}

// End of JavaScript
</script>
@endsection