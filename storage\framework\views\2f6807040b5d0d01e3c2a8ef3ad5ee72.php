<?php $__env->startSection('title', 'Sent Emails'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>📧 Sent Emails</h2>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('admin.email.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Email Management
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.email.sent')); ?>" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Email Type</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">All Types</option>
                                <option value="booking_confirmation" <?php echo e(request('type') === 'booking_confirmation' ? 'selected' : ''); ?>>Booking Confirmation</option>
                                <option value="payment_receipt" <?php echo e(request('type') === 'payment_receipt' ? 'selected' : ''); ?>>Payment Receipt</option>
                                <option value="booking_reminder" <?php echo e(request('type') === 'booking_reminder' ? 'selected' : ''); ?>>Booking Reminder</option>
                                <option value="welcome_client" <?php echo e(request('type') === 'welcome_client' ? 'selected' : ''); ?>>Welcome Client</option>
                                <option value="welcome_driver" <?php echo e(request('type') === 'welcome_driver' ? 'selected' : ''); ?>>Welcome Driver</option>
                                <option value="contact_form" <?php echo e(request('type') === 'contact_form' ? 'selected' : ''); ?>>Contact Form</option>
                                <option value="test_email" <?php echo e(request('type') === 'test_email' ? 'selected' : ''); ?>>Test Email</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="sent" <?php echo e(request('status') === 'sent' ? 'selected' : ''); ?>>Sent</option>
                                <option value="delivered" <?php echo e(request('status') === 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                                <option value="failed" <?php echo e(request('status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                                <option value="bounced" <?php echo e(request('status') === 'bounced' ? 'selected' : ''); ?>>Bounced</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by email or subject..." value="<?php echo e(request('search')); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="<?php echo e(route('admin.email.sent')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sent Emails Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Sent Emails (<?php echo e($sentEmails->total()); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if($sentEmails->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>To</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Sent At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $sentEmails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $email): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' ', $email->type))); ?></span>
                                            </td>
                                            <td><?php echo e($email->to_email); ?></td>
                                            <td><?php echo e(Str::limit($email->subject, 50)); ?></td>
                                            <td>
                                                <span class="badge <?php echo e($email->status_badge_class); ?>">
                                                    <?php echo e(ucfirst($email->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($email->sent_at): ?>
                                                    <?php echo e($email->sent_at->format('M j, Y g:i A')); ?>

                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if($email->status === 'failed'): ?>
                                                        <button type="button" class="btn btn-outline-warning" 
                                                                onclick="retryEmail(<?php echo e($email->id); ?>)"
                                                                title="Retry sending">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button type="button" class="btn btn-outline-info" 
                                                            onclick="viewEmailDetails(<?php echo e($email->id); ?>)"
                                                            title="View details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($sentEmails->appends(request()->query())->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sent emails found</h5>
                            <p class="text-muted">No emails match your current filters.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Details Modal -->
<div class="modal fade" id="emailDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="emailDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function viewEmailDetails(emailId) {
    // Load email details via AJAX
    fetch(`/admin/email/logs/${emailId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('emailDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('emailDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error loading email details:', error);
            alert('Failed to load email details');
        });
}

function retryEmail(emailId) {
    if (confirm('Are you sure you want to retry sending this email?')) {
        fetch(`/admin/email/logs/${emailId}/retry`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email queued for retry');
                location.reload();
            } else {
                alert('Failed to retry email: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error retrying email:', error);
            alert('Failed to retry email');
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\YnrCars\resources\views/admin/email/sent.blade.php ENDPATH**/ ?>