<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradientWhite" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle for Y -->
  <circle cx="25" cy="30" r="22" fill="url(#logoGradientWhite)" opacity="0.1"/>
  
  <!-- Y Letter -->
  <path d="M15 15 L25 25 L35 15 M25 25 L25 45" stroke="#ffffff" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- N Letter -->
  <path d="M50 15 L50 45 M50 15 L70 45 M70 15 L70 45" stroke="#ffffff" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- R Letter -->
  <path d="M85 15 L85 45 M85 15 L95 15 Q105 15 105 25 Q105 30 100 30 L85 30 M95 30 L105 45" stroke="#ffffff" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Cars Text -->
  <text x="125" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#ffffff">Cars</text>
  
  <!-- Underline -->
  <line x1="15" y1="52" x2="185" y2="52" stroke="url(#logoGradientWhite)" stroke-width="2"/>
  
  <!-- Small car icon -->
  <g transform="translate(160, 15)">
    <rect x="0" y="8" width="25" height="8" rx="2" fill="#ffffff" opacity="0.7"/>
    <circle cx="5" cy="18" r="3" fill="#f8f9fa"/>
    <circle cx="20" cy="18" r="3" fill="#f8f9fa"/>
    <rect x="2" y="5" width="6" height="3" rx="1" fill="#ffffff"/>
  </g>
</svg>
