<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('incoming_calls', function (Blueprint $table) {
            $table->id();
            
            // CircleLoop call data
            $table->string('call_id')->unique(); // CircleLoop call ID
            $table->string('user_id')->nullable(); // CircleLoop user ID
            $table->enum('event_type', ['call_ringing', 'call_missed', 'call_answered'])->default('call_ringing');
            
            // Phone numbers
            $table->string('caller_number'); // Original format from CircleLoop
            $table->string('caller_number_e164')->nullable(); // E164 format
            $table->string('dialled_number')->nullable(); // Number that was dialed
            
            // Client identification
            $table->foreignId('client_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('client_name')->nullable(); // Cached client name
            $table->string('client_email')->nullable(); // Cached client email
            
            // Call status and handling
            $table->enum('status', ['ringing', 'answered', 'missed', 'handled'])->default('ringing');
            $table->text('notes')->nullable(); // Admin notes about the call
            $table->foreignId('handled_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('handled_at')->nullable();
            
            // Follow-up tracking
            $table->boolean('follow_up_required')->default(false);
            $table->timestamp('follow_up_at')->nullable();
            
            // Additional metadata
            $table->json('metadata')->nullable(); // Store additional CircleLoop data
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['caller_number', 'created_at']);
            $table->index(['client_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['event_type', 'created_at']);
            $table->index(['follow_up_required', 'follow_up_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('incoming_calls');
    }
};
