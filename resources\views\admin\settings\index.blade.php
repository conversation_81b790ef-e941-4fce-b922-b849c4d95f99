@extends('layouts.admin')

@section('title', 'Settings')

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jsvectormap@1.5.3/dist/css/jsvectormap.min.css">
<style>
    .content-wrapper {
        padding: 20px;
    }

    /* Map styles */
    .jvm-tooltip {
        background-color: #343a40;
        color: #fff;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
    }

    .jvm-zoom-btn {
        background-color: #fff;
        border: 1px solid #dee2e6;
        color: #343a40;
        width: 30px;
        height: 30px;
        border-radius: 4px;
        line-height: 30px;
        text-align: center;
        font-size: 16px;
        margin-bottom: 5px;
    }

    .jvm-zoom-btn:hover {
        background-color: #f8f9fa;
    }

    .jvm-series-container {
        display: none;
    }

    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: calc(100vh - 76px);
        padding-top: 20px;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 10px 20px;
        margin-bottom: 5px;
        border-radius: 5px;
    }

    .sidebar .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar .nav-link.active {
        color: #fff;
        background-color: #ee393d;
    }

    .sidebar .nav-link i {
        margin-right: 10px;
    }

    .settings-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .settings-card .card-header {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 20px;
    }

    .settings-card .card-body {
        padding: 30px;
    }

    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 20px;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        padding: 10px 20px;
        margin-right: 5px;
        border-radius: 0;
        font-weight: 600;
    }

    .nav-tabs .nav-link:hover {
        color: #343a40;
        border-bottom: 2px solid #dee2e6;
    }

    .nav-tabs .nav-link.active {
        color: #ee393d;
        border-bottom: 2px solid #ee393d;
    }

    .tab-content {
        padding: 20px 0;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
    }

    .form-control {
        border-radius: 5px;
        padding: 10px 15px;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-12 content-wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Settings</h2>
            </div>

            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="card settings-card">
                <div class="card-header">
                    <h4 class="mb-0">Application Settings</h4>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab" aria-controls="company" aria-selected="true">
                                <i class="fas fa-building me-2"></i> Company
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab" aria-controls="pricing" aria-selected="false">
                                <i class="fas fa-dollar-sign me-2"></i> Pricing
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="localization-tab" data-bs-toggle="tab" data-bs-target="#localization" type="button" role="tab" aria-controls="localization" aria-selected="false">
                                <i class="fas fa-globe me-2"></i> Localization
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="booking-tab" data-bs-toggle="tab" data-bs-target="#booking" type="button" role="tab" aria-controls="booking" aria-selected="false">
                                <i class="fas fa-calendar-alt me-2"></i> Booking
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="extra-services-tab" data-bs-toggle="tab" data-bs-target="#extra-services" type="button" role="tab" aria-controls="extra-services" aria-selected="false">
                                <i class="fas fa-plus-circle me-2"></i> Extra Services
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="driver-tab" data-bs-toggle="tab" data-bs-target="#driver" type="button" role="tab" aria-controls="driver" aria-selected="false">
                                <i class="fas fa-id-card me-2"></i> Driver
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="api-tab" data-bs-toggle="tab" data-bs-target="#api" type="button" role="tab" aria-controls="api" aria-selected="false">
                                <i class="fas fa-key me-2"></i> API Keys
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="autocomplete-tab" data-bs-toggle="tab" data-bs-target="#autocomplete" type="button" role="tab" aria-controls="autocomplete" aria-selected="false">
                                <i class="fas fa-search-location me-2"></i> Autocomplete
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab" aria-controls="payment" aria-selected="false">
                                <i class="fas fa-credit-card me-2"></i> Payment
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab" aria-controls="email" aria-selected="false">
                                <i class="fas fa-envelope me-2"></i> Email
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab" aria-controls="appearance" aria-selected="false">
                                <i class="fas fa-palette me-2"></i> Appearance
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab" aria-controls="social" aria-selected="false">
                                <i class="fas fa-share-alt me-2"></i> Social Media
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                                <i class="fas fa-file-alt me-2"></i> Content
                            </button>
                        </li>
                    </ul>

                    <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="tab-content" id="settingsTabsContent">
                            <!-- Company Tab -->
                            <div class="tab-pane fade show active" id="company" role="tabpanel" aria-labelledby="company-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_name" class="form-label">Company Name</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" value="{{ isset($settingsCollection) && isset($settingsCollection['company_name']) ? $settingsCollection['company_name']->value : '' }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_email" class="form-label">Company Email</label>
                                            <input type="email" class="form-control" id="company_email" name="company_email" value="{{ isset($settingsCollection) && isset($settingsCollection['company_email']) ? $settingsCollection['company_email']->value : '' }}" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_phone" class="form-label">Company Phone</label>
                                            <input type="text" class="form-control" id="company_phone" name="company_phone" value="{{ isset($settingsCollection) && isset($settingsCollection['company_phone']) ? $settingsCollection['company_phone']->value : '' }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company_address" class="form-label">Company Address</label>
                                            <input type="text" class="form-control" id="company_address" name="company_address" value="{{ isset($settingsCollection) && isset($settingsCollection['company_address']) ? $settingsCollection['company_address']->value : '' }}" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Tab -->
                            <div class="tab-pane fade" id="pricing" role="tabpanel" aria-labelledby="pricing-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="currency_code" class="form-label">Currency</label>
                                            <select class="form-select" id="currency_code" name="currency_code" required>
                                                <option value="USD" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'USD') ? 'selected' : '' }}>US Dollar ($)</option>
                                                <option value="EUR" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'EUR') ? 'selected' : '' }}>Euro (€)</option>
                                                <option value="GBP" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'GBP') ? 'selected' : '' }}>British Pound (£)</option>
                                                <option value="CAD" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'CAD') ? 'selected' : '' }}>Canadian Dollar (C$)</option>
                                                <option value="AUD" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'AUD') ? 'selected' : '' }}>Australian Dollar (A$)</option>
                                                <option value="JPY" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'JPY') ? 'selected' : '' }}>Japanese Yen (¥)</option>
                                                <option value="INR" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'INR') ? 'selected' : '' }}>Indian Rupee (₹)</option>
                                                <option value="CNY" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'CNY') ? 'selected' : '' }}>Chinese Yuan (¥)</option>
                                                <option value="AED" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'AED') ? 'selected' : '' }}>UAE Dirham (د.إ)</option>
                                                <option value="SAR" {{ (isset($settingsCollection) && isset($settingsCollection['currency_code']) && $settingsCollection['currency_code']->value == 'SAR') ? 'selected' : '' }}>Saudi Riyal (﷼)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="{{ isset($settingsCollection) && isset($settingsCollection['currency_symbol']) ? $settingsCollection['currency_symbol']->value : '$' }}" required>
                                            <small class="form-text">Symbol to display before prices (e.g., $, €, £)</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Tax Rate, Base Fare, and Price per Kilometer settings have been moved to individual vehicle settings.
                                    Please configure these values for each vehicle in the <a href="{{ route('admin.vehicles.index') }}">Manage Vehicles</a> section.
                                </div>
                            </div>

                            <!-- Localization Tab -->
                            <div class="tab-pane fade" id="localization" role="tabpanel" aria-labelledby="localization-tab">
                                <h5 class="mb-4">Country Settings</h5>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="country_code" class="form-label">Country</label>
                                            <select class="form-select" id="country_code" name="country_code" required>
                                                <option value="US" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'US') ? 'selected' : '' }}>United States</option>
                                                <option value="GB" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'GB') ? 'selected' : '' }}>United Kingdom</option>
                                                <option value="CA" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'CA') ? 'selected' : '' }}>Canada</option>
                                                <option value="AU" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'AU') ? 'selected' : '' }}>Australia</option>
                                                <option value="DE" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'DE') ? 'selected' : '' }}>Germany</option>
                                                <option value="FR" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'FR') ? 'selected' : '' }}>France</option>
                                                <option value="IT" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'IT') ? 'selected' : '' }}>Italy</option>
                                                <option value="ES" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'ES') ? 'selected' : '' }}>Spain</option>
                                                <option value="IN" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'IN') ? 'selected' : '' }}>India</option>
                                                <option value="AE" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'AE') ? 'selected' : '' }}>United Arab Emirates</option>
                                                <option value="SA" {{ (isset($settingsCollection) && isset($settingsCollection['country_code']) && $settingsCollection['country_code']->value == 'SA') ? 'selected' : '' }}>Saudi Arabia</option>
                                            </select>
                                            <small class="form-text">Primary country for operations</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="distance_unit" class="form-label">Distance Unit</label>
                                            <select class="form-select" id="distance_unit" name="distance_unit" required>
                                                <option value="miles" {{ (isset($settingsCollection) && isset($settingsCollection['distance_unit']) && $settingsCollection['distance_unit']->value == 'miles') ? 'selected' : '' }}>Miles</option>
                                                <option value="kilometers" {{ (isset($settingsCollection) && isset($settingsCollection['distance_unit']) && $settingsCollection['distance_unit']->value == 'kilometers') ? 'selected' : '' }}>Kilometers</option>
                                            </select>
                                            <small class="form-text">Unit to use for displaying distances</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Select Country on Map</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted mb-3">Click on a country to select it. The country code will be automatically updated in the dropdown above.</p>
                                        <div id="country-map" style="height: 400px; width: 100%; border-radius: 8px;"></div>
                                        <input type="hidden" id="selected_country_name" name="selected_country_name" value="{{ isset($settingsCollection) && isset($settingsCollection['selected_country_name']) ? $settingsCollection['selected_country_name']->value : '' }}">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="timezone" class="form-label">Default Timezone</label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="UTC" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'UTC') ? 'selected' : '' }}>UTC</option>
                                                <option value="America/New_York" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'America/New_York') ? 'selected' : '' }}>Eastern Time (US & Canada)</option>
                                                <option value="America/Chicago" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'America/Chicago') ? 'selected' : '' }}>Central Time (US & Canada)</option>
                                                <option value="America/Denver" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'America/Denver') ? 'selected' : '' }}>Mountain Time (US & Canada)</option>
                                                <option value="America/Los_Angeles" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'America/Los_Angeles') ? 'selected' : '' }}>Pacific Time (US & Canada)</option>
                                                <option value="Europe/London" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'Europe/London') ? 'selected' : '' }}>London</option>
                                                <option value="Europe/Paris" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'Europe/Paris') ? 'selected' : '' }}>Paris</option>
                                                <option value="Asia/Dubai" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'Asia/Dubai') ? 'selected' : '' }}>Dubai</option>
                                                <option value="Asia/Kolkata" {{ (isset($settingsCollection) && isset($settingsCollection['timezone']) && $settingsCollection['timezone']->value == 'Asia/Kolkata') ? 'selected' : '' }}>India</option>
                                            </select>
                                            <small class="form-text">Default timezone for date and time display</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_format" class="form-label">Date Format</label>
                                            <select class="form-select" id="date_format" name="date_format">
                                                <option value="Y-m-d" {{ (isset($settingsCollection) && isset($settingsCollection['date_format']) && $settingsCollection['date_format']->value == 'Y-m-d') ? 'selected' : '' }}>YYYY-MM-DD (2023-12-31)</option>
                                                <option value="m/d/Y" {{ (isset($settingsCollection) && isset($settingsCollection['date_format']) && $settingsCollection['date_format']->value == 'm/d/Y') ? 'selected' : '' }}>MM/DD/YYYY (12/31/2023)</option>
                                                <option value="d/m/Y" {{ (isset($settingsCollection) && isset($settingsCollection['date_format']) && $settingsCollection['date_format']->value == 'd/m/Y') ? 'selected' : '' }}>DD/MM/YYYY (31/12/2023)</option>
                                                <option value="d.m.Y" {{ (isset($settingsCollection) && isset($settingsCollection['date_format']) && $settingsCollection['date_format']->value == 'd.m.Y') ? 'selected' : '' }}>DD.MM.YYYY (31.12.2023)</option>
                                            </select>
                                            <small class="form-text">Format for displaying dates throughout the application</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Autocomplete Tab -->
                            <div class="tab-pane fade" id="autocomplete" role="tabpanel" aria-labelledby="autocomplete-tab">
                                <h5 class="mb-4">Address Autocomplete Settings</h5>

                                <div class="alert alert-info mb-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    These settings control how address autocomplete works throughout the application. Proper configuration can improve user experience and reduce errors.
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="autocomplete_enabled" name="autocomplete_enabled" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_enabled']) && $settingsCollection['autocomplete_enabled']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="autocomplete_enabled">Enable Address Autocomplete</label>
                                            </div>
                                            <small class="form-text">Enable or disable address autocomplete functionality.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="autocomplete_restrict_country" name="autocomplete_restrict_country" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_restrict_country']) && $settingsCollection['autocomplete_restrict_country']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="autocomplete_restrict_country">Restrict to Country</label>
                                            </div>
                                            <small class="form-text">Restrict autocomplete results to the selected country.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="autocomplete_types" class="form-label">Place Types</label>
                                            <select class="form-select" id="autocomplete_types" name="autocomplete_types">
                                                <option value="geocode" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == 'geocode') || (!isset($settingsCollection['autocomplete_types'])) ? 'selected' : '' }}>Geocode (Recommended for Postal Codes)</option>
                                                <option value="address" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == 'address') ? 'selected' : '' }}>Address Only</option>
                                                <option value="establishment" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == 'establishment') ? 'selected' : '' }}>Establishments</option>
                                                <option value="(cities)" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == '(cities)') ? 'selected' : '' }}>Cities</option>
                                                <option value="(regions)" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == '(regions)') ? 'selected' : '' }}>Regions</option>
                                                <option value="" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_types']) && $settingsCollection['autocomplete_types']->value == '') ? 'selected' : '' }}>All Types</option>
                                            </select>
                                            <small class="form-text">Type of places to show in autocomplete results. <strong>Geocode</strong> is recommended for best postal code support.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="autocomplete_country" class="form-label">Country for Autocomplete</label>
                                            <select class="form-select" id="autocomplete_country" name="autocomplete_country" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_restrict_country']) && $settingsCollection['autocomplete_restrict_country']->value != 'true') ? 'disabled' : '' }}>
                                                <option value="">-- Select Country --</option>
                                                <option value="US" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'US') ? 'selected' : '' }}>United States</option>
                                                <option value="GB" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'GB') ? 'selected' : '' }}>United Kingdom</option>
                                                <option value="CA" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'CA') ? 'selected' : '' }}>Canada</option>
                                                <option value="AU" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'AU') ? 'selected' : '' }}>Australia</option>
                                                <option value="DE" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'DE') ? 'selected' : '' }}>Germany</option>
                                                <option value="FR" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'FR') ? 'selected' : '' }}>France</option>
                                                <option value="IT" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'IT') ? 'selected' : '' }}>Italy</option>
                                                <option value="ES" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'ES') ? 'selected' : '' }}>Spain</option>
                                                <option value="IN" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'IN') ? 'selected' : '' }}>India</option>
                                                <option value="AE" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'AE') ? 'selected' : '' }}>United Arab Emirates</option>
                                                <option value="SA" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_country']) && $settingsCollection['autocomplete_country']->value == 'SA') ? 'selected' : '' }}>Saudi Arabia</option>
                                            </select>
                                            <small class="form-text">Select the country to restrict autocomplete results to.</small>
                                            <button type="button" id="sync-autocomplete-country-btn" class="btn btn-sm btn-outline-secondary mt-2">
                                                <i class="fas fa-sync-alt me-1"></i> Sync with Main Country
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="autocomplete_bias_radius" class="form-label">Location Bias Radius (km)</label>
                                            <input type="number" class="form-control" id="autocomplete_bias_radius" name="autocomplete_bias_radius" value="{{ isset($settingsCollection) && isset($settingsCollection['autocomplete_bias_radius']) ? $settingsCollection['autocomplete_bias_radius']->value : '50' }}" min="0" max="500">
                                            <small class="form-text">Radius in kilometers to bias autocomplete results (0 = no bias).</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="autocomplete_use_strict_bounds" name="autocomplete_use_strict_bounds" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_use_strict_bounds']) && $settingsCollection['autocomplete_use_strict_bounds']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="autocomplete_use_strict_bounds">Use Strict Bounds</label>
                                            </div>
                                            <small class="form-text">If enabled, autocomplete will return only results within the specified radius.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            <label for="autocomplete_fields" class="form-label">Address Fields to Return</label>
                                            <select class="form-select" id="autocomplete_fields" name="autocomplete_fields">
                                                <option value="formatted_address" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_fields']) && $settingsCollection['autocomplete_fields']->value == 'formatted_address') ? 'selected' : '' }}>Formatted Address Only</option>
                                                <option value="address_components" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_fields']) && $settingsCollection['autocomplete_fields']->value == 'address_components') ? 'selected' : '' }}>Address Components</option>
                                                <option value="geometry" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_fields']) && $settingsCollection['autocomplete_fields']->value == 'geometry') ? 'selected' : '' }}>Geometry (Coordinates)</option>
                                                <option value="all" {{ (isset($settingsCollection) && isset($settingsCollection['autocomplete_fields']) && $settingsCollection['autocomplete_fields']->value == 'all') ? 'selected' : '' }}>All Fields</option>
                                            </select>
                                            <small class="form-text">Which address fields to return when a place is selected.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-lightbulb me-2"></i> Autocomplete Tips</h6>
                                        <ul class="mb-0">
                                            <li>Restricting to a country improves accuracy and reduces API costs</li>
                                            <li>Using "Address Only" for place types helps users enter valid addresses</li>
                                            <li>Location bias helps prioritize results near your business area</li>
                                            <li>Strict bounds is useful for local businesses that only serve a specific area</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- API Keys Tab -->
                            <div class="tab-pane fade" id="api" role="tabpanel" aria-labelledby="api-tab">
                                <h5 class="mb-4">Google Maps Settings</h5>

                                <!-- Troubleshooting Alert -->
                                <div class="alert alert-info" role="alert">
                                    <h6 class="alert-heading"><i class="fas fa-info-circle"></i> Google Maps API Setup Guide</h6>
                                    <p class="mb-2"><strong>Common Error:</strong> "ApiProjectMapError" usually indicates:</p>
                                    <ul class="mb-2">
                                        <li>Billing is not enabled on your Google Cloud project</li>
                                        <li>Required APIs are not enabled (Maps JavaScript API, Places API, Geocoding API, Directions API)</li>
                                        <li>API key restrictions are too strict</li>
                                        <li>API key is invalid or expired</li>
                                    </ul>
                                    <p class="mb-0">
                                        <strong>Quick Fix:</strong>
                                        <a href="https://console.cloud.google.com/billing" target="_blank" rel="noopener">Enable billing</a> →
                                        <a href="https://console.cloud.google.com/apis/library" target="_blank" rel="noopener">Enable APIs</a> →
                                        <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener">Create/Update API key</a>
                                    </p>
                                </div>

                                <div class="form-group mb-4">
                                    <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                                    <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key" value="{{ isset($settingsCollection) && isset($settingsCollection['google_maps_api_key']) ? $settingsCollection['google_maps_api_key']->value : '' }}" placeholder="Enter your Google Maps API key">
                                    <small class="form-text">
                                        <strong>Required for maps and autocomplete functionality.</strong><br>
                                        Get your API key from <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener">Google Cloud Console</a><br>
                                        <strong>Required APIs:</strong> Maps JavaScript API, Places API, Geocoding API, Directions API<br>
                                        <strong>Common Error:</strong> ApiProjectMapError usually means billing is not enabled or API restrictions are too strict.
                                    </small>
                                    @if(isset($settingsCollection) && isset($settingsCollection['google_maps_api_key']) && $settingsCollection['google_maps_api_key']->value)
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="test-api-key">
                                                <i class="fas fa-check-circle"></i> Test API Key
                                            </button>
                                            <span id="api-key-status" class="ms-2"></span>
                                        </div>
                                    @endif
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="google_maps_default_zoom" class="form-label">Default Map Zoom Level</label>
                                            <input type="number" class="form-control" id="google_maps_default_zoom" name="google_maps_default_zoom" value="{{ isset($settingsCollection) && isset($settingsCollection['google_maps_default_zoom']) ? $settingsCollection['google_maps_default_zoom']->value : '10' }}" min="1" max="20">
                                            <small class="form-text">Default zoom level for maps (1-20, where 1 is world view and 20 is building view).</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="google_maps_libraries" class="form-label">Google Maps Libraries</label>
                                            <input type="text" class="form-control" id="google_maps_libraries" name="google_maps_libraries" value="{{ isset($settingsCollection) && isset($settingsCollection['google_maps_libraries']) ? $settingsCollection['google_maps_libraries']->value : 'places,geometry,drawing' }}">
                                            <small class="form-text">Comma-separated list of Google Maps libraries to load (e.g., places,geometry,drawing).</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="google_maps_default_lat" class="form-label">Default Map Center (Latitude)</label>
                                            <input type="text" class="form-control" id="google_maps_default_lat" name="google_maps_default_lat" value="{{ isset($settingsCollection) && isset($settingsCollection['google_maps_default_lat']) ? $settingsCollection['google_maps_default_lat']->value : '51.5074' }}">
                                            <small class="form-text">Default latitude for map center (e.g., 51.5074 for London).</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="google_maps_default_lng" class="form-label">Default Map Center (Longitude)</label>
                                            <input type="text" class="form-control" id="google_maps_default_lng" name="google_maps_default_lng" value="{{ isset($settingsCollection) && isset($settingsCollection['google_maps_default_lng']) ? $settingsCollection['google_maps_default_lng']->value : '-0.1278' }}">
                                            <small class="form-text">Default longitude for map center (e.g., -0.1278 for London).</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="google_maps_use_distance_matrix" name="google_maps_use_distance_matrix" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_use_distance_matrix']) && $settingsCollection['google_maps_use_distance_matrix']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="google_maps_use_distance_matrix">Use Distance Matrix API</label>
                                            </div>
                                            <small class="form-text">Enable to use Google's Distance Matrix API for more accurate distance and duration calculations.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="google_maps_use_directions" name="google_maps_use_directions" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_use_directions']) && $settingsCollection['google_maps_use_directions']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="google_maps_use_directions">Use Directions API</label>
                                            </div>
                                            <small class="form-text">Enable to use Google's Directions API for route visualization and navigation.</small>
                                        </div>
                                    </div>
                                </div>

                                <h5 class="mt-4 mb-3">Google Maps Country Settings</h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="google_maps_restrict_country" name="google_maps_restrict_country" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_restrict_country']) && $settingsCollection['google_maps_restrict_country']->value == 'true') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="google_maps_restrict_country">Restrict to Country</label>
                                            </div>
                                            <small class="form-text">Enable to restrict Google Maps results to the selected country.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="google_maps_country_code" class="form-label">Country for Google Maps</label>
                                            <select class="form-select" id="google_maps_country_code" name="google_maps_country_code" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_restrict_country']) && $settingsCollection['google_maps_restrict_country']->value != 'true') ? 'disabled' : '' }}>
                                                <option value="">-- Select Country --</option>
                                                <option value="US" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'US') ? 'selected' : '' }}>United States</option>
                                                <option value="GB" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'GB') ? 'selected' : '' }}>United Kingdom</option>
                                                <option value="CA" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'CA') ? 'selected' : '' }}>Canada</option>
                                                <option value="AU" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'AU') ? 'selected' : '' }}>Australia</option>
                                                <option value="DE" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'DE') ? 'selected' : '' }}>Germany</option>
                                                <option value="FR" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'FR') ? 'selected' : '' }}>France</option>
                                                <option value="IT" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'IT') ? 'selected' : '' }}>Italy</option>
                                                <option value="ES" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'ES') ? 'selected' : '' }}>Spain</option>
                                                <option value="IN" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'IN') ? 'selected' : '' }}>India</option>
                                                <option value="AE" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'AE') ? 'selected' : '' }}>United Arab Emirates</option>
                                                <option value="SA" {{ (isset($settingsCollection) && isset($settingsCollection['google_maps_country_code']) && $settingsCollection['google_maps_country_code']->value == 'SA') ? 'selected' : '' }}>Saudi Arabia</option>
                                            </select>
                                            <small class="form-text">Select the country to restrict Google Maps results to. This improves address autocomplete accuracy.</small>
                                            <button type="button" id="sync-country-btn" class="btn btn-sm btn-outline-secondary mt-2">
                                                <i class="fas fa-sync-alt me-1"></i> Sync with Main Country
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-map-marker-alt me-2"></i> Country Restriction Benefits</h6>
                                        <ul class="mb-0">
                                            <li>Improves address autocomplete accuracy by prioritizing results from the selected country</li>
                                            <li>Reduces API costs by limiting the search area</li>
                                            <li>Provides more relevant search results for your users</li>
                                            <li>Helps prevent address selection errors</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i> Google Maps APIs are billed based on usage. Make sure to set up billing and quotas in your Google Cloud Console to avoid service interruptions.
                                </div>
                            </div>

                            <!-- Payment Tab -->
                            <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
                                <div class="form-group">
                                    <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                    <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" value="{{ isset($settingsCollection) && isset($settingsCollection['paypal_client_id']) ? $settingsCollection['paypal_client_id']->value : '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="paypal_secret" class="form-label">PayPal Secret</label>
                                    <input type="password" class="form-control" id="paypal_secret" name="paypal_secret" value="{{ isset($settingsCollection) && isset($settingsCollection['paypal_secret']) ? $settingsCollection['paypal_secret']->value : '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="paypal_mode" class="form-label">PayPal Mode</label>
                                    <select class="form-select" id="paypal_mode" name="paypal_mode" required>
                                        <option value="sandbox" {{ (isset($settingsCollection) && isset($settingsCollection['paypal_mode']) ? $settingsCollection['paypal_mode']->value : 'sandbox') == 'sandbox' ? 'selected' : '' }}>Sandbox (Testing)</option>
                                        <option value="live" {{ (isset($settingsCollection) && isset($settingsCollection['paypal_mode']) ? $settingsCollection['paypal_mode']->value : '') == 'live' ? 'selected' : '' }}>Live (Production)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Social Media Tab -->
                            <div class="tab-pane fade" id="social" role="tabpanel" aria-labelledby="social-tab">
                                <div class="form-group">
                                    <label for="facebook_url" class="form-label">Facebook URL</label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url" value="{{ isset($settingsCollection) && isset($settingsCollection['facebook_url']) ? $settingsCollection['facebook_url']->value : '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="twitter_url" class="form-label">Twitter URL</label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url" value="{{ isset($settingsCollection) && isset($settingsCollection['twitter_url']) ? $settingsCollection['twitter_url']->value : '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="instagram_url" class="form-label">Instagram URL</label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url" value="{{ isset($settingsCollection) && isset($settingsCollection['instagram_url']) ? $settingsCollection['instagram_url']->value : '' }}">
                                </div>

                                <div class="form-group">
                                    <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                                    <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" value="{{ isset($settingsCollection) && isset($settingsCollection['linkedin_url']) ? $settingsCollection['linkedin_url']->value : '' }}">
                                </div>
                            </div>

                            <!-- Booking Tab -->
                            <div class="tab-pane fade" id="booking" role="tabpanel" aria-labelledby="booking-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="advance_booking_time" class="form-label">Advance Booking Time (days)</label>
                                            <input type="number" class="form-control" id="advance_booking_time" name="advance_booking_time" value="{{ isset($settingsCollection) && isset($settingsCollection['advance_booking_time']) ? $settingsCollection['advance_booking_time']->value : '30' }}" min="1" max="90" required>
                                            <small class="form-text">How many days in advance can users book a ride.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="minimum_hourly_duration" class="form-label">Minimum Hourly Duration</label>
                                            <input type="number" class="form-control" id="minimum_hourly_duration" name="minimum_hourly_duration" value="{{ isset($settingsCollection) && isset($settingsCollection['minimum_hourly_duration']) ? $settingsCollection['minimum_hourly_duration']->value : '2' }}" min="1" max="12" required>
                                            <small class="form-text">Minimum number of hours for hourly bookings.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="cancellation_time_window" class="form-label">Cancellation Time Window (hours)</label>
                                            <input type="number" class="form-control" id="cancellation_time_window" name="cancellation_time_window" value="{{ isset($settingsCollection) && isset($settingsCollection['cancellation_time_window']) ? $settingsCollection['cancellation_time_window']->value : '24' }}" min="1" max="72" required>
                                            <small class="form-text">Hours before pickup time when cancellation is free.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="cancellation_fee_percentage" class="form-label">Cancellation Fee Percentage</label>
                                            <input type="number" class="form-control" id="cancellation_fee_percentage" name="cancellation_fee_percentage" value="{{ isset($settingsCollection) && isset($settingsCollection['cancellation_fee_percentage']) ? $settingsCollection['cancellation_fee_percentage']->value : '20' }}" min="0" max="100" step="5" required>
                                            <small class="form-text">Percentage of booking amount charged for late cancellations.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="allow_guest_bookings" name="allow_guest_bookings" value="true" {{ (isset($settingsCollection) && isset($settingsCollection['allow_guest_bookings']) && $settingsCollection['allow_guest_bookings']->value == 'true') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="allow_guest_bookings">Allow Guest Bookings</label>
                                    </div>
                                    <small class="form-text">If enabled, users can start booking without logging in first.</small>
                                </div>
                            </div>

                            <!-- Extra Services Tab -->
                            <div class="tab-pane fade" id="extra-services" role="tabpanel" aria-labelledby="extra-services-tab">
                                <h5 class="mb-4">Extra Services Configuration</h5>

                                <div class="alert alert-info mb-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Configure pricing and availability for additional services that clients can add to their bookings.
                                </div>

                                <!-- Service Fees -->
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Service Fees</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="meet_and_greet_fee" class="form-label">
                                                        <i class="fas fa-handshake me-2 text-info"></i>Meet and Greet Fee
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">{{ \App\Services\SettingsService::getCurrencySymbol() }}</span>
                                                        <input type="number" class="form-control" id="meet_and_greet_fee" name="meet_and_greet_fee"
                                                               value="{{ isset($settingsCollection) && isset($settingsCollection['meet_and_greet_fee']) ? $settingsCollection['meet_and_greet_fee']->value : '15.00' }}"
                                                               min="0" max="999.99" step="0.01" required>
                                                    </div>
                                                    <small class="form-text">Fee for driver meeting client with name sign</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="child_seat_fee" class="form-label">
                                                        <i class="fas fa-baby me-2 text-warning"></i>Child Seat Fee
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">{{ \App\Services\SettingsService::getCurrencySymbol() }}</span>
                                                        <input type="number" class="form-control" id="child_seat_fee" name="child_seat_fee"
                                                               value="{{ isset($settingsCollection) && isset($settingsCollection['child_seat_fee']) ? $settingsCollection['child_seat_fee']->value : '10.00' }}"
                                                               min="0" max="999.99" step="0.01" required>
                                                    </div>
                                                    <small class="form-text">Fee for providing child safety seat</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="wheelchair_fee" class="form-label">
                                                        <i class="fas fa-wheelchair me-2 text-success"></i>Wheelchair Accessible Fee
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">{{ \App\Services\SettingsService::getCurrencySymbol() }}</span>
                                                        <input type="number" class="form-control" id="wheelchair_fee" name="wheelchair_fee"
                                                               value="{{ isset($settingsCollection) && isset($settingsCollection['wheelchair_fee']) ? $settingsCollection['wheelchair_fee']->value : '20.00' }}"
                                                               min="0" max="999.99" step="0.01" required>
                                                    </div>
                                                    <small class="form-text">Fee for wheelchair accessible vehicle</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label for="extra_luggage_fee" class="form-label">
                                                        <i class="fas fa-suitcase me-2 text-secondary"></i>Extra Luggage Fee
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">{{ \App\Services\SettingsService::getCurrencySymbol() }}</span>
                                                        <input type="number" class="form-control" id="extra_luggage_fee" name="extra_luggage_fee"
                                                               value="{{ isset($settingsCollection) && isset($settingsCollection['extra_luggage_fee']) ? $settingsCollection['extra_luggage_fee']->value : '5.00' }}"
                                                               min="0" max="999.99" step="0.01" required>
                                                    </div>
                                                    <small class="form-text">Fee for additional luggage space</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Service Availability -->
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-toggle-on me-2"></i>Service Availability</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="meet_and_greet_enabled" name="meet_and_greet_enabled" value="true"
                                                               {{ (isset($settingsCollection) && isset($settingsCollection['meet_and_greet_enabled']) && $settingsCollection['meet_and_greet_enabled']->value == 'true') ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="meet_and_greet_enabled">
                                                            <strong><i class="fas fa-handshake me-2 text-info"></i>Enable Meet and Greet</strong>
                                                        </label>
                                                    </div>
                                                    <small class="form-text">Allow clients to request meet and greet service</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="child_seat_enabled" name="child_seat_enabled" value="true"
                                                               {{ (isset($settingsCollection) && isset($settingsCollection['child_seat_enabled']) && $settingsCollection['child_seat_enabled']->value == 'true') ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="child_seat_enabled">
                                                            <strong><i class="fas fa-baby me-2 text-warning"></i>Enable Child Seat</strong>
                                                        </label>
                                                    </div>
                                                    <small class="form-text">Allow clients to request child safety seats</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="wheelchair_enabled" name="wheelchair_enabled" value="true"
                                                               {{ (isset($settingsCollection) && isset($settingsCollection['wheelchair_enabled']) && $settingsCollection['wheelchair_enabled']->value == 'true') ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="wheelchair_enabled">
                                                            <strong><i class="fas fa-wheelchair me-2 text-success"></i>Enable Wheelchair Accessible</strong>
                                                        </label>
                                                    </div>
                                                    <small class="form-text">Allow clients to request wheelchair accessible vehicles</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" type="checkbox" id="extra_luggage_enabled" name="extra_luggage_enabled" value="true"
                                                               {{ (isset($settingsCollection) && isset($settingsCollection['extra_luggage_enabled']) && $settingsCollection['extra_luggage_enabled']->value == 'true') ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="extra_luggage_enabled">
                                                            <strong><i class="fas fa-suitcase me-2 text-secondary"></i>Enable Extra Luggage</strong>
                                                        </label>
                                                    </div>
                                                    <small class="form-text">Allow clients to request additional luggage space</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Service Information -->
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Service Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-info"><i class="fas fa-handshake me-2"></i>Meet and Greet</h6>
                                                <ul class="list-unstyled small">
                                                    <li><i class="fas fa-check text-success me-1"></i> Driver meets client with name sign</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Professional greeting service</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Ideal for airport transfers</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="text-warning"><i class="fas fa-baby me-2"></i>Child Seat</h6>
                                                <ul class="list-unstyled small">
                                                    <li><i class="fas fa-check text-success me-1"></i> Certified child safety seats</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Multiple age groups supported</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Infant, toddler, and booster seats</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <h6 class="text-success"><i class="fas fa-wheelchair me-2"></i>Wheelchair Accessible</h6>
                                                <ul class="list-unstyled small">
                                                    <li><i class="fas fa-check text-success me-1"></i> Wheelchair accessible vehicles</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Ramp or lift access</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Secure wheelchair positioning</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="text-secondary"><i class="fas fa-suitcase me-2"></i>Extra Luggage</h6>
                                                <ul class="list-unstyled small">
                                                    <li><i class="fas fa-check text-success me-1"></i> Additional luggage capacity</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Perfect for long trips</li>
                                                    <li><i class="fas fa-check text-success me-1"></i> Airport transfer enhancement</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Driver Tab -->
                            <div class="tab-pane fade" id="driver" role="tabpanel" aria-labelledby="driver-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="driver_commission_percentage" class="form-label">Driver Commission Percentage</label>
                                            <input type="number" class="form-control" id="driver_commission_percentage" name="driver_commission_percentage" value="{{ isset($settingsCollection) && isset($settingsCollection['driver_commission_percentage']) ? $settingsCollection['driver_commission_percentage']->value : '80' }}" min="50" max="95" step="5" required>
                                            <small class="form-text">Percentage of the fare that goes to the driver.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="minimum_payout_amount" class="form-label">Minimum Payout Amount</label>
                                            <input type="number" class="form-control" id="minimum_payout_amount" name="minimum_payout_amount" value="{{ isset($settingsCollection) && isset($settingsCollection['minimum_payout_amount']) ? $settingsCollection['minimum_payout_amount']->value : '50' }}" min="10" max="200" step="10" required>
                                            <small class="form-text">Minimum amount required for driver payout.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="payout_schedule" class="form-label">Payout Schedule</label>
                                            <select class="form-select" id="payout_schedule" name="payout_schedule" required>
                                                <option value="weekly" {{ (isset($settingsCollection) && isset($settingsCollection['payout_schedule']) && $settingsCollection['payout_schedule']->value == 'weekly') ? 'selected' : '' }}>Weekly</option>
                                                <option value="biweekly" {{ (isset($settingsCollection) && isset($settingsCollection['payout_schedule']) && $settingsCollection['payout_schedule']->value == 'biweekly') ? 'selected' : '' }}>Bi-weekly</option>
                                                <option value="monthly" {{ (isset($settingsCollection) && isset($settingsCollection['payout_schedule']) && $settingsCollection['payout_schedule']->value == 'monthly') ? 'selected' : '' }}>Monthly</option>
                                            </select>
                                            <small class="form-text">How often drivers receive their payments.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="maximum_active_hours" class="form-label">Maximum Active Hours Per Day</label>
                                            <input type="number" class="form-control" id="maximum_active_hours" name="maximum_active_hours" value="{{ isset($settingsCollection) && isset($settingsCollection['maximum_active_hours']) ? $settingsCollection['maximum_active_hours']->value : '12' }}" min="4" max="16" required>
                                            <small class="form-text">Maximum hours a driver can be active per day.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mt-4">
                                    <label class="form-label">Driver Verification Requirements</label>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="req_license" name="driver_verification_requirements[license]" value="true" checked>
                                                <label class="form-check-label" for="req_license">Driver's License</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="req_insurance" name="driver_verification_requirements[insurance]" value="true" checked>
                                                <label class="form-check-label" for="req_insurance">Insurance</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="req_background" name="driver_verification_requirements[background_check]" value="true" checked>
                                                <label class="form-check-label" for="req_background">Background Check</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="req_vehicle" name="driver_verification_requirements[vehicle_inspection]" value="true" checked>
                                                <label class="form-check-label" for="req_vehicle">Vehicle Inspection</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Tab -->
                            <div class="tab-pane fade" id="email" role="tabpanel" aria-labelledby="email-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_driver" class="form-label">Mail Driver</label>
                                            <select class="form-select" id="mail_driver" name="mail_driver" required>
                                                <option value="smtp" {{ (isset($settingsCollection) && isset($settingsCollection['mail_driver']) && $settingsCollection['mail_driver']->value == 'smtp') ? 'selected' : '' }}>SMTP</option>
                                                <option value="sendmail" {{ (isset($settingsCollection) && isset($settingsCollection['mail_driver']) && $settingsCollection['mail_driver']->value == 'sendmail') ? 'selected' : '' }}>Sendmail</option>
                                                <option value="mailgun" {{ (isset($settingsCollection) && isset($settingsCollection['mail_driver']) && $settingsCollection['mail_driver']->value == 'mailgun') ? 'selected' : '' }}>Mailgun</option>
                                                <option value="ses" {{ (isset($settingsCollection) && isset($settingsCollection['mail_driver']) && $settingsCollection['mail_driver']->value == 'ses') ? 'selected' : '' }}>Amazon SES</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_host" class="form-label">Mail Host</label>
                                            <input type="text" class="form-control" id="mail_host" name="mail_host" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_host']) ? $settingsCollection['mail_host']->value : '' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_port" class="form-label">Mail Port</label>
                                            <input type="number" class="form-control" id="mail_port" name="mail_port" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_port']) ? $settingsCollection['mail_port']->value : '587' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_encryption" class="form-label">Mail Encryption</label>
                                            <select class="form-select" id="mail_encryption" name="mail_encryption">
                                                <option value="tls" {{ (isset($settingsCollection) && isset($settingsCollection['mail_encryption']) && $settingsCollection['mail_encryption']->value == 'tls') ? 'selected' : '' }}>TLS</option>
                                                <option value="ssl" {{ (isset($settingsCollection) && isset($settingsCollection['mail_encryption']) && $settingsCollection['mail_encryption']->value == 'ssl') ? 'selected' : '' }}>SSL</option>
                                                <option value="none" {{ (isset($settingsCollection) && isset($settingsCollection['mail_encryption']) && $settingsCollection['mail_encryption']->value == 'none') ? 'selected' : '' }}>None</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_username" class="form-label">Mail Username</label>
                                            <input type="text" class="form-control" id="mail_username" name="mail_username" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_username']) ? $settingsCollection['mail_username']->value : '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_password" class="form-label">Mail Password</label>
                                            <input type="password" class="form-control" id="mail_password" name="mail_password" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_password']) ? $settingsCollection['mail_password']->value : '' }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_from_address" class="form-label">From Address</label>
                                            <input type="email" class="form-control" id="mail_from_address" name="mail_from_address" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_from_address']) ? $settingsCollection['mail_from_address']->value : '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="mail_from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" id="mail_from_name" name="mail_from_name" value="{{ isset($settingsCollection) && isset($settingsCollection['mail_from_name']) ? $settingsCollection['mail_from_name']->value : '' }}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Appearance Tab -->
                            <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="primary_color" class="form-label">Primary Color</label>
                                            <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="{{ isset($settingsCollection) && isset($settingsCollection['primary_color']) ? $settingsCollection['primary_color']->value : '#ee393d' }}">
                                            <small class="form-text">Main color used throughout the application.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="secondary_color" class="form-label">Secondary Color</label>
                                            <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="{{ isset($settingsCollection) && isset($settingsCollection['secondary_color']) ? $settingsCollection['secondary_color']->value : '#343a40' }}">
                                            <small class="form-text">Secondary color used for accents and highlights.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="logo" class="form-label">Logo</label>
                                            <input type="file" class="form-control" id="logo" name="logo">
                                            @if(isset($settingsCollection) && isset($settingsCollection['logo']) && $settingsCollection['logo']->value)
                                                <div class="mt-2">
                                                    <img src="{{ asset('storage/' . $settingsCollection['logo']->value) }}" alt="Logo" style="max-height: 50px;">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="favicon" class="form-label">Favicon</label>
                                            <input type="file" class="form-control" id="favicon" name="favicon">
                                            @if(isset($settingsCollection) && isset($settingsCollection['favicon']) && $settingsCollection['favicon']->value)
                                                <div class="mt-2">
                                                    <img src="{{ asset('storage/' . $settingsCollection['favicon']->value) }}" alt="Favicon" style="max-height: 32px;">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="default_language" class="form-label">Default Language</label>
                                    <select class="form-select" id="default_language" name="default_language" required>
                                        <option value="en" {{ (isset($settingsCollection) && isset($settingsCollection['default_language']) && $settingsCollection['default_language']->value == 'en') ? 'selected' : '' }}>English</option>
                                        <option value="es" {{ (isset($settingsCollection) && isset($settingsCollection['default_language']) && $settingsCollection['default_language']->value == 'es') ? 'selected' : '' }}>Spanish</option>
                                        <option value="fr" {{ (isset($settingsCollection) && isset($settingsCollection['default_language']) && $settingsCollection['default_language']->value == 'fr') ? 'selected' : '' }}>French</option>
                                        <option value="de" {{ (isset($settingsCollection) && isset($settingsCollection['default_language']) && $settingsCollection['default_language']->value == 'de') ? 'selected' : '' }}>German</option>
                                        <option value="it" {{ (isset($settingsCollection) && isset($settingsCollection['default_language']) && $settingsCollection['default_language']->value == 'it') ? 'selected' : '' }}>Italian</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Content Tab -->
                            <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                                <div class="form-group">
                                    <label for="about_us" class="form-label">About Us</label>
                                    <textarea class="form-control" id="about_us" name="about_us" rows="5">{{ isset($settingsCollection) && isset($settingsCollection['about_us']) ? $settingsCollection['about_us']->value : '' }}</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="privacy_policy" class="form-label">Privacy Policy</label>
                                    <textarea class="form-control" id="privacy_policy" name="privacy_policy" rows="5">{{ isset($settingsCollection) && isset($settingsCollection['privacy_policy']) ? $settingsCollection['privacy_policy']->value : '' }}</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="terms_and_conditions" class="form-label">Terms and Conditions</label>
                                    <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" rows="5">{{ isset($settingsCollection) && isset($settingsCollection['terms_and_conditions']) ? $settingsCollection['terms_and_conditions']->value : '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Settings
                            </button>
                        </div>
                    </form>

                    <div class="mb-4">
                        <form id="clearAllCachesForm" method="POST" action="{{ route('admin.settings.clear-caches') }}">
                            @csrf
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-broom"></i> Clear All Caches (Cache, Config, Route, View)
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.tiny.cloud/1/we0aqm63gw5cnurtfsdrhln0xd7fcgwryut95km2b756tuwp/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap@1.5.3/dist/js/jsvectormap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap@1.5.3/dist/maps/world.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize TinyMCE for rich text editors
        tinymce.init({
            selector: '#about_us, #privacy_policy, #terms_and_conditions',
            plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
            toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
            height: 300,
        });

        // Initialize color pickers
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(input => {
            input.addEventListener('input', function() {
                // Update a preview element or apply the color to a preview area
                const previewEl = document.createElement('div');
                previewEl.style.backgroundColor = this.value;
                previewEl.style.width = '100%';
                previewEl.style.height = '20px';
                previewEl.style.marginTop = '5px';
                previewEl.style.borderRadius = '3px';

                // Replace existing preview or add new one
                const existingPreview = this.parentNode.querySelector('.color-preview');
                if (existingPreview) {
                    existingPreview.remove();
                }

                previewEl.className = 'color-preview';
                this.parentNode.appendChild(previewEl);
            });

            // Trigger the input event to create the initial preview
            input.dispatchEvent(new Event('input'));
        });

        // Handle file uploads for logo and favicon
        document.getElementById('logo')?.addEventListener('change', function(e) {
            previewImage(this, 'logo-preview', 50);
        });

        document.getElementById('favicon')?.addEventListener('change', function(e) {
            previewImage(this, 'favicon-preview', 32);
        });

        // Function to preview uploaded images
        function previewImage(input, previewId, maxHeight) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    let preview = document.getElementById(previewId);
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.id = previewId;
                        preview.style.maxHeight = maxHeight + 'px';
                        preview.style.marginTop = '10px';
                        input.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Handle Autocomplete settings
        const autocompleteRestrictCountryCheckbox = document.getElementById('autocomplete_restrict_country');
        const autocompleteCountrySelect = document.getElementById('autocomplete_country');

        if (autocompleteRestrictCountryCheckbox && autocompleteCountrySelect) {
            autocompleteRestrictCountryCheckbox.addEventListener('change', function() {
                autocompleteCountrySelect.disabled = !this.checked;
            });

            // Sync button to copy the main country code to Autocomplete country code
            const syncAutocompleteCountryBtn = document.getElementById('sync-autocomplete-country-btn');
            if (syncAutocompleteCountryBtn) {
                syncAutocompleteCountryBtn.addEventListener('click', function() {
                    const mainCountryCode = document.getElementById('country_code').value;
                    if (mainCountryCode) {
                        // Check if the option exists
                        const option = autocompleteCountrySelect.querySelector(`option[value="${mainCountryCode}"]`);
                        if (option) {
                            autocompleteCountrySelect.value = mainCountryCode;

                            // Make sure the restriction is enabled
                            if (!autocompleteRestrictCountryCheckbox.checked) {
                                autocompleteRestrictCountryCheckbox.checked = true;
                                autocompleteCountrySelect.disabled = false;
                            }

                            // Show a temporary success message
                            const successMsg = document.createElement('div');
                            successMsg.className = 'text-success mt-1';
                            successMsg.innerHTML = '<small><i class="fas fa-check-circle me-1"></i> Country synced successfully!</small>';
                            syncAutocompleteCountryBtn.parentNode.appendChild(successMsg);

                            // Remove the message after 3 seconds
                            setTimeout(() => {
                                successMsg.remove();
                            }, 3000);
                        }
                    }
                });
            }
        }

        // Handle Google Maps country restriction toggle
        const restrictCountryCheckbox = document.getElementById('google_maps_restrict_country');
        const countryCodeSelect = document.getElementById('google_maps_country_code');

        if (restrictCountryCheckbox && countryCodeSelect) {
            restrictCountryCheckbox.addEventListener('change', function() {
                countryCodeSelect.disabled = !this.checked;

                // If enabling restriction but no country is selected, default to the main country code
                if (this.checked && !countryCodeSelect.value) {
                    const mainCountryCode = document.getElementById('country_code').value;
                    if (mainCountryCode) {
                        // Check if the option exists
                        const option = countryCodeSelect.querySelector(`option[value="${mainCountryCode}"]`);
                        if (option) {
                            countryCodeSelect.value = mainCountryCode;
                        }
                    }
                }
            });

            // Sync button to copy the main country code to Google Maps country code
            const syncCountryBtn = document.getElementById('sync-country-btn');
            if (syncCountryBtn) {
                syncCountryBtn.addEventListener('click', function() {
                    const mainCountryCode = document.getElementById('country_code').value;
                    if (mainCountryCode) {
                        // Check if the option exists
                        const option = countryCodeSelect.querySelector(`option[value="${mainCountryCode}"]`);
                        if (option) {
                            countryCodeSelect.value = mainCountryCode;

                            // Make sure the restriction is enabled
                            if (!restrictCountryCheckbox.checked) {
                                restrictCountryCheckbox.checked = true;
                                countryCodeSelect.disabled = false;
                            }

                            // Show a temporary success message
                            const successMsg = document.createElement('div');
                            successMsg.className = 'text-success mt-1';
                            successMsg.innerHTML = '<small><i class="fas fa-check-circle me-1"></i> Country synced successfully!</small>';
                            syncCountryBtn.parentNode.appendChild(successMsg);

                            // Remove the message after 3 seconds
                            setTimeout(() => {
                                successMsg.remove();
                            }, 3000);
                        }
                    }
                });
            }
        }

        // Initialize country map
        let countryMap;
        let selectedCountryCode = document.getElementById('country_code').value;

        // Country code to country name mapping
        const countryCodeMap = {
            'US': 'United States',
            'GB': 'United Kingdom',
            'CA': 'Canada',
            'AU': 'Australia',
            'DE': 'Germany',
            'FR': 'France',
            'IT': 'Italy',
            'ES': 'Spain',
            'IN': 'India',
            'AE': 'United Arab Emirates',
            'SA': 'Saudi Arabia'
        };

        // Country name to country code mapping (reverse of countryCodeMap)
        const countryNameMap = {};
        for (const [code, name] of Object.entries(countryCodeMap)) {
            countryNameMap[name] = code;
        }

        // Initialize the map when the localization tab is shown
        document.getElementById('localization-tab').addEventListener('shown.bs.tab', function() {
            if (!countryMap) {
                initCountryMap();
            }
        });

        // Initialize the country map
        function initCountryMap() {
            countryMap = new jsVectorMap({
                selector: '#country-map',
                map: 'world',
                zoomButtons: true,
                zoomOnScroll: true,
                panOnDrag: true,
                backgroundColor: '#f8f9fa',
                selectedMarkers: [],
                markersSelectable: false,
                markers: [],
                selectedRegions: [selectedCountryCode],
                regionsSelectable: true,
                onRegionSelected: function(code, isSelected, selectedRegions) {
                    if (isSelected) {
                        // Get the country name from the map
                        const countryName = this.mapData.paths[code].name;

                        // Update the country code dropdown
                        const countryCode = code.toUpperCase();
                        if (document.querySelector(`#country_code option[value="${countryCode}"]`)) {
                            document.getElementById('country_code').value = countryCode;
                            document.getElementById('selected_country_name').value = countryName;
                        } else {
                            // If the country is not in our dropdown, show an alert
                            alert(`${countryName} (${countryCode}) is not available in the dropdown. Please select another country.`);
                            // Revert the selection
                            this.setSelectedRegions([selectedCountryCode]);
                        }
                    }
                }
            });
        }

        // Update the map when the country code dropdown changes
        document.getElementById('country_code').addEventListener('change', function() {
            selectedCountryCode = this.value;
            document.getElementById('selected_country_name').value = countryCodeMap[selectedCountryCode] || '';

            if (countryMap) {
                countryMap.setSelectedRegions([selectedCountryCode]);
            }
        });

        // Handle tab navigation
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('tab');

        if (activeTab) {
            const tab = document.querySelector(`#${activeTab}-tab`);
            if (tab) {
                const tabTrigger = new bootstrap.Tab(tab);
                tabTrigger.show();

                // Initialize the map if the active tab is localization
                if (activeTab === 'localization' && !countryMap) {
                    setTimeout(initCountryMap, 100);
                }
            }
        }

        // Update URL when tab changes
        const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', function (event) {
                const id = event.target.id.replace('-tab', '');
                const url = new URL(window.location);
                url.searchParams.set('tab', id);
                window.history.replaceState({}, '', url);
            });
        });

        // Google Maps API Key Testing
        const testApiKeyBtn = document.getElementById('test-api-key');
        if (testApiKeyBtn) {
            testApiKeyBtn.addEventListener('click', function() {
                const apiKey = document.getElementById('google_maps_api_key').value;
                const statusSpan = document.getElementById('api-key-status');

                if (!apiKey) {
                    statusSpan.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle"></i> Please enter an API key first</span>';
                    return;
                }

                // Show loading state
                testApiKeyBtn.disabled = true;
                testApiKeyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
                statusSpan.innerHTML = '<span class="text-info"><i class="fas fa-clock"></i> Testing API key...</span>';

                // Test the API key by making a simple geocoding request
                fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=London&key=${apiKey}`)
                    .then(response => response.json())
                    .then(data => {
                        testApiKeyBtn.disabled = false;
                        testApiKeyBtn.innerHTML = '<i class="fas fa-check-circle"></i> Test API Key';

                        if (data.status === 'OK') {
                            statusSpan.innerHTML = '<span class="text-success"><i class="fas fa-check-circle"></i> API key is working correctly!</span>';
                        } else if (data.status === 'REQUEST_DENIED') {
                            statusSpan.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle"></i> API key denied. Check your key and API restrictions.</span>';
                        } else if (data.status === 'OVER_QUERY_LIMIT') {
                            statusSpan.innerHTML = '<span class="text-warning"><i class="fas fa-exclamation-triangle"></i> API quota exceeded. Check your billing settings.</span>';
                        } else {
                            statusSpan.innerHTML = `<span class="text-danger"><i class="fas fa-times-circle"></i> API error: ${data.status}</span>`;
                        }
                    })
                    .catch(error => {
                        testApiKeyBtn.disabled = false;
                        testApiKeyBtn.innerHTML = '<i class="fas fa-check-circle"></i> Test API Key';
                        statusSpan.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle"></i> Network error. Please try again.</span>';
                        console.error('API key test error:', error);
                    });
            });
        }

        document.getElementById('clearAllCachesForm')?.addEventListener('submit', function(e) {
            if(!confirm('Are you sure you want to clear all caches?')) {
                e.preventDefault();
            }
        });
    });
</script>
@endsection