@extends('layouts.admin')

@section('title', 'Create Email Template')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📄 Create Email Template</h1>
            <p class="text-muted">Create a new email template for automated communications</p>
        </div>
        <div>
            <a href="{{ route('admin.email.templates.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Templates
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Template Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Template Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.email.templates.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Template Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select Type</option>
                                        <option value="booking" {{ old('type') == 'booking' ? 'selected' : '' }}>Booking</option>
                                        <option value="payment" {{ old('type') == 'payment' ? 'selected' : '' }}>Payment</option>
                                        <option value="notification" {{ old('type') == 'notification' ? 'selected' : '' }}>Notification</option>
                                        <option value="marketing" {{ old('type') == 'marketing' ? 'selected' : '' }}>Marketing</option>
                                        <option value="system" {{ old('type') == 'system' ? 'selected' : '' }}>System</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="client" {{ old('category') == 'client' ? 'selected' : '' }}>Client</option>
                                        <option value="driver" {{ old('category') == 'driver' ? 'selected' : '' }}>Driver</option>
                                        <option value="admin" {{ old('category') == 'admin' ? 'selected' : '' }}>Admin</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                   id="subject" name="subject" value="{{ old('subject') }}" required
                                   placeholder="e.g., Booking Confirmation - #{booking_number}">
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="2" 
                                      placeholder="Brief description of when this template is used">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Email Content <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="12" required 
                                      placeholder="Enter your email content here. Use variables like {client_name}, {booking_number}, etc.">{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="variables" class="form-label">Available Variables</label>
                            <div id="variablesContainer">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" name="variables[]" placeholder="Variable name (e.g., client_name)">
                                    <button type="button" class="btn btn-outline-secondary" onclick="addVariable()">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                Add variables that can be used in the template content. Use format: {variable_name}
                            </small>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active Template
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Create Template
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewTemplate()">
                                <i class="fas fa-eye me-1"></i> Preview
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Variable Helper -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">💡 Variable Helper</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Common Variables:</h6>
                    <div class="mb-3">
                        <h6 class="small text-muted">User Variables:</h6>
                        <div class="d-flex flex-wrap gap-1 mb-2">
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('client_name')">{client_name}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('driver_name')">{driver_name}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('user_email')">{user_email}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="small text-muted">Booking Variables:</h6>
                        <div class="d-flex flex-wrap gap-1 mb-2">
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('booking_number')">{booking_number}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('pickup_date')">{pickup_date}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('pickup_address')">{pickup_address}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('dropoff_address')">{dropoff_address}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('amount')">{amount}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="small text-muted">Company Variables:</h6>
                        <div class="d-flex flex-wrap gap-1 mb-2">
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_name')">{company_name}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_email')">{company_email}</span>
                            <span class="badge bg-light text-dark border variable-badge" onclick="insertVariable('company_phone')">{company_phone}</span>
                        </div>
                    </div>

                    <small class="text-muted">Click on any variable to insert it into the content.</small>
                </div>
            </div>

            <!-- Template Tips -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">📝 Template Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="small mb-0">
                        <li><strong>Variables:</strong> Use {variable_name} format in content</li>
                        <li><strong>Subject:</strong> Can also contain variables</li>
                        <li><strong>Line Breaks:</strong> Use double line breaks for paragraphs</li>
                        <li><strong>HTML:</strong> Basic HTML tags are supported</li>
                        <li><strong>Testing:</strong> Use preview to test with sample data</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addVariable() {
    const container = document.getElementById('variablesContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="variables[]" placeholder="Variable name">
        <button type="button" class="btn btn-outline-danger" onclick="removeVariable(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeVariable(button) {
    button.closest('.input-group').remove();
}

function insertVariable(variable) {
    const content = document.getElementById('content');
    const cursorPos = content.selectionStart;
    const textBefore = content.value.substring(0, cursorPos);
    const textAfter = content.value.substring(cursorPos);
    
    content.value = textBefore + '{' + variable + '}' + textAfter;
    content.focus();
    content.setSelectionRange(cursorPos + variable.length + 2, cursorPos + variable.length + 2);
}

function previewTemplate() {
    const formData = {
        name: $('#name').val(),
        subject: $('#subject').val(),
        content: $('#content').val(),
        type: $('#type').val(),
        category: $('#category').val()
    };

    if (!formData.subject || !formData.content) {
        alert('Please fill in the subject and content fields to preview.');
        return;
    }

    // Create preview content with sample data
    let previewSubject = formData.subject;
    let previewContent = formData.content;
    
    // Sample data for preview
    const sampleData = {
        client_name: 'John Doe',
        driver_name: 'Mike Johnson',
        booking_number: 'YNR20240101001',
        pickup_date: '2024-01-15 10:00 AM',
        pickup_address: '123 Main Street, London',
        dropoff_address: 'Heathrow Airport',
        amount: '£45.00',
        company_name: 'YNR Cars',
        company_email: '<EMAIL>',
        company_phone: '+44 ************'
    };

    // Replace variables with sample data
    Object.keys(sampleData).forEach(key => {
        const regex = new RegExp(`{${key}}`, 'g');
        previewSubject = previewSubject.replace(regex, sampleData[key]);
        previewContent = previewContent.replace(regex, sampleData[key]);
    });

    // Format content for display
    previewContent = previewContent.replace(/\n/g, '<br>');

    const previewHtml = `
        <div class="border rounded p-3" style="background-color: #f8f9fa;">
            <h6 class="text-primary">Subject:</h6>
            <p class="fw-bold">${previewSubject}</p>
            
            <h6 class="text-primary">Content:</h6>
            <div class="border rounded p-3 bg-white">
                ${previewContent}
            </div>
            
            <small class="text-muted mt-2 d-block">
                <strong>Note:</strong> This preview uses sample data. Actual emails will use real data.
            </small>
        </div>
    `;

    $('#previewContent').html(previewHtml);
    $('#previewModal').modal('show');
}

// Add click cursor pointer to variable badges
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = '.variable-badge { cursor: pointer; } .variable-badge:hover { background-color: #e9ecef !important; }';
    document.head.appendChild(style);
});
</script>
@endpush
