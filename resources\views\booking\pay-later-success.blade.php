@extends('layouts.guest')

@section('title', 'Pay Later Confirmed')

@section('styles')
<style>
    .success-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .success-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .success-card .card-header {
        background-color: #000;
        color: #fff;
        border-radius: 10px 10px 0 0;
        padding: 20px;
    }

    .success-card .card-body {
        padding: 30px;
    }

    .success-icon {
        font-size: 5rem;
        color: #28a745;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .payment-details {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .payment-details h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 500;
        color: #6c757d;
    }

    .detail-value {
        font-weight: 600;
        color: #495057;
    }

    .amount-highlight {
        font-size: 1.2em;
        color: #28a745;
        font-weight: 700;
    }

    .extra-services-list {
        text-align: left;
    }

    .service-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        font-size: 0.9em;
    }

    .service-item:last-child {
        margin-bottom: 0;
    }

    .service-fee {
        margin-left: auto;
        font-weight: 600;
        color: #28a745;
    }

    .btn-custom {
        background-color: #000;
        border-color: #000;
        color: #fff;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s;
    }

    .btn-custom:hover {
        background-color: #333;
        border-color: #333;
        color: #fff;
        transform: translateY(-2px);
    }

    .btn-outline-custom {
        background-color: transparent;
        border-color: #000;
        color: #000;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s;
    }

    .btn-outline-custom:hover {
        background-color: #000;
        border-color: #000;
        color: #fff;
        transform: translateY(-2px);
    }

    .alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
        border-radius: 10px;
        padding: 15px;
        margin: 20px 0;
    }

    .alert-info .fas {
        margin-right: 10px;
    }
</style>
@endsection

@section('content')
<div class="success-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="success-card card">
                    <div class="card-header text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            Pay Later Confirmed!
                        </h2>
                    </div>
                    <div class="card-body text-center">
                        <div class="success-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        
                        <h3 class="mb-3">Your booking has been confirmed!</h3>
                        <p class="lead mb-4">You have selected to pay later. Payment will be collected at the time of service.</p>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Important:</strong> Please have the exact amount ready for payment at the time of service.
                        </div>

                        <div class="payment-details">
                            <h5><i class="fas fa-receipt me-2"></i>Booking Details</h5>
                            
                            <div class="detail-row">
                                <span class="detail-label">Booking Number:</span>
                                <span class="detail-value">{{ $booking->booking_number }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Payment Method:</span>
                                <span class="detail-value">
                                    <i class="fas fa-clock me-1"></i>Pay Later
                                </span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Amount to Pay:</span>
                                <span class="detail-value amount-highlight">{{ $currencySymbol }}{{ number_format($booking->amount, 2) }}</span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value">
                                    <span class="badge bg-warning text-dark">Payment Pending</span>
                                </span>
                            </div>
                            
                            <div class="detail-row">
                                <span class="detail-label">Service Date:</span>
                                <span class="detail-value">{{ $booking->pickup_datetime ? $booking->pickup_datetime->format('M d, Y H:i') : 'N/A' }}</span>
                            </div>

                            <!-- Extra Services -->
                            @php
                                $hasExtraServices = $booking->meet_and_greet || $booking->child_seat || $booking->wheelchair_accessible || $booking->extra_luggage;
                                $extraServicesSettings = \App\Services\SettingsService::getExtraServicesSettings();
                            @endphp

                            @if($hasExtraServices)
                                <div class="detail-row">
                                    <span class="detail-label">Extra Services:</span>
                                    <span class="detail-value">
                                        <div class="extra-services-list">
                                            @if($booking->meet_and_greet)
                                                <div class="service-item">
                                                    <i class="fas fa-handshake me-1 text-info"></i>
                                                    Meet & Greet Service
                                                    <span class="service-fee">({{ $currencySymbol }}{{ number_format($extraServicesSettings['meet_and_greet']['fee'], 2) }})</span>
                                                </div>
                                            @endif

                                            @if($booking->child_seat)
                                                <div class="service-item">
                                                    <i class="fas fa-baby me-1 text-warning"></i>
                                                    Child Seat
                                                    <span class="service-fee">({{ $currencySymbol }}{{ number_format($extraServicesSettings['child_seat']['fee'], 2) }})</span>
                                                </div>
                                            @endif

                                            @if($booking->wheelchair_accessible)
                                                <div class="service-item">
                                                    <i class="fas fa-wheelchair me-1 text-success"></i>
                                                    Wheelchair Accessible
                                                    @if($extraServicesSettings['wheelchair_accessible']['fee'] > 0)
                                                        <span class="service-fee">({{ $currencySymbol }}{{ number_format($extraServicesSettings['wheelchair_accessible']['fee'], 2) }})</span>
                                                    @else
                                                        <span class="badge bg-success ms-1">Free</span>
                                                    @endif
                                                </div>
                                            @endif

                                            @if($booking->extra_luggage)
                                                <div class="service-item">
                                                    <i class="fas fa-suitcase me-1 text-secondary"></i>
                                                    Extra Luggage Space
                                                    <span class="service-fee">({{ $currencySymbol }}{{ number_format($extraServicesSettings['extra_luggage']['fee'], 2) }})</span>
                                                </div>
                                            @endif
                                        </div>
                                    </span>
                                </div>
                            @endif
                        </div>

                        <div class="mt-4">
                            <a href="{{ route('booking.confirmation', $booking->id) }}" class="btn btn-custom me-3">
                                <i class="fas fa-eye me-2"></i>View Booking Details
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-custom">
                                <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                            </a>
                        </div>

                        <div class="mt-4">
                            <p class="text-muted">
                                <i class="fas fa-envelope me-2"></i>
                                A confirmation email has been sent to your registered email address.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
