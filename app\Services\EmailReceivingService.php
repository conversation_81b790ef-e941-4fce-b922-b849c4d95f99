<?php

namespace App\Services;

use App\Models\ContactSubmission;

use App\Models\EmailLog;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Carbon\Carbon;

class EmailReceivingService
{
    /**
     * Process contact form submission with enhanced validation
     *
     * @param array $contactData
     * @return array
     */
    public static function processContactForm(array $contactData): array
    {
        try {
            // Enhanced validation
            $validator = Validator::make($contactData, [
                'name' => 'required|string|max:255|min:2',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'subject' => 'required|string|max:255|min:5',
                'message' => 'required|string|max:5000|min:10',
                'company' => 'nullable|string|max:255',
                'preferred_contact' => 'nullable|in:email,phone,both',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->toArray(),
                    'message' => 'Validation failed'
                ];
            }

            // Check for spam patterns
            $spamCheck = self::checkForSpam($contactData);
            if ($spamCheck['is_spam']) {
                Log::warning('Potential spam contact form submission', [
                    'email' => $contactData['email'],
                    'reason' => $spamCheck['reason'],
                    'data' => $contactData
                ]);
                
                // Still save but mark as potential spam
                $contactData['is_spam'] = true;
            }

            // Save to database
            $submission = ContactSubmission::create([
                'name' => $contactData['name'],
                'email' => $contactData['email'],
                'phone' => $contactData['phone'] ?? null,
                'subject' => $contactData['subject'],
                'message' => $contactData['message'],
                'company' => $contactData['company'] ?? null,
                'preferred_contact' => $contactData['preferred_contact'] ?? 'email',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'is_spam' => $contactData['is_spam'] ?? false,
                'status' => 'new',
                'submitted_at' => now(),
            ]);

            // Send emails if not spam
            if (!($contactData['is_spam'] ?? false)) {
                $emailSent = EmailService::sendContactFormEmails($contactData);
                
                if ($emailSent) {
                    $submission->update(['status' => 'emailed']);
                } else {
                    $submission->update(['status' => 'email_failed']);
                }
            } else {
                $submission->update(['status' => 'spam_detected']);
            }

            // Log the submission
            self::logEmailReceived('contact_form', $contactData, $submission->id);

            return [
                'success' => true,
                'submission_id' => $submission->id,
                'message' => 'Contact form processed successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Contact form processing failed', [
                'error' => $e->getMessage(),
                'data' => $contactData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process contact form: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Check for spam patterns in submitted data
     *
     * @param array $data
     * @return array
     */
    private static function checkForSpam(array $data): array
    {
        $spamIndicators = [];
        
        // Check for common spam patterns
        $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
        $message = strtolower($data['message'] ?? '');
        
        foreach ($spamKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $spamIndicators[] = "Contains spam keyword: {$keyword}";
            }
        }
        
        // Check for excessive links
        $linkCount = preg_match_all('/https?:\/\//', $message);
        if ($linkCount > 3) {
            $spamIndicators[] = "Too many links ({$linkCount})";
        }
        
        // Check for suspicious email patterns
        $email = $data['email'] ?? '';
        if (preg_match('/\d{5,}@/', $email)) {
            $spamIndicators[] = "Suspicious email pattern";
        }
        
        // Check message length vs content quality
        $messageLength = strlen($message);
        $wordCount = str_word_count($message);
        if ($messageLength > 100 && $wordCount < 10) {
            $spamIndicators[] = "Low content quality";
        }

        return [
            'is_spam' => !empty($spamIndicators),
            'reason' => implode(', ', $spamIndicators),
            'indicators' => $spamIndicators
        ];
    }





    /**
     * Process email reply to existing conversation
     *
     * @param array $emailData
     * @return array
     */
    public static function processEmailReply(array $emailData): array
    {
        try {
            // Extract reply information
            $fromEmail = $emailData['from'] ?? null;
            $subject = $emailData['subject'] ?? '';
            $content = $emailData['text'] ?? $emailData['html'] ?? '';

            if (!$fromEmail || !$content) {
                return [
                    'success' => false,
                    'message' => 'Invalid email data - missing from or content'
                ];
            }

            // Try to find related conversation
            $relatedSubmission = self::findRelatedConversation($fromEmail, $subject);

            // Log the email reply
            $emailLog = EmailLog::create([
                'type' => 'email_reply',
                'direction' => 'received',
                'from_email' => $fromEmail,
                'to_email' => SettingsService::getCompanyEmail(),
                'subject' => $subject,
                'content' => $content,
                'status' => 'processed',
                'related_type' => $relatedSubmission ? get_class($relatedSubmission) : null,
                'related_id' => $relatedSubmission ? $relatedSubmission->id : null,
                'metadata' => json_encode([
                    'timestamp' => $emailData['timestamp'] ?? now()->toISOString(),
                    'provider' => $emailData['provider'] ?? 'unknown',
                ]),
                'processed_at' => now(),
            ]);

            // Update related submission status if found
            if ($relatedSubmission) {
                $relatedSubmission->update([
                    'status' => 'responded',
                    'responded_at' => now(),
                ]);
            }

            // Notify admin of reply
            self::notifyAdminOfReply($emailData, $relatedSubmission);

            return [
                'success' => true,
                'email_log_id' => $emailLog->id,
                'related_submission' => $relatedSubmission ? $relatedSubmission->id : null,
                'message' => 'Email reply processed successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Email reply processing failed', [
                'error' => $e->getMessage(),
                'data' => $emailData
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process email reply: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Find related conversation based on email and subject
     *
     * @param string $email
     * @param string $subject
     * @return ContactSubmission|null
     */
    private static function findRelatedConversation(string $email, string $subject)
    {
        // Try to find contact submission
        $contactSubmission = ContactSubmission::where('email', $email)
            ->where('status', '!=', 'closed')
            ->orderBy('submitted_at', 'desc')
            ->first();

        return $contactSubmission;
    }

    /**
     * Notify admin of email reply
     *
     * @param array $emailData
     * @param mixed $relatedSubmission
     * @return void
     */
    private static function notifyAdminOfReply(array $emailData, $relatedSubmission = null): void
    {
        try {
            $adminEmails = User::where('role', 'admin')->pluck('email')->toArray();

            foreach ($adminEmails as $email) {
                Mail::to($email)->send(new \App\Mail\EmailReplyNotificationMail($emailData, $relatedSubmission));
            }

            Log::info('Email reply notification sent to admins', [
                'from_email' => $emailData['from'] ?? null,
                'admin_count' => count($adminEmails),
                'related_submission_id' => $relatedSubmission ? $relatedSubmission->id : null,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send email reply notification', [
                'error' => $e->getMessage(),
                'email_data' => $emailData
            ]);
        }
    }

    /**
     * Log email received for tracking
     *
     * @param string $type
     * @param array $data
     * @param int|null $relatedId
     * @return void
     */
    private static function logEmailReceived(string $type, array $data, ?int $relatedId = null): void
    {
        try {
            EmailLog::create([
                'type' => $type,
                'direction' => 'received',
                'from_email' => $data['email'] ?? null,
                'to_email' => SettingsService::getCompanyEmail(),
                'subject' => $data['subject'] ?? "New {$type}",
                'status' => 'processed',
                'related_type' => $type,
                'related_id' => $relatedId,
                'metadata' => json_encode([
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'timestamp' => now()->toISOString(),
                ]),
                'processed_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log email received', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }
    }
}
