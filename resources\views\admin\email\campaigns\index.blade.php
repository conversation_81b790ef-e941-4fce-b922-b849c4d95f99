@extends('layouts.admin')

@section('title', 'Email Campaigns')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📢 Email Campaigns</h1>
            <p class="text-muted">Create and manage bulk email campaigns</p>
        </div>
        <div>
            <a href="{{ route('admin.email.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Email
            </a>
            <a href="{{ route('admin.email.campaigns.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> New Campaign
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.email.campaigns.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                {{ ucfirst($status) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="recipient_type" class="form-label">Recipient Type</label>
                    <select class="form-select" id="recipient_type" name="recipient_type">
                        <option value="">All Recipients</option>
                        @foreach($recipientTypes as $type)
                            <option value="{{ $type }}" {{ request('recipient_type') == $type ? 'selected' : '' }}>
                                {{ ucfirst($type) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Search campaigns...">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <a href="{{ route('admin.email.campaigns.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Campaigns List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Email Campaigns ({{ $campaigns->total() }})</h6>
        </div>
        <div class="card-body">
            @if($campaigns->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Campaign</th>
                                <th>Recipients</th>
                                <th>Status</th>
                                <th>Progress</th>
                                <th>Performance</th>
                                <th>Scheduled</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($campaigns as $campaign)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <div class="fw-bold">{{ $campaign->name }}</div>
                                                <div class="text-muted small">{{ Str::limit($campaign->subject, 40) }}</div>
                                                @if($campaign->template)
                                                    <div class="text-muted small">
                                                        <i class="fas fa-file-alt me-1"></i>{{ $campaign->template->name }}
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <div class="fw-bold">{{ number_format($campaign->total_recipients) }}</div>
                                            <div class="small text-muted">{{ ucfirst($campaign->recipient_type) }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge {{ $campaign->status_badge_class }}">
                                            {{ ucfirst($campaign->status) }}
                                        </span>
                                        @if($campaign->creator)
                                            <div class="small text-muted">by {{ $campaign->creator->name }}</div>
                                        @endif
                                    </td>
                                    <td>
                                        @if($campaign->total_recipients > 0)
                                            <div class="progress mb-1" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: {{ ($campaign->sent_count / $campaign->total_recipients) * 100 }}%">
                                                    {{ $campaign->sent_count }}
                                                </div>
                                            </div>
                                            <div class="small text-muted text-center">
                                                {{ $campaign->sent_count }} / {{ $campaign->total_recipients }} sent
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($campaign->sent_count > 0)
                                            <div class="small">
                                                <div>📧 {{ number_format($campaign->delivery_rate, 1) }}% delivered</div>
                                                <div>👁️ {{ number_format($campaign->open_rate, 1) }}% opened</div>
                                                <div>🖱️ {{ number_format($campaign->click_rate, 1) }}% clicked</div>
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($campaign->scheduled_at)
                                            <div class="small">
                                                <div>{{ $campaign->scheduled_at->format('M j, Y') }}</div>
                                                <div class="text-muted">{{ $campaign->scheduled_at->format('g:i A') }}</div>
                                            </div>
                                        @else
                                            <span class="text-muted">Not scheduled</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.email.campaigns.show', $campaign) }}" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            @if($campaign->canBeEdited())
                                                <a href="{{ route('admin.email.campaigns.edit', $campaign) }}" 
                                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif

                                            @if($campaign->canBeSent())
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="sendCampaign({{ $campaign->id }})" title="Send Now">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            @endif

                                            @if($campaign->canBeCancelled())
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="cancelCampaign({{ $campaign->id }})" title="Cancel">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                            @endif

                                            @if($campaign->failed_count > 0)
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="retryFailed({{ $campaign->id }})" title="Retry Failed">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                            @endif

                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown" title="More">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ route('admin.email.campaigns.preview', $campaign) }}">
                                                            <i class="fas fa-search me-2"></i>Preview
                                                        </a>
                                                    </li>
                                                    @if($campaign->canBeEdited())
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" 
                                                               onclick="deleteCampaign({{ $campaign->id }})">
                                                                <i class="fas fa-trash me-2"></i>Delete
                                                            </a>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing {{ $campaigns->firstItem() }} to {{ $campaigns->lastItem() }} of {{ $campaigns->total() }} results
                    </div>
                    {{ $campaigns->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Email Campaigns Found</h5>
                    <p class="text-muted">Create your first email campaign to reach your users.</p>
                    <a href="{{ route('admin.email.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create Campaign
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Confirmation Modals -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                <!-- Content will be set by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModalAction">Confirm</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentAction = null;
let currentCampaignId = null;

function sendCampaign(campaignId) {
    currentAction = 'send';
    currentCampaignId = campaignId;
    
    $('#confirmModalTitle').text('Send Campaign');
    $('#confirmModalBody').html('Are you sure you want to send this campaign now? This action cannot be undone.');
    $('#confirmModalAction').removeClass().addClass('btn btn-success').text('Send Campaign');
    $('#confirmModal').modal('show');
}

function cancelCampaign(campaignId) {
    currentAction = 'cancel';
    currentCampaignId = campaignId;
    
    $('#confirmModalTitle').text('Cancel Campaign');
    $('#confirmModalBody').html('Are you sure you want to cancel this campaign? Pending emails will not be sent.');
    $('#confirmModalAction').removeClass().addClass('btn btn-danger').text('Cancel Campaign');
    $('#confirmModal').modal('show');
}

function retryFailed(campaignId) {
    currentAction = 'retry';
    currentCampaignId = campaignId;
    
    $('#confirmModalTitle').text('Retry Failed Emails');
    $('#confirmModalBody').html('Are you sure you want to retry sending failed emails for this campaign?');
    $('#confirmModalAction').removeClass().addClass('btn btn-warning').text('Retry Failed');
    $('#confirmModal').modal('show');
}

function deleteCampaign(campaignId) {
    currentAction = 'delete';
    currentCampaignId = campaignId;
    
    $('#confirmModalTitle').text('Delete Campaign');
    $('#confirmModalBody').html('Are you sure you want to delete this campaign? This action cannot be undone.');
    $('#confirmModalAction').removeClass().addClass('btn btn-danger').text('Delete Campaign');
    $('#confirmModal').modal('show');
}

$('#confirmModalAction').click(function() {
    if (!currentAction || !currentCampaignId) return;
    
    let url, method = 'POST';
    
    switch(currentAction) {
        case 'send':
            url = `/admin/email/campaigns/${currentCampaignId}/send`;
            break;
        case 'cancel':
            url = `/admin/email/campaigns/${currentCampaignId}/cancel`;
            break;
        case 'retry':
            url = `/admin/email/campaigns/${currentCampaignId}/retry-failed`;
            break;
        case 'delete':
            url = `/admin/email/campaigns/${currentCampaignId}`;
            method = 'DELETE';
            break;
    }
    
    $.ajax({
        url: url,
        method: method,
        data: {
            _token: '{{ csrf_token() }}',
            ...(method === 'DELETE' && { _method: 'DELETE' })
        },
        success: function(response) {
            $('#confirmModal').modal('hide');
            if (response.success) {
                if (currentAction === 'delete') {
                    location.reload();
                } else {
                    // Show success message and reload
                    alert(response.message);
                    location.reload();
                }
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr) {
            $('#confirmModal').modal('hide');
            const response = xhr.responseJSON;
            alert('Error: ' + (response?.message || 'An error occurred'));
        }
    });
});

// Auto-refresh for campaigns in progress
setInterval(function() {
    if ($('tbody tr').length > 0) {
        // Check if any campaigns are in sending status
        const sendingCampaigns = $('tbody tr').filter(function() {
            return $(this).find('.badge').text().trim().toLowerCase() === 'sending';
        });
        
        if (sendingCampaigns.length > 0) {
            // Refresh the page to update progress
            location.reload();
        }
    }
}, 30000); // Refresh every 30 seconds
</script>
@endpush
